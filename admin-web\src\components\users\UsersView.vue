<script setup>
import { ref} from 'vue'
import Button from 'primevue/button'
import ConfirmDialog from 'primevue/confirmdialog'
import { useConfirm } from 'primevue/useconfirm'
import UsersTable from './UsersTable.vue'

import SpeedDial from 'primevue/speeddial';

const usersTableRef = ref(null);

function handleCreateUserClick() {
  usersTableRef.value?.openCreateUserDrawer();
}

function showFiltersClick() {
  usersTableRef.value?.openFilterDrawer();
}
</script>

<template>
  <div class="users-view">
    <UsersTable ref="usersTableRef" />
    

    <ConfirmDialog />

    <SpeedDial direction="up" :style="{ position: 'absolute', right: '1.5rem', bottom: '1.2rem' }"
      :tooltipOptions="{ position: 'left' }" @click='handleCreateUserClick'></SpeedDial>

    <SpeedDial direction="up" :style="{ position: 'absolute', right: '5.5rem', bottom: '1.2rem' }"
      :tooltipOptions="{ position: 'left' }" @click="showFiltersClick">
    
      <template #button="{ toggleCallback }">
        <Button icon="pi pi-filter" @click="toggleCallback" rounded raised />
    </template>
    </SpeedDial>
  </div>
</template>

<style scoped>
.users-view {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.p-datatable-header-cell {
  font-size: 12px;
}
</style>