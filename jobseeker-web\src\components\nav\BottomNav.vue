<script setup>
import { ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router'
import Toolbar from 'primevue/toolbar';
import But<PERSON> from 'primevue/button';
import { navOptions } from './NavOptions'

const route = useRoute()
const router = useRouter()
const visibleItem = ref(null)

const setActiveNav = (path) => {
  const sortedNav = [...navOptions].sort((a, b) => b.to.length - a.to.length)
  const activeNav = sortedNav.find(item => path.startsWith(item.to))
  visibleItem.value = activeNav?.text || null
}

const onNavBtnClick = (item) => {
  visibleItem.value = item.text
  router.push(item.to)
}

onMounted(() => {
  setActiveNav(route.path)
})

// 💡 Watch for route changes (e.g., via <router-link> from other components)
watch(() => route.path, (newPath) => {
  setActiveNav(newPath)
})
</script>
<template>
    <Toolbar class="bottom-nav">

        <template #center>
            <div class="card flex flex-1 items-center">
                <Button v-for="(item, index) in navOptions" :key="index"
                    :label="visibleItem === item.text ? item.text : ''" severity="secondary"
                    :icon="`pi pi-${item.icon}`" iconClass="btn-icon" iconPos="top" @click="onNavBtnClick(item)" 
                    class="nav-item flex-1" :class="visibleItem === item.text ? 'active' : ''" v-tooltip.top="item.text"/>
            </div>
        </template>
    </Toolbar>

</template>

<style scoped>
.bottom-nav {
    position: fixed;
    width: 100%;
    z-index: 1;
    bottom: 0;
    padding: 0;
    height: 5rem;
}

:deep(.p-toolbar-center) {
    flex: 1;
    gap: 4px;
}

.nav-item {
    background: transparent;
    border-color: transparent;
    border-radius: 0;
}

:deep(.p-button-label) {
    font-weight: var(--p-button-label-font-weight);
    font-size: smaller;
}

:deep(.btn-icon) {
    font-size: 1.8rem;
}

.active {
    background: var(--p-button-secondary-hover-background);
    border: 1px solid var(--p-button-secondary-hover-border-color);
    color: var(--p-button-secondary-hover-color);
}
:deep(.active .btn-icon){
    color: var(--p-button-link-color);
}
</style>

