import { Controller, Get, Res } from '@nestjs/common';
import { AppService } from './app.service';
import { Response } from 'express';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('/jdadmin')
  getAdminL(@Res() res: Response): void {
    res.send(this.appService.getAdmin());
  }

  @Get('/jdadmin/*path')
  getAdmin(@Res() res: Response): void {
    res.send(this.appService.getAdmin());
  }

  @Get('/employer')
  getEmployer(@Res() res: Response): void {
    res.send(this.appService.getEmployer());
  }

  @Get('/employer/*path')
  getEmployerL(@Res() res: Response): void {
    res.send(this.appService.getEmployer());
  }

  @Get('/jobseeker')
  getJobseekerL(@Res() res: Response): void {
    res.send(this.appService.getJobseeker());
  }

  @Get('/jobseeker/*path')
  getJobseeker(@Res() res: Response): void {
    res.send(this.appService.getJobseeker());
  }

  // Handle all other routes to serve the Vue.js application
  @Get('')
  serveVueApp(@Res() res: Response): void {
    res.send(this.appService.getWelcome());
  }
}
