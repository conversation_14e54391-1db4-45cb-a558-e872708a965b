import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { applicationsService } from '@/api/services/applicationsService'

export const useApplicationsStore = defineStore('applications', () => {
  const applications = ref([])
  const pendingApplications = ref([]) // New: for unsaved applications
  const isLoading = ref(false)
  const error = ref(null)

  // Fetch all applications from the backend
  const fetchApplications = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await applicationsService.getUserApplications()
      console.log('Raw applications response:', response) // Debug log

      if (response.success) {
        // Handle both array and paginated responses
        applications.value = response.data?.items || []
        
        console.log('Processed applications:', applications.value) // Debug log
      } else {
        error.value = response.message || 'Failed to fetch applications'
        console.error('Failed to fetch applications:', response.message)
      }
    } catch (err) {
      error.value = err.message || 'Failed to fetch applications'
      console.error('Error fetching applications:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Create a pending application (not saved to backend yet)
  const createPendingApplication = (job) => {
    // Check if this job is already in pending applications
    const existingPendingApp = pendingApplications.value.find(app => app.jobId === job.id)
    if (existingPendingApp) {
      return existingPendingApp
    }

    // Check if this job is already in saved applications
    const existingSavedApp = applications.value.find(app => app.jobId === job.id)
    if (existingSavedApp) {
      return existingSavedApp
    }

    // Create a new pending application
    const pendingApp = {
      id: `pending-${Date.now()}`, // Temporary ID with pending prefix
      jobId: job.id,
      jobTitle: job.title,
      company: job.company?.name || job.company,
      location: job.location,
      color: job.color,
      status: 'PENDING',
      isPending: true, // Flag to identify unsaved applications
      appliedDate: new Date().toISOString(),
    }

    // Add to pending applications
    pendingApplications.value.push(pendingApp)
    return pendingApp
  }

  // Save a pending application to the backend
  const savePendingApplication = async (pendingAppId) => {
    isLoading.value = true
    error.value = null

    try {
      // Find the pending application
      const pendingAppIndex = pendingApplications.value.findIndex(app => app.id === pendingAppId)
      if (pendingAppIndex === -1) {
        throw new Error('Pending application not found')
      }

      const pendingApp = pendingApplications.value[pendingAppIndex]
      console.log('Saving pending application:', pendingApp)

      // Create the application through the API
      const response = await applicationsService.create({
        jobId: pendingApp.jobId,
        coverLetter: pendingApp.coverLetter || '',
      })

      console.log('API response:', response)

      if (response.success) {
        // Remove from pending applications
        pendingApplications.value.splice(pendingAppIndex, 1)

        // Fetch the latest applications to ensure we have the most up-to-date data
        await fetchApplications()

        const savedApp = applications.value.find(app => app.jobId === pendingApp.jobId)
        console.log('Saved application found:', savedApp)
        return savedApp
      } else {
        throw new Error(response.message || 'Failed to save application')
      }
    } catch (err) {
      console.error('Error saving pending application:', err)
      error.value = err.message || 'Failed to save application'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  // Apply to a job (create application in backend)
  const addApplication = async (job) => {
    isLoading.value = true
    error.value = null
    try {
      // Create the application through the API
      const response = await applicationsService.create({
        jobId: job.id,
        // Add any additional application data here if needed
        coverLetter: '', // Optional: Add if your form collects this
      })

      // If we got a success response (even without data), create an application object
      if (response.success) {
        // Fetch the latest applications to ensure we have the most up-to-date data
        await fetchApplications()

        // Return the newly created application
        return applications.value.find(app => app.jobId === job.id) || {
          id: Date.now().toString(), // Temporary ID until we refresh
          jobId: job.id,
          jobTitle: job.title,
          company: job.company,
          location: job.location,
          color: job.color,
          status: 'PENDING',
          appliedDate: new Date().toISOString(),
          ...(response.data || {})
        }
      } else {
        throw new Error(response.message || 'Failed to apply')
      }
    } catch (err) {
      error.value = err.message || 'Failed to apply'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  // Remove a pending application
  const removePendingApplication = (pendingAppId) => {
    const index = pendingApplications.value.findIndex(app => app.id === pendingAppId)
    if (index !== -1) {
      pendingApplications.value.splice(index, 1)
    }
  }

  // Save all pending applications
  const saveAllPendingApplications = async () => {
    const pendingApps = [...pendingApplications.value]
    const results = []

    for (const pendingApp of pendingApps) {
      try {
        const savedApp = await savePendingApplication(pendingApp.id)
        results.push({ success: true, app: savedApp })
      } catch (error) {
        results.push({ success: false, error: error.message, app: pendingApp })
      }
    }

    return results
  }

  // Get all applications (saved + pending)
  const allApplications = computed(() => {
    return [...applications.value, ...pendingApplications.value]
  })

  // Check if a job has been applied to (either saved or pending)
  const hasAppliedToJob = (jobId) => {
    return applications.value.some(app => app.jobId === jobId) ||
           pendingApplications.value.some(app => app.jobId === jobId)
  }

  const handleFiltersUpdate = (newFilters) => {
    console.log('Filters changed:', newFilters)
    filters.value = newFilters
    // jobsStore.fetchJobs() if using API
  }

  const filteredJobs = computed(() => {
    console.log('Filtering jobs with:', filters.value)
    // ...rest of filter logic
  })

  return {
    applications,
    pendingApplications,
    allApplications,
    isLoading,
    error,
    fetchApplications,
    addApplication,
    createPendingApplication,
    savePendingApplication,
    removePendingApplication,
    saveAllPendingApplications,
    hasAppliedToJob,
    handleFiltersUpdate,
    filteredJobs,
  }
}) 