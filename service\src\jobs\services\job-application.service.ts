import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobEntity, JobStatus } from '../entities/job.entity';
import { JobApplicationEntity, ApplicationStatus } from '../entities/job-application.entity';
import { JobFavoriteEntity } from '../entities/job-favorite.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { CreateJobApplicationDto } from '../dto/create-job-application.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';
import { UserRole } from '../../users/enums/user.enum';
import { NotificationsService } from '../../notifications/notifications.service';

@Injectable()
export class JobApplicationService {
  constructor(
    @InjectRepository(JobEntity)
    private readonly jobRepository: Repository<JobEntity>,
    @InjectRepository(JobApplicationEntity)
    private readonly applicationRepository: Repository<JobApplicationEntity>,
    @InjectRepository(JobFavoriteEntity)
    private readonly favoriteRepository: Repository<JobFavoriteEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    private readonly notificationsService: NotificationsService,
  ) {}

  async applyForJob(
    userId: string,
    jobId: string,
    createApplicationDto: CreateJobApplicationDto,
  ): Promise<JobApplicationEntity> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const job = await this.jobRepository.findOne({
      where: { id: jobId, isDeleted: false },
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    if (job.status !== JobStatus.ACTIVE) {
      throw new BadRequestException('Cannot apply for inactive jobs');
    }

    // Fetch all previous applications for this job by this user, latest first
    const previousApplications = await this.applicationRepository.find({
      where: {
        job: { id: jobId },
        applicant: { id: userId },
      },
      order: { createdAt: 'DESC' },
    });

    if (previousApplications.length > 0) {
      const latest = previousApplications[0];
      if (
        latest.status !== ApplicationStatus.REJECTED &&
        latest.status !== ApplicationStatus.WITHDRAWN
      ) {
        throw new BadRequestException('You have already applied for this job');
      }
    }

    let application;
    // Find a withdrawn application from previousApplications, if any
    const withdrawnApplication = previousApplications.find(
      (app) => app.status === ApplicationStatus.WITHDRAWN,
    );
    if (withdrawnApplication) {
      // If there's a withdrawn application, update its status and other fields
      withdrawnApplication.status = ApplicationStatus.PENDING;
      withdrawnApplication.coverLetter = createApplicationDto.coverLetter;
      withdrawnApplication.resumeUrl = createApplicationDto.resumeUrl;
      withdrawnApplication.adminComment = null;
      application = withdrawnApplication;
    } else {
      // Create new application if no withdrawn application exists
      application = this.applicationRepository.create({
        job,
        applicant: user,
        ...createApplicationDto,
      });
    }

    const savedApplication = await this.applicationRepository.save(application);

    // Notify employer
    await this.notificationsService.notifyJobApplied(
      {
        jobId: job.id,
        applicantId: user.id,
      },
      user,
    );

    return savedApplication;
  }

  async getMyApplications(
    userId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobApplicationEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [applications, total] = await this.applicationRepository.findAndCount({
      where: {
        applicant: { id: userId },
      },
      relations: ['job', 'job.industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: applications,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async toggleFavorite(userId: string, jobId: string): Promise<{ isFavorite: boolean }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const job = await this.jobRepository.findOne({
      where: { id: jobId, isDeleted: false },
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    const existingFavorite = await this.favoriteRepository.findOne({
      where: {
        job: { id: jobId },
        user: { id: userId },
      },
    });

    if (existingFavorite) {
      await this.favoriteRepository.remove(existingFavorite);
      return { isFavorite: false };
    }

    const favorite = this.favoriteRepository.create({
      job,
      user,
    });

    await this.favoriteRepository.save(favorite);
    return { isFavorite: true };
  }

  async getMyFavorites(
    userId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [favorites, total] = await this.favoriteRepository.findAndCount({
      where: {
        user: { id: userId },
      },
      relations: ['job', 'job.industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: favorites.map((fav) => fav.job),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async updateApplicationStatus(
    applicationId: string,
    userId: string,
    userRole: UserRole,
    status: ApplicationStatus,
    comment?: string,
    interviewDate?: string,
    interviewTime?: string,
    interviewLocation?: string,
    interviewNotes?: string,
  ): Promise<JobApplicationEntity> {
    const application = await this.applicationRepository.findOne({
      where: { id: applicationId },
      relations: ['job', 'job.employer', 'applicant'],
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Only employer or admin can update status
    if (
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN &&
      application.job.employer.id !== userId
    ) {
      throw new ForbiddenException('Only employer or admin can update application status');
    }

    application.status = status;
    if (comment) {
      application.adminComment = comment;
    }

    // Set interview details if status is INTERVIEW_SCHEDULED
    if (status === ApplicationStatus.INTERVIEW_SCHEDULED) {
      if (interviewDate) application.interviewDate = new Date(interviewDate);
      if (interviewTime) application.interviewTime = interviewTime;
      if (interviewLocation) application.interviewLocation = interviewLocation;
      if (interviewNotes) application.interviewNotes = interviewNotes;
    }

    const saved = await this.applicationRepository.save(application);

    // Notify jobseeker of status change
    await this.notificationsService.notifyApplicationStatusUpdate(application);

    return saved;
  }

  async withdrawApplication(applicationId: string, userId: string): Promise<JobApplicationEntity> {
    const application = await this.applicationRepository.findOne({
      where: { id: applicationId },
      relations: ['applicant'],
    });
    if (!application) {
      throw new NotFoundException('Application not found');
    }
    if (application.applicant.id !== userId) {
      throw new ForbiddenException('Only the applicant can withdraw this application');
    }
    application.status = ApplicationStatus.WITHDRAWN;
    return this.applicationRepository.save(application);
  }

  async getJobApplicants(
    userId: string,
    userRole: UserRole,
    paginationDto: PaginationDto,
    jobId?: string,
  ): Promise<PaginatedResponseDto<JobApplicationEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const whereCondition: any = {};

    // If specific job ID is provided, filter by that job
    if (jobId) {
      whereCondition.job = { id: jobId };
    }

    // For employers, only show applications for their own jobs
    if (userRole === UserRole.EMPLOYER) {
      whereCondition.job = {
        ...whereCondition.job,
        employer: { id: userId },
      };
    }

    // For admins and super admins, show all applications
    // (whereCondition remains empty to show all)

    const [applications, total] = await this.applicationRepository.findAndCount({
      where: whereCondition,
      relations: ['job', 'job.industry', 'applicant', 'applicant.profile'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: applications,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async updateApplication(
    applicationId: string,
    updateDto: CreateJobApplicationDto, // or UpdateJobApplicationDto
  ): Promise<JobApplicationEntity> {
    const application = await this.applicationRepository.findOne({ where: { id: applicationId } });
    if (!application) throw new NotFoundException('Application not found');

    // Update interview fields if provided
    if (updateDto.interviewDate !== undefined) {
      application.interviewDate = new Date(updateDto.interviewDate);
    }
    if (updateDto.interviewTime !== undefined) application.interviewTime = updateDto.interviewTime;
    if (updateDto.interviewLocation !== undefined)
      application.interviewLocation = updateDto.interviewLocation;
    if (updateDto.interviewNotes !== undefined)
      application.interviewNotes = updateDto.interviewNotes;

    // ...update other fields as needed...

    return this.applicationRepository.save(application);
  }
}
