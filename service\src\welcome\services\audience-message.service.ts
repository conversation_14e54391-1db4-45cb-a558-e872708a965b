import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AudienceMessageEntity } from '../entities/audience-message.entity';
import { CreateAudienceMessageDto, UpdateAudienceMessageDto } from '../dto/audience-message.dto';
import { WelcomeEntity } from '../entities/welcome.entity';

@Injectable()
export class AudienceMessageService {
  constructor(
    @InjectRepository(AudienceMessageEntity)
    private readonly audienceMessageRepository: Repository<AudienceMessageEntity>,
    @InjectRepository(WelcomeEntity)
    private readonly welcomeRepository: Repository<WelcomeEntity>,
  ) {}

  async create(
    welcomeId: string,
    createDto: CreateAudienceMessageDto,
  ): Promise<AudienceMessageEntity> {
    const welcome = await this.welcomeRepository.findOne({ where: { id: welcomeId } });
    if (!welcome) {
      throw new NotFoundException('Welcome not found');
    }

    const audienceMessage = this.audienceMessageRepository.create({
      ...createDto,
      welcome,
    });

    return this.audienceMessageRepository.save(audienceMessage);
  }

  async findAll(welcomeId: string): Promise<AudienceMessageEntity[]> {
    return this.audienceMessageRepository.find({
      where: { welcome: { id: welcomeId } },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<AudienceMessageEntity> {
    const audienceMessage = await this.audienceMessageRepository.findOne({ where: { id } });
    if (!audienceMessage) {
      throw new NotFoundException('Audience message not found');
    }
    return audienceMessage;
  }

  async update(id: string, updateDto: UpdateAudienceMessageDto): Promise<AudienceMessageEntity> {
    const audienceMessage = await this.findOne(id);

    Object.assign(audienceMessage, updateDto);
    return this.audienceMessageRepository.save(audienceMessage);
  }

  async delete(id: string): Promise<void> {
    const audienceMessage = await this.findOne(id);
    await this.audienceMessageRepository.remove(audienceMessage);
  }

  async deleteAll(welcomeId: string): Promise<void> {
    await this.audienceMessageRepository.delete({ welcome: { id: welcomeId } });
  }
}
