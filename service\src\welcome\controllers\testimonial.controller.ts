import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { TestimonialService } from '../services/testimonial.service';
import { CreateTestimonialDto, UpdateTestimonialDto } from '../dto/testimonial.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { ApiTags, ApiOperation, ApiResponse as SwaggerResponse } from '@nestjs/swagger';
import { ApiResponseDto } from '../dto/api-response.dto';
import { TestimonialEntity } from '../entities/testimonial.entity';

@ApiTags('Testimonials')
@Controller('welcome/:welcomeId/testimonials')
export class TestimonialController {
  constructor(private readonly testimonialService: TestimonialService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new testimonial' })
  @SwaggerResponse({
    status: HttpStatus.CREATED,
    description: 'Testimonial created successfully',
    type: ApiResponseDto<TestimonialEntity>,
  })
  async create(
    @Param('welcomeId') welcomeId: string,
    @Body() createDto: CreateTestimonialDto,
  ): Promise<ApiResponseDto<TestimonialEntity>> {
    try {
      const testimonial = await this.testimonialService.create(welcomeId, createDto);
      return ApiResponseDto.success(testimonial, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error(
        'TESTIMONIAL_CREATE_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all testimonials for a welcome' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Testimonials retrieved successfully',
    type: ApiResponseDto<[TestimonialEntity]>,
  })
  async findAll(
    @Param('welcomeId') welcomeId: string,
  ): Promise<ApiResponseDto<TestimonialEntity[]>> {
    try {
      const testimonials = await this.testimonialService.findAll(welcomeId);
      return ApiResponseDto.success(testimonials, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'TESTIMONIALS_FETCH_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a testimonial by ID' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Testimonial retrieved successfully',
    type: ApiResponseDto<TestimonialEntity>,
  })
  async findOne(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<TestimonialEntity>> {
    try {
      const testimonial = await this.testimonialService.findOne(id);
      return ApiResponseDto.success(testimonial, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('TESTIMONIAL_NOT_FOUND', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update a testimonial' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Testimonial updated successfully',
    type: ApiResponseDto<TestimonialEntity>,
  })
  async update(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
    @Body() updateDto: UpdateTestimonialDto,
  ): Promise<ApiResponseDto<TestimonialEntity>> {
    try {
      const testimonial = await this.testimonialService.update(id, updateDto);
      return ApiResponseDto.success(testimonial, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'TESTIMONIAL_UPDATE_ERROR',
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete a testimonial' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Testimonial deleted successfully',
    type: ApiResponseDto<void>,
  })
  async delete(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<void>> {
    try {
      await this.testimonialService.delete(id);
      return ApiResponseDto.success(null, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'TESTIMONIAL_DELETE_ERROR',
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
