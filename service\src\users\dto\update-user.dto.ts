import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, Length } from 'class-validator';
import { UserRole, SubscriptionType } from '../enums/user.enum';
import { UpdateProfileDto } from './profile.dto';

export class UpdateUserDto extends UpdateProfileDto {
  @ApiProperty({ example: 'John', required: false })
  @IsString()
  @IsOptional()
  @Length(2, 50)
  firstName?: string;

  @ApiProperty({ example: 'Doe', required: false })
  @IsString()
  @IsOptional()
  @Length(2, 50)
  lastName?: string;

  @ApiProperty({ enum: UserRole, required: false })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @ApiProperty({ enum: SubscriptionType, required: false })
  @IsEnum(SubscriptionType)
  @IsOptional()
  subscriptionType?: SubscriptionType;
}
