import {
  Entity,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinC<PERSON>um<PERSON>,
} from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { IndustryEntity } from '../../industries/entities/industry.entity';
import { SubIndustryEntity } from '../../industries/entities/sub-industry.entity';
import { JobApplicationEntity } from './job-application.entity';
import { JobFavoriteEntity } from './job-favorite.entity';
import { CompanyEntity } from '../../companies/entities/company.entity';
import { ProfileEntity } from '../../users/entities/profile.entity';

export enum JobType {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  CONTRACT = 'CONTRACT',
  DAILY_WAGE = 'DAILY_WAGE', // Already present

  TEMPORARY = 'TEMPORARY',
  SEASONAL = 'SEASONAL',
  FREELANCE = 'FREELANCE',
  APPRENTICESHIP = 'APPRENTICESHIP',
}
export enum JobStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',

  DRAFT = 'DRAFT',
  CLOSED = 'CLOSED',
  ON_HOLD = 'ON_HOLD',
  FILLED = 'FILLED',
}
export enum ExperienceLevel {
  FRESHER = 'FRESHER', // Map to 'ENTRY LEVEL'
  EXPERIENCED = 'EXPERIENCED', // Map to 'SOME' or 'EXPERIENCED'
  EXPERT = 'EXPERT',

  SOME = 'SOME', // Missing, added for '1-3 years'
  SENIOR = 'SENIOR', // Missing, added for '5-10 years'
}
export enum ApplicationStatus {
  SUBMITTED = 'SUBMITTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  INTERVIEW_SCHEDULED = 'INTERVIEW_SCHEDULED',
  INTERVIEW_COMPLETED = 'INTERVIEW_COMPLETED',
  OFFER_EXTENDED = 'OFFER_EXTENDED',
  HIRED = 'HIRED',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN',
}
export enum WorkSchedule {
  DAY_SHIFT = 'DAY_SHIFT',
  EVENING_SHIFT = 'EVENING_SHIFT',
  NIGHT_SHIFT = 'NIGHT_SHIFT',
  ROTATING_SHIFTS = 'ROTATING_SHIFTS',
  FLEXIBLE_HOURS = 'FLEXIBLE_HOURS',
  WEEKEND_ONLY = 'WEEKEND_ONLY',
}
export enum EducationLevel {
  NONE = 'NONE',
  HIGH_SCHOOL = 'HIGH_SCHOOL',
  TRADE_SCHOOL = 'TRADE_SCHOOL',
  ASSOCIATE = 'ASSOCIATE',
  BACHELOR = 'BACHELOR',
  CERTIFICATION = 'CERTIFICATION',
}
export enum LocationType {
  ON_SITE = 'ON_SITE',
  REMOTE = 'REMOTE',
  HYBRID = 'HYBRID',
  TRAVEL = 'TRAVEL',
  MULTIPLE = 'MULTIPLE',
}
export enum CompanySize {
  STARTUP = 'STARTUP',
  SMALL = 'SMALL',
  MEDIUM = 'MEDIUM',
  LARGE = 'LARGE',
  ENTERPRISE = 'ENTERPRISE',
}
export enum ContactDisplayType {
  NONE = 'NONE',
  PHONE = 'PHONE',
  EMAIL = 'EMAIL',
  BOTH = 'BOTH',
}
export enum JobUrgency {
  URGENT = 'URGENT',
  WITHIN_10_DAYS = 'WITHIN_10_DAYS',
  WITHIN_1_MONTH = 'WITHIN_1_MONTH',
  FLEXIBLE = 'FLEXIBLE',
}
export enum PaymentType {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  BI_WEEKLY = 'BI_WEEKLY',
  MONTHLY = 'MONTHLY',
}

@Entity('jobs')
export class JobEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text')
  description: string;

  @ManyToOne(() => IndustryEntity, (industry) => industry.jobs)
  @JoinColumn()
  industry: IndustryEntity;

  @ManyToOne(() => SubIndustryEntity, (subIndustry) => subIndustry.jobs)
  @JoinColumn()
  subIndustry: SubIndustryEntity;

  @Column('decimal', { precision: 10, scale: 2 })
  salary: number;

  @Column({
    type: 'enum',
    enum: JobType,
    default: JobType.FULL_TIME,
  })
  jobType: JobType;

  @Column({
    type: 'enum',
    enum: PaymentType,
    default: PaymentType.MONTHLY,
  })
  paymentType: PaymentType;

  @Column({ type: 'text', nullable: true })
  location: string;

  @Column({ default: false })
  isDeleted: boolean;

  @Column({ default: false })
  isUserVerified: boolean;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.PENDING,
  })
  status: JobStatus;

  @Column({ type: 'timestamp', nullable: true })
  scheduledPostTime: Date;

  @Column({ type: 'text', nullable: true })
  adminComment: string;

  @Column({
    type: 'enum',
    enum: JobUrgency,
    default: JobUrgency.FLEXIBLE,
  })
  urgency: JobUrgency;

  @Column({
    type: 'enum',
    enum: ExperienceLevel,
    default: ExperienceLevel.FRESHER,
  })
  experienceLevel: ExperienceLevel;

  @Column('text', { array: true, default: [] })
  benefits: string[];

  @Column('text', { array: true, default: [] })
  requirements: string[];

  @Column('text', { array: true, default: [] })
  responsibilities: string[];

  @Column('text', { array: true, default: [] })
  skills: string[];

  @Column({ type: 'text', nullable: true })
  thumbnail: string;

  @Column({ type: 'text', array: true, default: [] })
  images: string[];

  @Column({ default: true })
  showContact: boolean;

  @Column({
    type: 'enum',
    enum: ContactDisplayType,
    default: ContactDisplayType.NONE,
  })
  contactDisplayType: ContactDisplayType;

  @Column({ type: 'text', nullable: true })
  contactPhone: string;

  @Column({ type: 'text', nullable: true })
  contactEmail: string;

  @Column({ type: 'text', nullable: true })
  contactPerson: string;

  @Column({ type: 'int', nullable: true })
  vacancies: number;

  @Column({ type: 'int', nullable: true })
  workingHours: number;

  @Column({ default: false })
  accommodation: boolean;

  @Column({ default: false })
  transportation: boolean;

  @Column({ default: false })
  foodProvided: boolean;

  @Column({ default: false })
  safetyEquipment: boolean;

  @Column({ default: false })
  trainingProvided: boolean;

  @Column({ type: 'int', default: 0 })
  views: number;

  @ManyToOne(() => UserEntity, (user) => user.jobs)
  @JoinColumn()
  employer: UserEntity;

  @ManyToOne(() => CompanyEntity)
  @JoinColumn()
  company: CompanyEntity;

  @ManyToOne(() => ProfileEntity)
  @JoinColumn()
  profile: ProfileEntity;

  @OneToMany(() => JobApplicationEntity, (application) => application.job)
  applications: JobApplicationEntity[];

  @OneToMany(() => JobFavoriteEntity, (favorite) => favorite.job)
  favorites: JobFavoriteEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
