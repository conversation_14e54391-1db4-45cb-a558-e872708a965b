import { Entity, Column, PrimaryGeneratedColumn, OneTo<PERSON>ne, JoinColumn } from 'typeorm';
import { UserEntity } from './user.entity';

@Entity('business_details')
export class BusinessDetailsEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  shopName: string;

  @Column()
  shopAddress: string;

  @Column('decimal', { precision: 10, scale: 8, nullable: true })
  latitude: number;

  @Column('decimal', { precision: 11, scale: 8, nullable: true })
  longitude: number;

  @Column({ nullable: true })
  gstNumber: string;

  @Column()
  ownerFirstName: string;

  @Column({ nullable: true })
  ownerMiddleName: string;

  @Column()
  ownerLastName: string;

  @Column({ default: false })
  isVerified: boolean;

  @Column({ type: 'timestamp', nullable: true })
  verifiedAt: Date;

  @Column({ nullable: true })
  verifiedBy: string;

  @Column()
  userId: string;

  @OneToOne(() => UserEntity, (user) => user.businessDetails)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}
