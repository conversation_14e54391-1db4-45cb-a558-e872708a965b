import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

const BASE_URL = ENDPOINTS.NOTIFICATIONS.LIST

export const notificationsService = {
  async getNotifications({ userId, page = 1, limit = 20, isRead, type }) {
    const params = { userId, page, limit }
    if (isRead !== undefined) params.isRead = isRead
    if (type) params.type = type
    return httpClient.get(BASE_URL, { params })
  },

  async markAsRead({ notificationId, userId }) {
    return httpClient.post(`${BASE_URL}/read/${notificationId}`, { userId })
  },

  async markAllAsRead({ userId }) {
    return httpClient.post(`${BASE_URL}/read-all`, { userId })
  },

  async deleteNotification({ notificationId, userId }) {
    return httpClient.delete(`${BASE_URL}/${notificationId}`, { data: { userId } })
  },
} 