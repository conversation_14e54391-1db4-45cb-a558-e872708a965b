/**
 * Utility functions for formatting data
 */

/**
 * Formats boolean values to "Yes"/"No" strings
 * @param {boolean|string} value - The value to format
 * @returns {string} "Yes", "No", or the original value if not a boolean
 */
export const formatBooleanValue = (value) => {
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No'
  }
  // If it's a string that contains boolean-like values, handle those too
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase()
    if (lowerValue === 'true' || lowerValue === 'yes') return 'Yes'
    if (lowerValue === 'false' || lowerValue === 'no') return 'No'
    // If it's not a boolean string, return the original value
    return value
  }
  return value
}

/**
 * Formats job type enum values to readable strings
 * @param {string} type - The job type enum value
 * @returns {string} Formatted job type
 */
export const formatJobType = (type) => {
  const typeMap = {
    'FULL_TIME': 'Full-time',
    'PART_TIME': 'Part-time',
    'CONTRACT': 'Contract',
    'TEMPORARY': 'Temporary',
    'FREELANCE': 'Freelance'
  }
  return typeMap[type] || type
}

/**
 * Formats experience level enum values to readable strings
 * @param {string} level - The experience level enum value
 * @returns {string} Formatted experience level
 */
export const formatExperienceLevel = (level) => {
  const levelMap = {
    'FRESHER': 'Entry Level',
    'EXPERIENCED': 'Experienced',
    'EXPERT': 'Expert Level',
    'SENIOR': 'Senior Level'
  }
  return levelMap[level] || level
} 