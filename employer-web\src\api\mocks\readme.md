Authorized User (Job Seeker):
🧪 Testing Scenarios:

Email: <EMAIL>
OTP: 123456
Result: ✅ Login successful → Dashboard
❌ Unauthorized User (Non-Job Seeker):

Email: <EMAIL>
OTP: 123456
Result: ❌ Shows unauthorized alert → Logout
🔄 Other Test Scenarios:

OTP: 000000 → Shows "OTP Expired" alert
OTP: 111111 → Shows "Invalid OTP" alert



✅ Number-Only Input:

✅ Typing "1", "2", "3" → Works
❌ Typing "a", "!", "@" → Blocked
✅ Pasting "12ab34cd56" → Extracts "123456"
🔄 OTP Clearing Scenarios:

❌ Unauthorized user → Clears OTP + Shows alert
❌ OTP expired (000000) → Clears OTP + Shows expired alert
❌ Invalid OTP (111111) → Clears OTP + Shows invalid alert
🔄 Resend OTP → Clears OTP + Focuses first input
⌨️ Keyboard Navigation:

✅ Arrow keys → Navigate between inputs
✅ Backspace → Move to previous input when empty
✅ Tab → Standard tab navigation
❌ Letters/symbols → Completely blocked