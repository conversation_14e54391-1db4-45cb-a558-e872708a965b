import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
  Request,
} from '@nestjs/common';
import { DescriptionService } from '../services/description.service';
import { CreateDescriptionDto, UpdateDescriptionDto } from '../dto/description.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { ApiTags, ApiOperation, ApiResponse as SwaggerResponse } from '@nestjs/swagger';
import { ApiResponseDto } from '../dto/api-response.dto';
import { DescriptionEntity } from '../entities/description.entity';

@ApiTags('Descriptions')
@Controller('welcome/:welcomeId/descriptions')
export class DescriptionController {
  constructor(private readonly descriptionService: DescriptionService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new description' })
  @SwaggerResponse({
    status: HttpStatus.CREATED,
    description: 'Description created successfully',
    type: ApiResponseDto<DescriptionEntity>,
  })
  async create(
    @Param('welcomeId') welcomeId: string,
    @Body() createDto: CreateDescriptionDto,
  ): Promise<ApiResponseDto<DescriptionEntity>> {
    try {
      const description = await this.descriptionService.create(welcomeId, createDto);
      return ApiResponseDto.success(description, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error(
        'DESCRIPTION_CREATE_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all descriptions for a welcome' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Descriptions retrieved successfully',
    type: ApiResponseDto<[DescriptionEntity]>,
  })
  async findAll(
    @Param('welcomeId') welcomeId: string,
  ): Promise<ApiResponseDto<DescriptionEntity[]>> {
    try {
      const descriptions = await this.descriptionService.findAll(welcomeId);
      return ApiResponseDto.success(descriptions, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'DESCRIPTIONS_FETCH_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a description by ID' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Description retrieved successfully',
    type: ApiResponseDto<DescriptionEntity>,
  })
  async findOne(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<DescriptionEntity>> {
    try {
      const description = await this.descriptionService.findOne(id);
      return ApiResponseDto.success(description, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('DESCRIPTION_NOT_FOUND', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update a description' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Description updated successfully',
    type: ApiResponseDto<DescriptionEntity>,
  })
  async update(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
    @Body() updateDto: UpdateDescriptionDto,
  ): Promise<ApiResponseDto<DescriptionEntity>> {
    try {
      const description = await this.descriptionService.update(id, updateDto);
      return ApiResponseDto.success(description, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'DESCRIPTION_UPDATE_ERROR',
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete a description' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Description deleted successfully',
    type: ApiResponseDto<void>,
  })
  async delete(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<void>> {
    try {
      await this.descriptionService.delete(id);
      return ApiResponseDto.success(null, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'DESCRIPTION_DELETE_ERROR',
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
