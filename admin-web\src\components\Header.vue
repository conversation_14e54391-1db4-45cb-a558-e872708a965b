<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Button from 'primevue/button'

const route = useRoute()

const emit = defineEmits(['toggle-sidebar'])

const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const breadcrumbTitle = computed(() => {
  return route.meta?.title || 'Dashboard'
})
</script>

<template>
  <header class="app-header">
    <div class="header-left">
      <Button
        icon="pi pi-bars"
        text
        rounded
        @click="toggleSidebar"
        class="sidebar-toggle"
        aria-label="Toggle sidebar"
      />
      
      <div class="breadcrumb">
        <span class="breadcrumb-item">{{ breadcrumbTitle }}</span>
      </div>
    </div>
    
    <div class="header-right">
      <div class="header-info">
        <span class="welcome-text">Welcome back!</span>
      </div>
    </div>
  </header>
</template>

<style scoped>
.app-header {
  height: 60px;
  background: var(--p-surface-card);
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  position: fixed;
  top: 0;
  left: 240px; /* Start after sidebar */
  right: 0;
  z-index: 90;
  transition: left 0.3s ease;
}

/* When sidebar is collapsed */
.app-header.sidebar-collapsed {
  left: 70px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  display: none;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-item {
  color: var(--p-text-color);
  font-weight: 600;
  font-size: 1.125rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-info {
  display: flex;
  align-items: center;
}

.welcome-text {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .app-header {
    left: 0;
    padding: 0 1rem;
  }
  
  .sidebar-toggle {
    display: flex;
  }
  
  .welcome-text {
    display: none;
  }
}
</style>