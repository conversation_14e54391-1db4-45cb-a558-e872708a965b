import { Controller, Get, Post, Put, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { BusinessDetailsService } from '../services/business-details.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { BusinessDetailsEntity } from '../entities/business-details.entity';

@ApiTags('Business Details')
@Controller('business-details')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BusinessDetailsController {
  constructor(private readonly businessDetailsService: BusinessDetailsService) {}

  @Post()
  @Roles(UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Create business details' })
  @ApiResponse({ status: 201, description: 'Business details created successfully' })
  async create(@Request() req, @Body() businessData: Partial<BusinessDetailsEntity>) {
    return this.businessDetailsService.create(req.user.id, businessData);
  }

  @Put()
  @Roles(UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Update business details' })
  @ApiResponse({ status: 200, description: 'Business details updated successfully' })
  async update(@Request() req, @Body() businessData: Partial<BusinessDetailsEntity>) {
    return this.businessDetailsService.update(req.user.id, businessData);
  }

  @Get('my-details')
  @Roles(UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Get own business details' })
  @ApiResponse({ status: 200, description: 'Business details retrieved successfully' })
  async getMyDetails(@Request() req) {
    return this.businessDetailsService.getByUserId(req.user.id);
  }

  @Get('search')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Search businesses' })
  @ApiResponse({ status: 200, description: 'Businesses retrieved successfully' })
  async searchBusinesses(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('searchTerm') searchTerm?: string,
    @Query('isVerified') isVerified?: string,
  ) {
    return this.businessDetailsService.searchBusinesses(
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined,
      searchTerm,
      isVerified ? isVerified === 'true' : undefined,
    );
  }

  @Post(':userId/verify')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Verify business' })
  @ApiResponse({ status: 200, description: 'Business verified successfully' })
  async verifyBusiness(@Param('userId') userId: string, @Request() req) {
    return this.businessDetailsService.verify(userId, req.user.id);
  }

  @Post(':userId/unverify')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Unverify business' })
  @ApiResponse({ status: 200, description: 'Business unverified successfully' })
  async unverifyBusiness(@Param('userId') userId: string, @Request() req) {
    return this.businessDetailsService.unverify(userId, req.user.id);
  }
}
