<script setup>
import { ref, computed, onMounted, onActivated, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
// import Avatar from 'primevue/avatar'
import LoadingSkelton from './LoadingSkelton.vue'
import Tag from 'primevue/tag'
import { useApplicationsStore } from '@/stores/applications'
import { applicationsService } from '@/api/services/applicationsService'
import Select from 'primevue/select'
import { useGlobalToast } from '@/composables/useGlobalToast'
const { showToast } = useGlobalToast()

const router = useRouter()
const { t } = useI18n()
const isLoading = ref(false)
const activeFilter = ref('all')
const applicationsStore = useApplicationsStore()

// Computed filters
const pendingApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'PENDING' || app.status === 'pending' || app.status === 'UNDER_REVIEW' || app.status === 'under_review'))
)

const interviewApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'INTERVIEW' || app.status === 'interview'))
)

const offerApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'OFFER' || app.status === 'offer'))
)

const rejectedApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'REJECTED' || app.status === 'rejected'))
)

const withdrawnApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'WITHDRAWN' || app.status === 'withdrawn'))
)

const filterOptions = computed(() => [
  { label: t('applications.allApplications', { count: applicationsStore.applications?.length || 0 }), value: 'all' },
  { label: t('applications.pending', { count: pendingApplications.value.length }), value: 'pending' },
  { label: t('applications.interview', { count: interviewApplications.value.length }), value: 'interview' },
  { label: t('applications.offers', { count: offerApplications.value.length }), value: 'offer' },
  { label: t('applications.rejected', { count: rejectedApplications.value.length }), value: 'rejected' },
  { label: t('applications.withdrawn', { count: withdrawnApplications.value.length }), value: 'withdrawn' }
])

const filteredAndSortedApplications = computed(() => {
  let apps = [...applicationsStore.applications]
  if (activeFilter.value === 'pending') {
    apps = apps.filter(app => app.status === 'PENDING' || app.status === 'pending' || app.status === 'UNDER_REVIEW' || app.status === 'under_review')
  } else if (activeFilter.value === 'interview') {
    apps = apps.filter(app => app.status === 'INTERVIEW' || app.status === 'interview')
  } else if (activeFilter.value === 'offer') {
    apps = apps.filter(app => app.status === 'OFFER' || app.status === 'offer')
  } else if (activeFilter.value === 'rejected') {
    apps = apps.filter(app => app.status === 'REJECTED' || app.status === 'rejected')
  } else if (activeFilter.value === 'withdrawn') {
    apps = apps.filter(app => app.status === 'WITHDRAWN' || app.status === 'withdrawn')
  }
  // Sort
  return apps.sort((a, b) => new Date(b.appliedDate) - new Date(a.appliedDate))
})

const getStatusSeverity = (status) => {
  switch (status?.toLowerCase()) {
    case 'pending':
    case 'under_review':
      return 'warning'
    case 'interview':
      return 'info'
    case 'offer':
      return 'success'
    case 'rejected':
      return 'danger'
    case 'withdrawn':
      return 'secondary'
    default:
      return 'secondary'
  }
}

const getTimelineIcon = (type) => {
  switch (type) {
    case 'applied':
      return 'pi pi-send'
    case 'review':
      return 'pi pi-eye'
    case 'interview':
      return 'pi pi-users'
    case 'offer':
      return 'pi pi-check-circle'
    case 'rejected':
      return 'pi pi-times-circle'
    default:
      return 'pi pi-circle'
  }
}

const now = ref(new Date())
let timer = null

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const diffMs = now.value - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHr = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHr / 24);

  if (diffSec < 60) return `${diffSec} second${diffSec !== 1 ? 's' : ''} ago`;
  if (diffMin < 60) return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  if (diffHr < 24) return `${diffHr} hour${diffHr !== 1 ? 's' : ''} ago`;
  if (diffDay === 1) return '1 day ago';
  if (diffDay < 7) return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  if (diffDay < 30) return `${Math.floor(diffDay / 7)} week${Math.floor(diffDay / 7) !== 1 ? 's' : ''} ago`;
  if (diffDay < 365) return `${Math.floor(diffDay / 30)} month${Math.floor(diffDay / 30) !== 1 ? 's' : ''} ago`;
  return `${Math.floor(diffDay / 365)} year${Math.floor(diffDay / 365) !== 1 ? 's' : ''} ago`;
}

const getEmptyStateTitle = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applications.noPendingApplications')
    case 'interview':
      return t('applications.noInterviewInvitations')
    case 'offer':
      return t('applications.noJobOffersYet')
    case 'rejected':
      return t('applications.noRejectedApplications')
    case 'withdrawn':
      return t('applications.noWithdrawnApplications')
    default:
      return t('applications.noApplicationsYet')
  }
}

const getEmptyStateMessage = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applications.allReviewed')
    case 'interview':
      return t('applications.keepApplyingInterviews')
    case 'offer':
      return t('applications.keepApplyingOffers')
    case 'rejected':
      return t('applications.noRejections')
    case 'withdrawn':
      return t('applications.withdrawnApplications')
    default:
      return t('applications.startApplying')
  }
}

const searchJobs = () => {
  router.push('/jobseeker/jobs')
}

const viewJobDetails = (application) => {
  const jobId = application.job?.id || application.jobId;
  if (jobId) {
    router.push(`/jobseeker/jobs/${jobId}`);
  } else {
    showToast({
      severity: 'info',
      summary: 'Job Not Found',
      detail: 'This job is no longer available.'
    });
  }
}

const withdrawApplication = async (application) => {
  const confirmed = await showToast({
    severity: 'info',
    summary: 'Withdrawn Application',
    detail: t('applications.withdrawConfirm', { job: application.jobTitle }),
    confirmText: 'Yes',
    cancelText: t('common.cancel') || 'No'
  });
  if (confirmed) {
    try {
      await applicationsService.withdraw(application.id)
      await applicationsStore.fetchApplications()
      showToast({
        severity: 'success',
        summary: 'Success',
        detail: 'Your application has been withdrawn successfully'
      });
      console.log('Applications in store:', applicationsStore.applications)
    } catch (error) {
      showToast({
        severity: 'danger',
        summary: 'Error',
        detail: error.message || 'Failed to withdraw application'
      });
    }
  }
}

const applyToJob = async (job) => {
  if (hasApplied(job.id)) return;
  await applicationsStore.addApplication(job)
  await applicationsStore.fetchApplications()
  showToast({
    severity: 'success',
    summary: 'Application Submitted',
    detail: `Your application for "${job.title}" has been submitted successfully!`
  });
}

const hasApplied = (jobId) => {
  const app = applicationsStore.applications.find(app => app && app.jobId === jobId)
  return app && (app.status === 'APPLIED' || app.status === 'PENDING')
}

const getCountdown = (dateString) => {
  const now = new Date();
  const interviewDate = new Date(dateString);
  const diff = interviewDate - now;
  if (diff <= 0) return 'Started';
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff / (1000 * 60)) % 60);
  return `${hours}h ${minutes}m`;
};

// Track which interview details are open
const openInterviewDetails = ref({})

const toggleInterviewDetails = (id) => {
  openInterviewDetails.value[id] = !openInterviewDetails.value[id]
}

onMounted(async () => {
  isLoading.value = true;
  await applicationsStore.fetchApplications()
  isLoading.value = false;
  // timer = setInterval(() => {
  //   now.value = new Date()
  // }, 30000) // update every 30 seconds
})

// onActivated(() => {
//   applicationsStore.fetchApplications()
//   if (!timer) {
//     timer = setInterval(() => {
//       now.value = new Date()
//     }, 30000)
//   }
// })

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<template>
  <div class="applications-page flex-1 overflow-x-hidden overflow-y-auto">
    <div class="applications-content">

      <!-- Filter Controls -->
      <div class="filter-sort-controls filter-right sticky top-0 z-10">
        <label class="font-bold">{{ t('QuickFilter') }}</label>
        <Select :loading="isLoading" v-model="activeFilter" :options="filterOptions" optionLabel="label" optionValue="value"
          class="filter-dropdown" style="min-width: 200px;" />
      </div>

      <!-- Applications List -->
      <div class="applications-list p-4" v-if="filteredAndSortedApplications.length > 0">
        <div v-for="application in filteredAndSortedApplications" :key="application.id" class="application-card">
          <div class="application-header">
            <div class="job-info">
              <!-- <div class="job-avatar">
                <Avatar :label="(application.company && application.company.length > 0) ? application.company.charAt(0).toUpperCase() : (application.jobTitle ? application.jobTitle.charAt(0).toUpperCase() : '?')"
                  :style="{ backgroundColor: application.color }" shape="circle" size="large" />
              </div> -->
              <div class="job-details">
                <h3 class="job-title">{{ application.jobTitle }}</h3>
                <p class="job-company">
                  {{ application.job?.company?.name || application.job?.contactPerson || application.job?.employer?.email || 'No Company Name' }}
                </p>
                <p class="job-location" v-if="application.location">
                  <i class="pi pi-map-marker"></i>
                  {{ application.location }}
                </p>
              </div>
            </div>
            <div class="application-status">
              <Tag :value="application.status" :severity="getStatusSeverity(application.status)" class="status-tag" />
              <span class="application-date">
                Applied {{ formatDate(application.createdAt) }}
              </span>
            </div>
          </div>

          <div class="application-content">
            <div class="application-timeline">
              <div class="timeline-item active">
                <div class="timeline-icon">
                  <i class="pi pi-send"></i>
                </div>
                <div class="timeline-content">
                  <h4>{{ t('applications.applicationSubmitted') }}</h4>
                  <p>{{ t('applications.applicationSubmittedDesc') }}</p>
                  <span class="timeline-date">{{ formatDate(application.appliedDate) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div v-if="application.status === 'INTERVIEW_SCHEDULED'">
            <Button
              :label="openInterviewDetails[application.id] ? 'Hide Interview Details' : 'Show Interview Details'"
              icon="pi pi-calendar"
              class="p-button-info p-button-sm"
              @click="toggleInterviewDetails(application.id)"
              style="margin-bottom: 0.5rem;"
            />
            <div v-if="openInterviewDetails[application.id]" class="interview-details">
              <div class="interview-header">
                <h4 style="margin: 0; display: flex; align-items: center;">
                  <i class="pi pi-calendar" style="font-size: 1.2rem; color: #2196f3; margin-right: 0.5rem;"></i>
                  Interview Scheduled
                </h4>
              </div>
              <div class="interview-info">
                <p v-if="application.interviewDate">
                  <i class="pi pi-calendar" style="color: #2196f3; margin-right: 0.5rem;"></i>
                  <strong>Date:</strong> {{ new Date(application.interviewDate).toLocaleString() }}
                </p>
                <p v-if="application.interviewLocation">
                  <i class="pi pi-map-marker" style="color: #2196f3; margin-right: 0.5rem;"></i>
                  <strong>Location:</strong> {{ application.interviewLocation }}
                </p>
                <p v-if="application.interviewNotes">
                  <i class="pi pi-info-circle" style="color: #2196f3; margin-right: 0.5rem;"></i>
                  <strong>Notes:</strong> {{ application.interviewNotes }}
                </p>
              </div>
              <Button
                v-if="application.interviewLink"
                :label="'Join Interview'"
                icon="pi pi-video"
                class="p-button-success"
                @click="window.open(application.interviewLink, '_blank')"
              />
            </div>
          </div>

          <div class="application-actions flex justify-between">
            <div></div>
            <div >
              <Button @click="viewJobDetails(application)" icon="pi pi-eye" :label="t('applications.viewJob')"
                size="large" severity="info" outlined raised class="mr-4"/>
              <Button @click="withdrawApplication(application)" icon="pi pi-times" :label="t('applications.withdraw')"
                 size="large" severity="danger" outlined raised
                v-if="application.status === 'PENDING' || application.status === 'UNDER_REVIEW'" />
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="!isLoading" class="empty-state">
        <i class="pi pi-send empty-icon"></i>
        <h3>{{ getEmptyStateTitle() }}</h3>
        <p>{{ getEmptyStateMessage() }}</p>
        <Button @click="searchJobs" icon="pi pi-search" :label="t('applications.findJobsToApply')"
          v-if="activeFilter === 'all'" />
      </div>

      <LoadingSkelton v-if="isLoading" />
      <!-- Loading State -->
      <!-- <div v-if="isLoading" class="loading-state">
        <i class="pi pi-spin pi-spinner loading-icon"></i>
        <p>{{ t('applications.loadingApplications') }}</p>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.applications-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.applications-content {
  margin: 0 auto;
  /* padding: 0 1rem 1rem; */
}

.page-header {
  margin-bottom: 2rem;
}

.header-content p {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.filter-sort-controls {
  display: flex;
  justify-content: space-between;
}

.filter-dropdown {
  min-width: 200px;
}

.filter-dropdown::-webkit-scrollbar {
  display: none;
}

.filter-dropdown-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.filter-dropdown {
  display: flex;
  flex-wrap: nowrap;
  min-width: max-content;
  border-bottom: 1px solid var(--surface-border);
  overflow-x: auto;
}

.filter-dropdown-item {
  flex-shrink: 0;
  padding: 1rem 1.5rem !important;
  border-bottom: 2px solid transparent !important;
  color: var(--text-color-secondary) !important;
  font-weight: 500 !important;
  white-space: nowrap;
  transition: all var(--transition-duration) ease !important;
}

.filter-dropdown-item:hover {
  color: var(--primary-color) !important;
  background: var(--surface-hover) !important;
}

.filter-dropdown-item.active {
  color: var(--primary-color) !important;
  border-bottom-color: var(--primary-color) !important;
  background: var(--highlight-bg) !important;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.application-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all var(--transition-duration) ease;
}

.application-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.job-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.job-details {
  flex: 1;
}

.job-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.job-company {
  color: var(--primary-color);
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.job-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.job-location i {
  color: var(--primary-color);
}

.application-status {
  text-align: right;
}

.application-date {
  /* font-size: 0.8rem; */
  color: var(--text-color-secondary);
}

.application-timeline {
  margin-bottom: 1.5rem;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  opacity: 0.6;
  transition: opacity var(--transition-duration) ease;
}

.timeline-item.active {
  opacity: 1;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--surface-200);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  flex-shrink: 0;
}

.timeline-item.active .timeline-icon {
  background: var(--primary-color);
  color: white;
}
    .filter-sort-controls {
      background: var(--surface-200);
    }

.timeline-content {
  flex: 1;
}

.timeline-content h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1.2rem;
  font-weight: 600;
}

.timeline-content p {
  margin: 0 0 0.25rem 0;
  color: var(--text-color-secondary);
  /* font-size: 0.85rem; */
}

.timeline-date {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

.application-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon,
.loading-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

.interview-details {
  background: #f6faff;
  border-left: 4px solid #2196f3;
  padding: 1rem;
  margin-top: 1rem;
  border-radius: 8px;
}

.interview-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
.interview-info p {
  margin: 0.25rem 0;
}

@media (max-width: 768px) {
  /* .applications-content {
    padding: 0.2rem;
  } */
  .header-content h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  /* .filter-dropdown {
    width: 100%;
    margin-bottom: 1rem;
  } */

  .application-header {
    flex-direction: column;
    /* align-items: flex-start; */
  }

  .application-status {
    text-align: left;
    width: 100%;
  }

  .application-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

.filter-sort-controls.filter-right {
  justify-content: flex-end;
  padding: 1rem;
}
</style>