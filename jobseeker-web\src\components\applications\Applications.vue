<script setup>
import { ref, computed, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import { useApplicationsStore } from '@/stores/applications'
import { applicationsService } from '@/api/services/applicationsService'
import alertManager from '@/utils/alertManager'
import Select from 'primevue/select'

const router = useRouter()
const { t } = useI18n()
const isLoading = ref(false)
const activeFilter = ref('all')
const applicationsStore = useApplicationsStore()

// Computed filters
const pendingApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'PENDING' || app.status === 'pending' || app.status === 'UNDER_REVIEW' || app.status === 'under_review'))
)

const interviewApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'INTERVIEW' || app.status === 'interview'))
)

const offerApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'OFFER' || app.status === 'offer'))
)

const rejectedApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'REJECTED' || app.status === 'rejected'))
)

const withdrawnApplications = computed(() =>
  applicationsStore.applications.filter(app => app && (app.status === 'WITHDRAWN' || app.status === 'withdrawn'))
)

const filterOptions = computed(() => [
  { label: t('applications.allApplications', { count: applicationsStore.applications?.length || 0 }), value: 'all' },
  { label: t('applications.pending', { count: pendingApplications.value.length }), value: 'pending' },
  { label: t('applications.interview', { count: interviewApplications.value.length }), value: 'interview' },
  { label: t('applications.offers', { count: offerApplications.value.length }), value: 'offer' },
  { label: t('applications.rejected', { count: rejectedApplications.value.length }), value: 'rejected' },
  { label: t('applications.withdrawn', { count: withdrawnApplications.value.length }), value: 'withdrawn' }
])

const filteredAndSortedApplications = computed(() => {
  let apps = [...applicationsStore.applications]
  if (activeFilter.value === 'pending') {
    apps = apps.filter(app => app.status === 'PENDING' || app.status === 'pending' || app.status === 'UNDER_REVIEW' || app.status === 'under_review')
  } else if (activeFilter.value === 'interview') {
    apps = apps.filter(app => app.status === 'INTERVIEW' || app.status === 'interview')
  } else if (activeFilter.value === 'offer') {
    apps = apps.filter(app => app.status === 'OFFER' || app.status === 'offer')
  } else if (activeFilter.value === 'rejected') {
    apps = apps.filter(app => app.status === 'REJECTED' || app.status === 'rejected')
  } else if (activeFilter.value === 'withdrawn') {
    apps = apps.filter(app => app.status === 'WITHDRAWN' || app.status === 'withdrawn')
  }
  // Sort
  return apps.sort((a, b) => new Date(b.appliedDate) - new Date(a.appliedDate))
})

const getStatusSeverity = (status) => {
  switch (status?.toLowerCase()) {
    case 'pending':
    case 'under_review':
      return 'warning'
    case 'interview':
      return 'info'
    case 'offer':
      return 'success'
    case 'rejected':
      return 'danger'
    case 'withdrawn':
      return 'secondary'
    default:
      return 'secondary'
  }
}

const getTimelineIcon = (type) => {
  switch (type) {
    case 'applied':
      return 'pi pi-send'
    case 'review':
      return 'pi pi-eye'
    case 'interview':
      return 'pi pi-users'
    case 'offer':
      return 'pi pi-check-circle'
    case 'rejected':
      return 'pi pi-times-circle'
    default:
      return 'pi pi-circle'
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) return '1 day ago'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  return `${Math.floor(diffDays / 30)} months ago`
}

const getEmptyStateTitle = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applications.noPendingApplications')
    case 'interview':
      return t('applications.noInterviewInvitations')
    case 'offer':
      return t('applications.noJobOffersYet')
    case 'rejected':
      return t('applications.noRejectedApplications')
    case 'withdrawn':
      return t('applications.noWithdrawnApplications')
    default:
      return t('applications.noApplicationsYet')
  }
}

const getEmptyStateMessage = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applications.allReviewed')
    case 'interview':
      return t('applications.keepApplyingInterviews')
    case 'offer':
      return t('applications.keepApplyingOffers')
    case 'rejected':
      return t('applications.noRejections')
    case 'withdrawn':
      return t('applications.withdrawnApplications')
    default:
      return t('applications.startApplying')
  }
}

const searchJobs = () => {
  router.push('/jobseeker/jobs')
}

const viewJobDetails = (application) => {
  if (application.jobId) {
    router.push(`/jobseeker/jobs/${application.jobId}`)
  } else {
    alertManager.showError('Job not found', 'This job is no longer available.')
  }
}

const withdrawApplication = async (application) => {
  const confirmed = await alertManager.showConfirm(
    t('Withdrawn Application') || 'Withdraw Application',
    t('applications.withdrawConfirm', { job: application.jobTitle }),
    { confirmText: 'Yes', cancelText: t('common.cancel') || 'No' }
  );
  if (confirmed) {
    try {
      await applicationsService.withdraw(application.id)
      await applicationsStore.fetchApplications()
      alertManager.showSuccess('Application Withdrawn', 'Your application has been withdrawn successfully')
      console.log('Applications in store:', applicationsStore.applications)
    } catch (error) {
      alert(error.message || 'Failed to withdraw application')
    }
  }
}

const applyToJob = async (job) => {
  if (hasApplied(job.id)) return;
  await applicationsStore.addApplication(job)
  await applicationsStore.fetchApplications()
  alertManager.showSuccess('Application Submitted', `Your application for "${job.title}" has been submitted successfully!`)
}

const hasApplied = (jobId) => {
  const app = applicationsStore.applications.find(app => app && app.jobId === jobId)
  return app && (app.status === 'APPLIED' || app.status === 'PENDING')
}

onMounted(() => {
  applicationsStore.fetchApplications()
})

onActivated(() => {
  applicationsStore.fetchApplications()
})
</script>

<template>
  <div class="applications-page flex-1 overflow-x-hidden overflow-y-auto">
    <div class="applications-content">

      <!-- Filter Controls -->
      <div class="filter-sort-controls filter-right">
        <Select v-model="activeFilter" :options="filterOptions" optionLabel="label" optionValue="value"
          class="filter-dropdown" style="min-width: 200px;" />
      </div>

      <!-- Applications List -->
      <div class="applications-list" v-if="filteredAndSortedApplications.length > 0">
        <div v-for="application in filteredAndSortedApplications" :key="application.id" class="application-card">
          <div class="application-header">
            <div class="job-info">
              <div class="job-avatar">
                <Avatar :label="application.company?.charAt(0).toUpperCase()"
                  :style="{ backgroundColor: application.color }" shape="circle" size="large" />
              </div>
              <div class="job-details">
                <h3 class="job-title">{{ application.jobTitle }}</h3>
                <p class="job-company">{{ application.company }}</p>
                <p class="job-location" v-if="application.location">
                  <i class="pi pi-map-marker"></i>
                  {{ application.location }}
                </p>
              </div>
            </div>
            <div class="application-status">
              <Tag :value="application.status" :severity="getStatusSeverity(application.status)" class="status-tag" />
              <span class="application-date">
                {{ t('applications.appliedAgo', { time: formatDate(application.appliedDate) }) }}
              </span>
            </div>
          </div>

          <div class="application-content">
            <div class="application-timeline">
              <div class="timeline-item active">
                <div class="timeline-icon">
                  <i class="pi pi-send"></i>
                </div>
                <div class="timeline-content">
                  <h4>{{ t('applications.applicationSubmitted') }}</h4>
                  <p>{{ t('applications.applicationSubmittedDesc') }}</p>
                  <span class="timeline-date">{{ formatDate(application.appliedDate) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="application-actions">
            <Button @click="viewJobDetails(application)" icon="pi pi-eye" :label="t('applications.viewJob')" text
              size="small" />
            <Button @click="withdrawApplication(application)" icon="pi pi-times" :label="t('applications.withdraw')"
              text size="small" severity="danger"
              v-if="application.status === 'PENDING' || application.status === 'UNDER_REVIEW'" />
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="empty-state">
        <i class="pi pi-send empty-icon"></i>
        <h3>{{ getEmptyStateTitle() }}</h3>
        <p>{{ getEmptyStateMessage() }}</p>
        <Button @click="searchJobs" icon="pi pi-search" :label="t('applications.findJobsToApply')"
          v-if="activeFilter === 'all'" />
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <i class="pi pi-spin pi-spinner loading-icon"></i>
        <p>{{ t('applications.loadingApplications') }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.applications-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.applications-content {
  margin: 0 auto;
  padding: 1rem;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content p {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.filter-sort-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-dropdown {
  min-width: 200px;
}

.filter-dropdown::-webkit-scrollbar {
  display: none;
}

.filter-dropdown-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.filter-dropdown {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.5rem;
  min-width: max-content;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--surface-border);
  overflow-x: auto;
}

.filter-dropdown-item {
  flex-shrink: 0;
  padding: 1rem 1.5rem !important;
  border-bottom: 2px solid transparent !important;
  color: var(--text-color-secondary) !important;
  font-weight: 500 !important;
  white-space: nowrap;
  transition: all var(--transition-duration) ease !important;
}

.filter-dropdown-item:hover {
  color: var(--primary-color) !important;
  background: var(--surface-hover) !important;
}

.filter-dropdown-item.active {
  color: var(--primary-color) !important;
  border-bottom-color: var(--primary-color) !important;
  background: var(--highlight-bg) !important;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.application-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all var(--transition-duration) ease;
}

.application-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.job-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.job-details {
  flex: 1;
}

.job-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.job-company {
  color: var(--primary-color);
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.job-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.job-location i {
  color: var(--primary-color);
}

.application-status {
  text-align: right;
}

.status-tag {
  margin-right: 0.5rem;
}

.application-date {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.application-timeline {
  margin-bottom: 1.5rem;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  opacity: 0.6;
  transition: opacity var(--transition-duration) ease;
}

.timeline-item.active {
  opacity: 1;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--surface-200);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  flex-shrink: 0;
}

.timeline-item.active .timeline-icon {
  background: var(--primary-color);
  color: white;
}

.timeline-content {
  flex: 1;
}

.timeline-content h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 0.95rem;
  font-weight: 600;
}

.timeline-content p {
  margin: 0 0 0.25rem 0;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
}

.timeline-date {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

.application-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon,
.loading-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .applications-content {
    padding: 0.2rem;
  }
  .header-content h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-sort-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-dropdown {
    width: 100%;
    margin-bottom: 1rem;
  }

  .application-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .application-status {
    text-align: left;
    width: 100%;
  }

  .application-actions {
    flex-wrap: wrap;
  }
  .p-tag, .skill-tag, .job-badges .p-tag {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    margin-bottom: 0.25rem !important;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

.filter-sort-controls.filter-right {
  justify-content: flex-end;
}
</style>