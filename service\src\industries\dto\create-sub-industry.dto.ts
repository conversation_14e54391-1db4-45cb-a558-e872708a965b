import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, Matches, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateSubIndustryDto {
  @ApiProperty({
    description: 'Sub-industry name',
    example: 'Software Development',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @Transform(({ value }) => value?.trim())
  @Matches(/^[a-zA-Z0-9\s-]+$/, {
    message: 'Sub-industry name can only contain letters, numbers, spaces, and hyphens',
  })
  name: string;

  @ApiProperty({
    description: 'Sub-industry description',
    required: false,
    example: 'Software development services including web, mobile, and desktop applications',
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.trim())
  description?: string;

  @ApiProperty({
    description: 'Parent industry ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  industryId: string;
}
