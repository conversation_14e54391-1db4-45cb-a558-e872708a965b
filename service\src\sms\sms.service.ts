import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as twilio from 'twilio';

@Injectable()
export class SmsService {
  private readonly twilioClient: twilio.Twilio;
  private readonly verifyServiceSid: string;
  private readonly logger = new Logger(SmsService.name);

  constructor(private readonly configService: ConfigService) {
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
    this.verifyServiceSid = this.configService.get<string>('TWILIO_VERIFY_SERVICE_SID');

    if (!accountSid || !authToken || !this.verifyServiceSid) {
      this.logger.error('Twilio configuration is incomplete');
      throw new Error(
        'Twilio configuration is incomplete. Please check your environment variables.',
      );
    }

    this.twilioClient = twilio(accountSid, authToken);
    this.logger.log('SMS service initialized successfully');
  }

  private validatePhoneNumber(phoneNumber: string): void {
    // Basic E.164 format validation
    if (!phoneNumber.startsWith('+')) {
      throw new BadRequestException('Phone number must start with country code (e.g., +1)');
    }

    // Remove any spaces or special characters
    const cleanNumber = phoneNumber.replace(/[^0-9+]/g, '');

    // Check length (minimum 8 digits, maximum 15 digits)
    if (cleanNumber.length < 8 || cleanNumber.length > 15) {
      throw new BadRequestException('Invalid phone number length');
    }

    // Check if it's a valid number format
    if (!/^\+[1-9]\d{1,14}$/.test(cleanNumber)) {
      throw new BadRequestException('Invalid phone number format');
    }
  }

  async sendOtpSms(phoneNumber: string, otp: string): Promise<void> {
    try {
      this.validatePhoneNumber(phoneNumber);
      this.logger.debug(`Attempting to send verification code to ${phoneNumber}`);

      const verification = await this.twilioClient.verify.v2
        .services(this.verifyServiceSid)
        .verifications.create({ to: phoneNumber, channel: 'sms' });

      this.logger.log(`Verification sent successfully. Status: ${verification.status}`);
    } catch (error) {
      this.logger.error(`Failed to send verification: ${error.message}`, error.stack);

      if (error.code === 60200) {
        throw new BadRequestException(
          'Invalid phone number format. Please provide a valid phone number.',
        );
      } else if (error.code === 60203) {
        throw new BadRequestException(
          'The phone number is not verified. Please verify your number first.',
        );
      } else if (error.code === 60212) {
        throw new BadRequestException('Too many attempts. Please try again later.');
      } else if (error.code === 60214) {
        throw new BadRequestException(
          'Phone number is not mobile. Please provide a mobile number.',
        );
      } else {
        this.logger.error(`Unexpected Twilio error: ${error.code} - ${error.message}`);
        throw new BadRequestException(
          `Failed to send verification: ${error.message || 'Please try again later.'}`,
        );
      }
    }
  }

  async resendOtp(phoneNumber: string): Promise<void> {
    try {
      this.validatePhoneNumber(phoneNumber);
      this.logger.debug(`Attempting to resend verification code to ${phoneNumber}`);

      const verification = await this.twilioClient.verify.v2
        .services(this.verifyServiceSid)
        .verifications.create({ to: phoneNumber, channel: 'sms' });

      this.logger.log(`Verification resent successfully. Status: ${verification.status}`);
    } catch (error) {
      this.logger.error(`Failed to resend verification: ${error.message}`, error.stack);
      throw new BadRequestException(
        `Failed to resend verification: ${error.message || 'Please try again later.'}`,
      );
    }
  }

  async verifyOtp(phoneNumber: string, otp: string): Promise<boolean> {
    try {
      this.validatePhoneNumber(phoneNumber);
      this.logger.debug(`Verifying code for ${phoneNumber}`);

      const verificationCheck = await this.twilioClient.verify.v2
        .services(this.verifyServiceSid)
        .verificationChecks.create({ to: phoneNumber, code: otp });

      this.logger.log(`Verification check status: ${verificationCheck.status}`);
      return verificationCheck.status === 'approved';
    } catch (error) {
      this.logger.error(`Failed to verify code: ${error.message}`, error.stack);

      if (error.code === 60200) {
        throw new BadRequestException('Invalid verification code format');
      } else if (error.code === 60202) {
        throw new BadRequestException('Verification code expired');
      } else if (error.code === 60204) {
        throw new BadRequestException('Invalid verification code');
      } else {
        throw new BadRequestException(
          `Failed to verify code: ${error.message || 'Please try again.'}`,
        );
      }
    }
  }
}
