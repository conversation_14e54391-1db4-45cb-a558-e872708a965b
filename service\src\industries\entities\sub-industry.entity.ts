import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { IndustryEntity } from './industry.entity';
import { JobEntity } from '../../jobs/entities/job.entity';

@Entity('sub_industries')
export class SubIndustryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: true })
  isActive: boolean;

  @Column()
  industryId: string;

  @ManyToOne(() => IndustryEntity, (industry) => industry.subIndustries)
  @JoinColumn({ name: 'industryId' })
  industry: IndustryEntity;

  @OneToMany(() => JobEntity, (job) => job.subIndustry)
  jobs: JobEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
