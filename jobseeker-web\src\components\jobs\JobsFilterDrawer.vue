<script setup>
import { ref } from 'vue'
import Drawer from 'primevue/drawer';
import Button from 'primevue/button';
import JobsFilter from './JobsFilter.vue';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  filters: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'update:filters', 'clear-filters'])

const handleFiltersUpdate = (newFilters) => {
  emit('update:filters', newFilters)
}

const handleClearFilters = () => {
  emit('clear-filters')
}

const closeDrawer = () => {
  emit('update:visible', false)
}

const handleDrawerHide = () => {
  emit('update:visible', false)
}
</script>
<template>
    <Drawer 
        :visible="visible" 
        position="right" 
        :modal="true"
        :dismissableMask="true"
        :showCloseIcon="false"
        @hide="handleDrawerHide"
        class="mobile-filter-drawer"
        :style="{ width: '90vw', maxWidth: '400px' }"
    >
        <template #header>
            <div class="drawer-header flex justify-between items-center">
                <h3>Filters</h3>
                <Button 
                icon="pi pi-times" 
                @click="closeDrawer" 
                text 
                rounded 
                class="close-btn p-button-sm" 
                aria-label="Close" 
                />
            </div>
        </template>
        
        <div class="drawer-content">
            <JobsFilter 
                :filters="filters" 
                @update:filters="handleFiltersUpdate" 
                @clear-filters="handleClearFilters"
            />
        </div>
        
        <template #footer>
            <div class="drawer-footer">
                <Button 
                    @click="handleClearFilters" 
                    outlined 
                    :label="'Clear All'" 
                    icon="pi pi-filter-slash" 
                    class="clear-btn"
                />
                <Button 
                    @click="closeDrawer" 
                    :label="'Apply Filters'" 
                    icon="pi pi-check" 
                    class="apply-filters-btn"
                />
            </div>
        </template>
    </Drawer>
</template>

<style scoped>
.drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.drawer-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-color-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: var(--surface-100);
    color: var(--text-color);
}

.drawer-footer {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-top: 1px solid var(--surface-border);
}

.clear-btn {
    flex: 1;
}

.apply-filters-btn {
    flex: 2;
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.mobile-filter-drawer {
    width: 90vw !important;
    max-width: 400px !important;
}

@media (max-width: 480px) {
    .mobile-filter-drawer {
        width: 95vw !important;
    }
    
    .drawer-footer {
        flex-direction: column;
    }
    
    .clear-btn,
    .apply-filters-btn {
        flex: none;
    }
}
</style>