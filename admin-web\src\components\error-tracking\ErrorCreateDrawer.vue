<script setup>
import { ref, reactive, watch, computed } from 'vue';
import Drawer from 'primevue/drawer';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
import Textarea from 'primevue/textarea';

const props = defineProps({
  visible: <PERSON><PERSON>an,
  errorLog: Object // null for create, object for edit
});
const emit = defineEmits(['close', 'success', 'update']);

const isEditMode = computed(() => !!props.errorLog && !!props.errorLog.id);

const form = reactive({
  errorMessage: '',
  screen: '',
  userAgent: '',
  url: '',
  metadata: '', // JSON string
  severity: null
});

watch(() => props.errorLog, (val) => {
  if (val) {
    form.errorMessage = val.errorMessage || '';
    form.screen = val.screen || '';
    form.userAgent = val.userAgent || '';
    form.url = val.url || '';
    form.severity = val.severity || null;
    form.metadata = val.metadata ? JSON.stringify(val.metadata, null, 2) : '';
  } else {
    form.errorMessage = '';
    form.screen = '';
    form.userAgent = '';
    form.url = '';
    form.severity = null;
    form.metadata = '';
  }
}, { immediate: true });

const severityOptions = [
  { label: 'Low', value: 'LOW' },
  { label: 'Medium', value: 'MEDIUM' },
  { label: 'High', value: 'HIGH' },
  { label: 'Critical', value: 'CRITICAL' }
];

function handleClose() {
  emit('close');
}

function handleSubmit() {
  if (!form.errorMessage.trim()) return;
  let parsedMetadata = undefined;
  if (form.metadata.trim()) {
    try {
      parsedMetadata = JSON.parse(form.metadata);
    } catch (e) {
      alert('Metadata must be valid JSON');
      return;
    }
  }
  const payload = {
    ...form,
    metadata: parsedMetadata
  };
  if (isEditMode.value) {
    emit('update', { id: props.errorLog.id, ...payload });
  } else {
    emit('success', payload);
  }
  handleClose();
}
</script>

<template>
  <Drawer :visible="visible" @update:visible="handleClose" position="right" style="width: 400px">
    <template #header>
      <h3>{{ isEditMode ? 'Edit Error Log' : 'Log New Error' }}</h3>
    </template>
    <form class="p-4 flex flex-col gap-4" @submit.prevent="handleSubmit">
      <div>
        <label class="font-semibold">Error Message *</label>
        <InputText v-model="form.errorMessage" class="w-full mt-1" required placeholder="Describe the error..." />
      </div>
      <div>
        <label class="font-semibold">Screen</label>
        <InputText v-model="form.screen" class="w-full mt-1" placeholder="e.g. UserProfile" />
      </div>
      <div>
        <label class="font-semibold">User Agent</label>
        <InputText v-model="form.userAgent" class="w-full mt-1" placeholder="Browser info" />
      </div>
      <div>
        <label class="font-semibold">URL</label>
        <InputText v-model="form.url" class="w-full mt-1" placeholder="https://example.com/page" />
      </div>
      <div>
        <label class="font-semibold">Severity</label>
        <Select v-model="form.severity" :options="severityOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select severity" />
      </div>
      <div>
        <label class="font-semibold">Metadata (JSON)</label>
        <Textarea v-model="form.metadata" class="w-full mt-1" rows="3" placeholder='{"key": "value"}' />
      </div>
      <div class="flex justify-end gap-2 mt-4">
        <Button label="Cancel" severity="secondary" @click="handleClose" type="button" />
        <Button :label="isEditMode ? 'Update' : 'Create'" type="submit" :disabled="!form.errorMessage.trim()" />
      </div>
    </form>
  </Drawer>
</template> 