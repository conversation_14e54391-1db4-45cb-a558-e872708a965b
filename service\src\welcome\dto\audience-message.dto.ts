import { ApiProperty, ApiSchema } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

@ApiSchema({ name: 'CreateAudienceMessage' })
export class CreateAudienceMessageDto {
  @ApiProperty({
    description: 'The message content',
    example: 'Join our community of professionals',
  })
  @IsString()
  @IsNotEmpty()
  message: string;
}

@ApiSchema({ name: 'UpdateAudienceMessage' })
export class UpdateAudienceMessageDto {
  @ApiProperty({
    description: 'The message content',
    example: 'Join our community of professionals',
    required: false,
  })
  @IsString()
  @IsOptional()
  message?: string;
}

@ApiSchema({ name: 'AudienceMessageResponse' })
export class AudienceMessageResponseDto {
  @ApiProperty({ description: 'Audience message ID' })
  id: string;

  @ApiProperty({ description: 'Audience message content' })
  message: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
