<script setup>
import { computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'
import Tag from 'primevue/tag';

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// Computed properties for user display
const userFullName = computed(() => {
  const user = authStore.user
  if (!user) return ''

  // Try to get name from profile first
  if (user.profile?.firstName || user.profile?.lastName) {
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }

  // Fallback to user.name if available
  if (user.name) {
    return user.name
  }

  // Fallback to user.firstName and user.lastName if available
  if (user.firstName || user.lastName) {
    const firstName = user.firstName || ''
    const lastName = user.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }

  return ''
})

const welcomeMessage = computed(() => {
  const name = userFullName.value
  if (name) {
    return t('dashboard.welcomeBack', { name })
  }
  return t('dashboard.welcomeBack', { name: t('profile.employer') })
})

const userLocation = computed(() => {
  const user = authStore.user
  if (!user) return ''

  // Try to get location from profile
  if (user.profile?.city && user.profile?.state) {
    return `${user.profile.city}, ${user.profile.state}`
  }

  if (user.profile?.city) {
    return user.profile.city
  }

  // Fallback to user.location if available
  return user.location || ''
})

// Get data from dashboard store
const recommendedJobs = computed(() =>
  dashboardStore.stats.recommendedJobs || []
)

const recentActivity = computed(() =>
dashboardStore.stats.recentActivity || []
)

const formatActivityTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMinutes = Math.floor((now - date) / (1000 * 60))

  if (diffMinutes < 1) return t('time.justNow')
  if (diffMinutes < 60) return t('time.minutesAgo', { count: diffMinutes })

  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return t('time.hoursAgo', { count: diffHours })

  const diffDays = Math.floor(diffHours / 24)
  return t('time.daysAgo', { count: diffDays })
}

// Enhanced activity formatting for job actions
const getActivityAction = (activity) => {
  // Check if job is deleted
  if (activity.isDeleted === true || activity.isDeleted === 1) {
    return 'deleted'
  }
  // Check if this is a new job (created recently)
  if (activity.action === 'created' || activity.action === 'added') {
    return 'added'
  }
  // Check if this is an updated job
  if (activity.action === 'updated' || activity.action === 'modified') {
    return 'updated'
  }
  // Fallback: determine based on timestamps or other logic
  const createdAt = new Date(activity.createdAt)
  const updatedAt = new Date(activity.updatedAt)
  // If updatedAt is significantly different from createdAt, it was updated
  if (updatedAt.getTime() - createdAt.getTime() > 60000) { // 1 minute difference
    return 'updated'
  }
  // Default to added for new jobs
  return 'added'
}

const getActivityIcon = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return 'pi pi-plus-circle'
    case 'deleted':
    case 'removed':
      return 'pi pi-trash'
    case 'updated':
    case 'modified':
      return 'pi pi-pencil'
    default:
      return 'pi pi-briefcase'
  }
}

const getActivityColor = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return '#10B981' // Green
    case 'deleted':
    case 'removed':
      return '#EF4444' // Red
    case 'updated':
    case 'modified':
      return '#3B82F6' // Blue
    default:
      return 'var(--primary-color)'
  }
}

const getActivityBgColor = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return 'rgba(16, 185, 129, 0.1)' // Light green
    case 'deleted':
    case 'removed':
      return 'rgba(239, 68, 68, 0.1)' // Light red
    case 'updated':
    case 'modified':
      return 'rgba(59, 130, 246, 0.1)' // Light blue
    default:
      return 'var(--surface-ground)'
  }
}

const getActivityText = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return 'Job Added'
    case 'deleted':
    case 'removed':
      return 'Job Deleted'
    case 'updated':
    case 'modified':
      return 'Job Updated'
    default:
      return 'Job Action'
  }
}

const searchJobs = () => {
  router.push('/employer/jobs')
}

const viewJobDetails = (job) => {
  router.push(`/employer/jobs/${job.id}`)
}

const saveJob = (job) => {
  console.log('Save job:', job.title)
  // TODO: Implement save job functionality
}

const applyToJob = (job) => {
  router.push(`/employer/jobs/${job.id}?action=apply`)
}

// Map application status to PrimeVue Tag severity
const statusSeverity = (status) => {
  switch ((status || '').toUpperCase()) {
    case 'PENDING':
      return 'warn'; // Yellow
    case 'REVIEWING':
      return 'info'; // Orange (PrimeVue info is blue, but warn is yellow)
    case 'SHORTLISTED':
      return 'info'; // Blue
    case 'INTERVIEW_SCHEDULED':
      return 'primary'; // or 'info', 'success', etc.
    case 'OFFERED':
      return 'success'; // Green
    case 'REJECTED':
      return 'danger'; // Red
    default:
      return 'secondary';
  }
};

onMounted(() => {
  dashboardStore.fetchStats()
  dashboardStore.startAutoRefresh()
})

onUnmounted(() => {
  dashboardStore.stopAutoRefresh()
})
</script>

<template>
  <div class="dashboard-container flex-1">
    <div class="dashboard-content">

      <!-- Job Seeker Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card primary">
          <div class="stat-icon">
            <i class="pi pi-briefcase"></i>
          </div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.totalJobs || 0 }}</h3>
            <p>Jobs Posted</p>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <i class="pi pi-send"></i>
          </div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.totalApplications || 0 }}</h3>
            <p>Applications Received</p>
          </div>
        </div>

      <!--  <div class="stat-card warning">
          <div class="stat-icon">
            <i class="pi pi-eye"></i>
          </div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.jobViews || 0 }}</h3>
            <p>Job Views</p>
          </div>
        </div>-->
      </div>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Applications Table -->
        <div class="recommended-jobs-card">
          <div class="card-header">
            <i class="pi pi-users card-icon"></i>
            <h3>Applications Received</h3>
          </div>
          <div class="jobs-list">
            <!-- Desktop Table -->
            <div class="table-responsive applications-table-desktop">
              <table style="width:100%; border-collapse:collapse; min-width: 500px;">
                <thead>
                  <tr>
                    <th style="text-align:left; padding:8px;">Job Title</th>
                    <th style="text-align:left; padding:8px;">Applicant Email</th>
                    <th style="text-align:left; padding:8px;">Application Date</th>
                    <th style="text-align:left; padding:8px;">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="app in dashboardStore.stats.applications || []" :key="app.id">
                    <td style="padding:8px;">{{ app.jobTitle }}</td>
                    <td style="padding:8px;">{{ app.applicantName }}</td>
                    <td style="padding:8px;">{{ new Date(app.applicationDate).toLocaleDateString() }}</td>
                    <td style="padding:8px;">
                      <Tag
                        :value="app.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())"
                        :severity="statusSeverity(app.status)"
                        v-if="app.status.toUpperCase() !== 'INTERVIEW_SCHEDULED'"
                      />
                      <Tag
                        v-else
                        :value="app.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())"
                        style="background: #e9d8fd; color: #6b21a8; border: none;"
                      />
                    </td>
                  </tr>
                  <tr v-if="!dashboardStore.stats.applications || dashboardStore.stats.applications.length === 0">
                    <td colspan="4" style="padding:8px; text-align:center; color:var(--text-color-secondary);">No
                      applications received yet.</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!-- Mobile Card Layout -->
            <div class="applications-cards-mobile">
              <div v-for="app in dashboardStore.stats.applications || []" :key="app.id" class="application-card">
                <div class="application-row"><span class="application-label">Job Title:</span> <span class="application-value">{{ app.jobTitle }}</span></div>
                <div class="application-row"><span class="application-label">Applicant Email:</span> <span class="application-value">{{ app.applicantName }}</span></div>
                <div class="application-row"><span class="application-label">Application Date:</span> <span class="application-value">{{ new Date(app.applicationDate).toLocaleDateString() }}</span></div>
                <div class="application-row">
                  <span class="application-label">Status:</span>
                  <span class="application-value">
                    <Tag
                      :value="app.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())"
                      :severity="statusSeverity(app.status)"
                      v-if="app.status.toUpperCase() !== 'INTERVIEW_SCHEDULED'"
                    />
                    <Tag
                      v-else
                      :value="app.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())"
                      style="background: #e9d8fd; color: #6b21a8; border: none;"
                    />
                  </span>
                </div>
              </div>
              <div v-if="!dashboardStore.stats.applications || dashboardStore.stats.applications.length === 0" class="application-card empty">
                No applications received yet.
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="activity-card">
          <div class="card-header">
            <i class="pi pi-clock card-icon"></i>
            <h3>{{ t('dashboard.recentActivity') }}</h3>
            <span class="last-updated" v-if="dashboardStore.lastUpdated">
              {{ t('dashboard.updated', { time: dashboardStore.getStatsAge() }) }}
            </span>
          </div>
          <!-- Desktop Activity List -->
          <div class="activity-list activity-list-desktop">
            <div v-for="activity in recentActivity" :key="activity.id" class="activity-item"
              :style="{ background: getActivityBgColor(activity) }">
              <div class="activity-icon" :style="{ background: getActivityBgColor(activity) }">
                <i :class="getActivityIcon(activity)" :style="{ color: getActivityColor(activity) }"></i>
              </div>
              <div class="activity-content">
                <p class="activity-title">
                  {{ getActivityText(activity) }}<span v-if="activity.title"> - {{ activity.title }}</span>
                </p>
                <span class="activity-time">{{ formatActivityTime(activity.time) }}</span>
              </div>
            </div>
          </div>
          <!-- Mobile Activity List -->
          <div class="activity-list-mobile">
            <div v-for="activity in recentActivity" :key="activity.id" class="activity-item"
              :style="{ background: getActivityBgColor(activity) }">
              <div class="activity-icon" :style="{ background: getActivityBgColor(activity) }">
                <i :class="getActivityIcon(activity)" :style="{ color: getActivityColor(activity) }"></i>
              </div>
              <div class="activity-content">
                <p class="activity-title">
                  {{ getActivityText(activity) }}<span v-if="activity.title"> - {{ activity.title }}</span>
                </p>
                <span class="activity-time">{{ formatActivityTime(activity.time) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  background: linear-gradient(135deg, var(--dashboard-bg-start) 0%, var(--dashboard-bg-end) 100%);
  transition: background var(--transition-duration) ease;
}

.dashboard-content {
  padding: 1rem;
}

.welcome-section {
  margin-bottom: 2rem;
}

.welcome-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.user-location,
.user-status {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-location i {
  color: var(--primary-color);
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-duration) ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.stat-card.success::before {
  background: var(--green-500);
}

.stat-card.warning::before {
  background: var(--yellow-500);
}

.stat-card.info::before {
  background: var(--blue-500);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0 0 0.5rem 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-change {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.stat-change.positive {
  color: var(--green-500);
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.recommended-jobs-card,
.activity-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.card-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.card-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
  flex: 1;
}

.view-all-btn {
  color: var(--primary-color) !important;
  padding: 0 !important;
}

.last-updated {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 380px;
  overflow-y: auto;
}

.job-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--surface-border);
  cursor: pointer;
  transition: all var(--transition-duration) ease;
}

.job-item:hover {
  border-color: var(--primary-color);
  background: var(--surface-hover);
}

.job-info {
  flex: 1;
  min-width: 0;
}

.job-title {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.job-title:hover {
  color: var(--primary-color);
}

.job-company {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 500;
}

.job-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.job-location,
.job-salary {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.job-location i {
  color: var(--primary-color);
}

.job-salary {
  font-weight: 600;
  color: var(--primary-color);
}

.job-salary i {
  color: var(--primary-color);
}

.job-actions {
  display: flex;
  gap: 0.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 380px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color var(--transition-duration) ease;
}

.activity-item:hover {
  background: var(--surface-hover);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.9rem;
}

.activity-description {
  margin: 0 0 0.25rem 0;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

.activity-description-wrapper {
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: pre-line;
  max-width: 100%;
}

.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
}

.table-responsive table {
  width: 100%;
  max-width: 100%;
}

.table-responsive th,
.table-responsive td {
  overflow-wrap: break-word;
  word-break: break-all;
  white-space: pre-line;
  max-width: 120px;
}

/* Hide/show table and card layout based on screen size */
.applications-table-desktop { display: block; }
.applications-cards-mobile { display: none; }

.activity-list-desktop { display: flex; }
.activity-list-mobile { display: none; }

@media (max-width: 768px) {
  .dashboard-container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  .dashboard-content {
    padding: 1rem;
  }

  .welcome-content h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: 1fr;
  }

  .content-grid {
    grid-template-columns: 1fr;
    overflow-x: auto;
    width: 100%;
    max-width: 100vw;
  }

  .recommended-jobs-card,
  .activity-card {
    padding: 0.5rem;
    border-radius: 5px;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  .table-responsive {
    max-width: 100vw;
    overflow-x: hidden;
  }

  .table-responsive table th,
  .table-responsive table td {
    padding: 2px;
    font-size: 0.75rem;
    max-width: 80px;
  }
  .table-responsive table {
    max-width: 100vw;
  }
  .applications-table-desktop { display: none; }
  .applications-cards-mobile { display: block; }
  .application-card {
    background: var(--surface-card);
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  }
  .application-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
  }
  .application-label {
    font-weight: 600;
    color: var(--text-color-secondary);
    margin-right: 0.5rem;
  }
  .application-value {
    color: var(--text-color);
    word-break: break-all;
    text-align: right;
  }
  .application-card.empty {
    text-align: center;
    color: var(--text-color-secondary);
    font-style: italic;
  }
  .applications-cards-mobile {
    max-height: 350px;
    overflow-y: auto;
  }
  .activity-list-desktop { display: none !important; }
  .activity-list-mobile {
    display: flex !important;
    flex-direction: column;
    gap: 1rem;
    max-height: 350px;
    overflow-y: auto;
  }
}
</style>