<script setup>
import { computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'
import Tag from 'primevue/tag';
import Button from 'primevue/button';
import DataView from 'primevue/dataview';
import Card from 'primevue/card';

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// Computed properties for user display
const userFullName = computed(() => {
  const user = authStore.user
  if (!user) return ''

  // Try to get name from profile first
  if (user.profile?.firstName || user.profile?.lastName) {
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }

  // Fallback to user.name if available
  if (user.name) {
    return user.name
  }

  // Fallback to user.firstName and user.lastName if available
  if (user.firstName || user.lastName) {
    const firstName = user.firstName || ''
    const lastName = user.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }

  return ''
})

const welcomeMessage = computed(() => {
  const name = userFullName.value
  if (name) {
    return t('dashboard.welcomeBack', { name })
  }
  return t('dashboard.welcomeBack', { name: t('profile.employer') })
})

const userLocation = computed(() => {
  const user = authStore.user
  if (!user) return ''

  // Try to get location from profile
  if (user.profile?.city && user.profile?.state) {
    return `${user.profile.city}, ${user.profile.state}`
  }

  if (user.profile?.city) {
    return user.profile.city
  }

  // Fallback to user.location if available
  return user.location || ''
})

// Get data from dashboard store
const recommendedJobs = computed(() =>
  dashboardStore.stats.recommendedJobs || []
)

const recentActivity = computed(() =>
dashboardStore.stats.recentActivity || []
)

const formatActivityTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMinutes = Math.floor((now - date) / (1000 * 60))

  if (diffMinutes < 1) return t('time.justNow')
  if (diffMinutes < 60) return t('time.minutesAgo', { count: diffMinutes })

  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return t('time.hoursAgo', { count: diffHours })

  const diffDays = Math.floor(diffHours / 24)
  return t('time.daysAgo', { count: diffDays })
}

// Enhanced activity formatting for job actions
const getActivityAction = (activity) => {
  // Check if job is deleted
  if (activity.isDeleted === true || activity.isDeleted === 1) {
    return 'deleted'
  }
  // Check if this is a new job (created recently)
  if (activity.action === 'created' || activity.action === 'added') {
    return 'added'
  }
  // Check if this is an updated job
  if (activity.action === 'updated' || activity.action === 'modified') {
    return 'updated'
  }
  // Fallback: determine based on timestamps or other logic
  const createdAt = new Date(activity.createdAt)
  const updatedAt = new Date(activity.updatedAt)
  // If updatedAt is significantly different from createdAt, it was updated
  if (updatedAt.getTime() - createdAt.getTime() > 60000) { // 1 minute difference
    return 'updated'
  }
  // Default to added for new jobs
  return 'added'
}

const getActivityIcon = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return 'pi pi-plus-circle'
    case 'deleted':
    case 'removed':
      return 'pi pi-trash'
    case 'updated':
    case 'modified':
      return 'pi pi-pencil'
    default:
      return 'pi pi-briefcase'
  }
}

const getActivityColor = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return '#10B981' // Green
    case 'deleted':
    case 'removed':
      return '#EF4444' // Red
    case 'updated':
    case 'modified':
      return '#3B82F6' // Blue
    default:
      return 'var(--primary-color)'
  }
}

const getActivityBgColor = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return 'rgba(16, 185, 129, 0.1)' // Light green
    case 'deleted':
    case 'removed':
      return 'rgba(239, 68, 68, 0.1)' // Light red
    case 'updated':
    case 'modified':
      return 'rgba(59, 130, 246, 0.1)' // Light blue
    default:
      return 'var(--surface-ground)'
  }
}

const getActivityText = (activity) => {
  const action = getActivityAction(activity)
  switch (action) {
    case 'added':
    case 'created':
      return 'Job Added'
    case 'deleted':
    case 'removed':
      return 'Job Deleted'
    case 'updated':
    case 'modified':
      return 'Job Updated'
    default:
      return 'Job Action'
  }
}

const searchJobs = () => {
  router.push('/employer/jobs')
}

const viewJobDetails = (job) => {
  router.push(`/employer/jobs/${job.id}`)
}

const saveJob = (job) => {
  console.log('Save job:', job.title)
  // TODO: Implement save job functionality
}

const applyToJob = (job) => {
  router.push(`/employer/jobs/${job.id}?action=apply`)
}

const navigateToJob = (activity) => {
  // Only navigate for added or updated jobs, not deleted ones
  const action = getActivityAction(activity)
  if (action === 'deleted' || action === 'removed') {
    return // Don't navigate for deleted jobs
  }
  
  // Check if activity has a jobId or id
  const jobId = activity.jobId || activity.id
  if (jobId) {
    router.push(`/employer/jobs/${jobId}`)
  }
}

// Map application status to PrimeVue Tag severity
const statusSeverity = (status) => {
  switch ((status || '').toUpperCase()) {
    case 'PENDING':
      return 'warn'; // Yellow
    case 'REVIEWING':
      return 'info'; // Orange (PrimeVue info is blue, but warn is yellow)
    case 'SHORTLISTED':
      return 'info'; // Blue
    case 'INTERVIEW_SCHEDULED':
      return 'primary'; // or 'info', 'success', etc.
    case 'OFFERED':
      return 'success'; // Green
    case 'REJECTED':
      return 'danger'; // Red
    default:
      return 'secondary';
  }
};

const getStatusClass = (status) => {
  switch ((status || '').toUpperCase()) {
    case 'PENDING':
      return 'pending';
    case 'REVIEWING':
      return 'reviewing';
    case 'SHORTLISTED':
      return 'shortlisted';
    case 'INTERVIEW_SCHEDULED':
      return 'interview-scheduled';
    case 'OFFERED':
      return 'offered';
    case 'REJECTED':
      return 'rejected';
    case 'WITHDRAWN':
      return 'withdrawn';
    default:
      return 'default';
  }
};

onMounted(() => {
  dashboardStore.fetchStats()
  dashboardStore.startAutoRefresh()
})

onUnmounted(() => {
  dashboardStore.stopAutoRefresh()
})
</script>

<template>
  <div class="dashboard-container flex-1">
    <div class="dashboard-content">

      <!-- Employer Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card primary">
          <div class="stat-icon">
            <i class="pi pi-briefcase"></i>
          </div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.totalJobs || 0 }}</h3>
            <p>Jobs Posted</p>
            <Button class="go-to-view-btn" icon="pi pi-arrow-right" severity="secondary" @click.prevent="router.push('/employer/jobs')"></Button>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <i class="pi pi-send"></i>
          </div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.totalApplications || 0 }}</h3>
            <p>Applications Received</p>
            <Button class="go-to-view-btn" icon="pi pi-arrow-right" severity="secondary" @click.prevent="router.push('/employer/applications')"></Button>
          </div>
        </div>

      <!--  <div class="stat-card warning">
          <div class="stat-icon">
            <i class="pi pi-eye"></i>
          </div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.jobViews || 0 }}</h3>
            <p>Job Views</p>
          </div>
        </div>-->
      </div>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Applications Table -->
        <div class="recommended-jobs-card">
          <div class="card-header">
            <i class="pi pi-users card-icon"></i>
            <h3>Applications Received</h3>
            <a class="view-all-link" @click.prevent="router.push('/employer/applications')">View All</a>
          </div>
          <div class="jobs-list">
            <DataView :value="dashboardStore.stats.applications || []" layout="list">
              <template #list="slotProps">
                <div v-for="(item, index) in slotProps.items" :key="index" class="mb-4">
                  <!-- Desktop View -->
                  <Card class="shadow-md border border-gray-200 rounded-lg cursor-pointer application-card desktop-view" @click="router.push('/employer/applications')">
                    <template #content>
                      <div class="application-row">
                        <!-- Job Title -->
                        <div class="info-item">
                          <div class="info-icon">
                            <i class="pi pi-briefcase text-blue-500"></i>
                          </div>
                          <div class="info-content">
                            <div class="info-value">{{ item.jobTitle }}</div>
                          </div>
                        </div>

                        <!-- Applicant Email -->
                        <div class="info-item">
                          <div class="info-icon">
                            <i class="pi pi-user text-green-500"></i>
                          </div>
                          <div class="info-content">
                            <div class="info-value">{{ item.applicantName }}</div>
                          </div>
                        </div>

                        <!-- Application Date -->
                        <div class="info-item">
                          <div class="info-icon">
                            <i class="pi pi-calendar text-purple-500"></i>
                          </div>
                          <div class="info-content">
                            <div class="info-value">{{ new Date(item.applicationDate).toLocaleDateString() }}</div>
                          </div>
                        </div>

                        <!-- Status -->
                        <div class="info-item">
                          <div class="info-icon">
                            <i class="pi pi-info-circle text-orange-500"></i>
                          </div>
                          <div class="info-content">
                            <div class="info-value">
                              <Tag
                                :value="item.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())"
                                :severity="statusSeverity(item.status)"
                                v-if="item.status.toUpperCase() !== 'INTERVIEW_SCHEDULED'"
                                :class="['status-tag', getStatusClass(item.status)]"
                              />
                              <Tag
                                v-else
                                :value="item.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())"
                                :class="['status-tag', getStatusClass(item.status)]"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </Card>

                  <!-- Mobile View -->
                  <div class="application-card mobile-view" @click="router.push('/employer/applications')">
                    <div class="application-row">
                      <span class="label">
                        <i class="pi pi-briefcase"></i>
                        Job Title:
                      </span>
                      <span class="value">{{ item.jobTitle }}</span>
                    </div>
                    <div class="application-row">
                      <span class="label">
                        <i class="pi pi-user"></i>
                        Applicant Name:
                      </span>
                      <span class="value">{{ item.applicantName }}</span>
                    </div>
                    <div class="application-row">
                      <span class="label">
                        <i class="pi pi-calendar"></i>
                        Applied Date:
                      </span>
                      <span class="value">{{ new Date(item.applicationDate).toLocaleDateString() }}</span>
                    </div>
                    <div class="application-row status-row">
                      <span class="label">
                        <i class="pi pi-info-circle"></i>
                        Status:
                      </span>
                      <span class="status-tag" :class="getStatusClass(item.status)">
                        {{ item.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) }}
                      </span>
                    </div>
                  </div>
                </div>
              </template>
            </DataView>
            
            <!-- Empty State -->
            <div v-if="!dashboardStore.stats.applications || dashboardStore.stats.applications.length === 0" class="empty-applications">
              <div class="text-center py-8">
                <i class="pi pi-users text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-600 mb-2">No Applications Yet</h3>
                <p class="text-sm text-gray-500">Applications you receive will appear here</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="activity-card">
          <div class="card-header">
            <i class="pi pi-clock card-icon"></i>
            <h3>{{ t('dashboard.recentActivity') }}</h3>
            <span class="last-updated" v-if="dashboardStore.lastUpdated">
              {{ t('dashboard.updated', { time: dashboardStore.getStatsAge() }) }}
            </span>
          </div>
          <!-- Desktop Activity List -->
          <div class="activity-list activity-list-desktop">
            <div v-for="activity in recentActivity" :key="activity.id" 
              :class="['activity-item', getActivityAction(activity) === 'deleted' || getActivityAction(activity) === 'removed' ? '' : 'cursor-pointer']"
              :style="{ background: getActivityBgColor(activity) }" @click="navigateToJob(activity)">
              <div class="activity-icon" :style="{ background: getActivityBgColor(activity) }">
                <i :class="getActivityIcon(activity)" :style="{ color: getActivityColor(activity) }"></i>
              </div>
              <div class="activity-content">
                <p class="activity-title">
                  {{ getActivityText(activity) }}<span v-if="activity.title"> - {{ activity.title }}</span>
                </p>
                <span class="activity-time">{{ formatActivityTime(activity.time) }}</span>
              </div>
            </div>
          </div>
          <!-- Mobile Activity List -->
          <div class="activity-list-mobile">
            <div v-for="activity in recentActivity" :key="activity.id" 
              :class="['activity-item', getActivityAction(activity) === 'deleted' || getActivityAction(activity) === 'removed' ? '' : 'cursor-pointer']"
              :style="{ background: getActivityBgColor(activity) }" @click="navigateToJob(activity)">
              <div class="activity-icon" :style="{ background: getActivityBgColor(activity) }">
                <i :class="getActivityIcon(activity)" :style="{ color: getActivityColor(activity) }"></i>
              </div>
              <div class="activity-content">
                <p class="activity-title">
                  {{ getActivityText(activity) }}<span v-if="activity.title"> - {{ activity.title }}</span>
                </p>
                <span class="activity-time">{{ formatActivityTime(activity.time) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Create Job Button if No Jobs Exist -->
      <div v-if="dashboardStore.stats.totalJobs === 0" class="empty-state">
        <i class="pi pi-briefcase empty-icon"></i>
        <h3>No Jobs Available</h3>
        <Button 
          @click="router.push('/employer/jobs')"
          icon="pi pi-plus"
          label="Create Job"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  background: linear-gradient(135deg, var(--dashboard-bg-start) 0%, var(--dashboard-bg-end) 100%);
  transition: background var(--transition-duration) ease;
}

.dashboard-content {
  padding: 1rem;
}

.welcome-section {
  margin-bottom: 2rem;
}

.welcome-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.user-location,
.user-status {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-location i {
  color: var(--primary-color);
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.go-to-view-btn {
  position: absolute !important;
  right: 1rem;
  bottom: 1rem;
}

.stat-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-duration) ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.stat-card.success::before {
  background: var(--green-500);
}

.stat-card.warning::before {
  background: var(--yellow-500);
}

.stat-card.info::before {
  background: var(--blue-500);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0 0 0.5rem 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-change {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.stat-change.positive {
  color: var(--green-500);
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.recommended-jobs-card,
.activity-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.card-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.card-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
  flex: 1;
}

.view-all-btn {
  color: var(--primary-color) !important;
  padding: 0 !important;
}

.view-all-link {
  float: right;
  margin-left: auto;
  color: var(--primary-color, #1976d2);
  font-size: 0.98em;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s;
}

.view-all-link:hover {
  color: #125ea2;
}

.last-updated {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 380px;
  overflow-y: auto;
}

.job-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--surface-border);
  cursor: pointer;
  transition: all var(--transition-duration) ease;
}

.job-item:hover {
  border-color: var(--primary-color);
  background: var(--surface-hover);
}

.job-info {
  flex: 1;
  min-width: 0;
}

.job-title {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.job-title:hover {
  color: var(--primary-color);
}

.job-company {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 500;
}

.job-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.job-location,
.job-salary {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.job-location i {
  color: var(--primary-color);
}

.job-salary {
  font-weight: 600;
  color: var(--primary-color);
}

.job-salary i {
  color: var(--primary-color);
}

.job-actions {
  display: flex;
  gap: 0.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 380px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all var(--transition-duration) ease;
  cursor: pointer;
}

.activity-item:hover {
  background: var(--surface-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.9rem;
}

.activity-description {
  margin: 0 0 0.25rem 0;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

.activity-description-wrapper {
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: pre-line;
  max-width: 100%;
}

.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
}

.table-responsive table {
  width: 100%;
  max-width: 100%;
}

.table-responsive th,
.table-responsive td {
  overflow-wrap: break-word;
  word-break: break-all;
  white-space: pre-line;
  max-width: 120px;
}

/* Hide/show table and card layout based on screen size */
.applications-table-desktop { display: block; }
.applications-cards-mobile { display: none; }

.activity-list-desktop { display: flex; }
.activity-list-mobile { display: none; }

/* Desktop/Mobile view switching */
.desktop-view { display: block; }
.mobile-view { display: none; }

@media (max-width: 768px) {
  .dashboard-container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  .dashboard-content {
    padding: 1rem;
  }

  .welcome-content h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: 1fr;
  }

  .content-grid {
    grid-template-columns: 1fr;
    overflow-x: auto;
    width: 100%;
    max-width: 100vw;
  }

  .recommended-jobs-card,
  .activity-card {
    padding: 0.5rem;
    border-radius: 5px;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  .table-responsive {
    max-width: 100vw;
    overflow-x: hidden;
  }

  .table-responsive table th,
  .table-responsive table td {
    padding: 2px;
    font-size: 0.75rem;
    max-width: 80px;
  }
  .table-responsive table {
    max-width: 100vw;
  }
  .applications-table-desktop { display: none; }
  .applications-cards-mobile { display: block; }
  
  /* Mobile view switching */
  .desktop-view { display: none !important; }
  .mobile-view { display: block !important; }
  
  .application-card {
    background: var(--surface-card);
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  }
  
  /* Card layout responsive adjustments */
  .jobs-list .p-card {
    margin-bottom: 1rem;
  }
  
  .jobs-list .p-card .p-card-title {
    font-size: 1rem;
  }
  
  .jobs-list .p-card .p-card-content {
    padding: 1rem;
  }
  
  /* Mobile application card adjustments */
  .application-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    padding: 0.5rem 0;
  }
  
  .info-item {
    flex-direction: column;
    gap: 0.3rem;
    align-items: center;
    text-align: center;
    min-width: 0;
  }
  
  .info-icon {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }
  
  .info-icon i {
    font-size: 0.8rem;
  }
  
  .info-content {
    min-width: 0;
    flex: 1;
    overflow: visible;
    text-align: center;
  }
  
  .info-value {
    font-size: 0.75rem;
    line-height: 1.2;
    word-break: break-word;
    overflow: visible;
    text-align: center;
  }
  
  .status-tag {
    font-size: 0.6rem;
    padding: 0.25rem 0.5rem;
  }
  
  /* Ensure cards have proper spacing on mobile */
  .jobs-list .p-card {
    margin-bottom: 0.75rem;
  }
  
  .jobs-list .p-card .p-card-content {
    padding: 0.75rem;
  }
  .application-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
  }
  .application-label {
    font-weight: 600;
    color: var(--text-color-secondary);
    margin-right: 0.5rem;
  }
  .application-value {
    color: var(--text-color);
    word-break: break-all;
    text-align: right;
  }
  .application-card.empty {
    text-align: center;
    color: var(--text-color-secondary);
    font-style: italic;
  }
  .applications-cards-mobile {
    max-height: 350px;
    overflow-y: auto;
  }
  
  /* Mobile Application Card Styles */
  .application-card.mobile-view {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .application-card.mobile-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .application-card.mobile-view .application-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.2;
    padding: 0;
    font-size: 0.95rem;
  }
  .application-card.mobile-view .application-row:last-child {
    margin-bottom: 0;
  }
  .application-card.mobile-view .label,
  .application-card.mobile-view .value {
    margin: 0;
    padding: 0;
  }
  
  .application-card.mobile-view .label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .application-card.mobile-view .label i {
    font-size: 0.9rem;
    color: var(--primary-color);
  }
  
  .application-card.mobile-view .value {
    font-size: 0.85rem;
    font-weight: 500;
    color: #111;
    text-align: right;
    word-break: break-word;
    max-width: 60%;
  }
  
  .application-card.mobile-view .status-row .status-tag {
    font-size: 0.75rem;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    font-weight: 600;
  }
  
  /* Status tag colors for mobile */
  .application-card.mobile-view .status-tag.pending {
    background-color: #fff3cd;
    color: #856404;
  }
  
  .application-card.mobile-view .status-tag.reviewing {
    background-color: #d1ecf1;
    color: #0c5460;
  }
  
  .application-card.mobile-view .status-tag.shortlisted {
    background-color: #d4edda;
    color: #155724;
  }
  
  .application-card.mobile-view .status-tag.interview-scheduled {
    background-color: #e9d8fd;
    color: #6b21a8;
  }
  
  .application-card.mobile-view .status-tag.offered {
    background-color: #d1f2eb;
    color: #0f5132;
  }
  
  .application-card.mobile-view .status-tag.rejected {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  .application-card.mobile-view .status-tag.withdrawn {
    background-color: #fbeaea;
    color: #d32f2f;
  }
  
  .application-card.mobile-view .status-tag.default {
    background-color: #f0f0f0;
    color: #555;
  }
  .activity-list-desktop { display: none !important; }
  .activity-list-mobile {
    display: flex !important;
    flex-direction: column;
    gap: 1rem;
    max-height: 350px;
    overflow-y: auto;
  }
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-applications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

/* Application Card Styles */
.application-card {
  transition: all 0.2s ease;
}

.application-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Horizontal Layout Styles */
.application-row {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1rem 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--surface-100);
  flex-shrink: 0;
}

.info-icon i {
  font-size: 1rem;
}

.info-content {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
}

.info-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
  word-break: break-word;
}

.status-tag {
  font-size: 0.7rem;
  font-weight: 600;
}

.empty-icon,
.loading-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

.status-tag.pending {
  background-color: #fff3cd;
  color: #856404;
}
.status-tag.reviewing {
  background-color: #d1ecf1;
  color: #0c5460;
}
.status-tag.shortlisted {
  background-color: #d4edda;
  color: #155724;
}
.status-tag.interview-scheduled {
  background-color: #e9d8fd;
  color: #6b21a8;
}
.status-tag.offered {
  background-color: #d1f2eb;
  color: #0f5132;
}
.status-tag.rejected {
  background-color: #f8d7da;
  color: #721c24;
}
.status-tag.withdrawn {
  background-color: #fbeaea;
  color: #d32f2f;
}
.status-tag.default {
  background-color: #f0f0f0;
  color: #555;
}
</style>