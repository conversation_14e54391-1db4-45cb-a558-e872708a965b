import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsUrl } from 'class-validator';
import { FeedType } from '../entities/feed.entity';

export class CreateFeedDto {
  @ApiProperty({
    description: 'Title of the feed',
    example: 'New Job Opportunities in Tech Industry',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Content of the feed',
    example: 'The tech industry is booming with new opportunities...',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Type of the feed',
    enum: FeedType,
    example: FeedType.NEWS,
  })
  @IsEnum(FeedType)
  type: FeedType;

  @ApiProperty({
    description: 'URL of the feed image',
    required: false,
    example: 'https://example.com/image.jpg',
  })
  @IsUrl()
  @IsOptional()
  imageUrl?: string;
}
