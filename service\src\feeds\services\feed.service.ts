import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FeedEntity, FeedStatus } from '../entities/feed.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { CreateFeedDto } from '../dto/create-feed.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';
import { UserRole } from '../../users/enums/user.enum';

@Injectable()
export class FeedService {
  constructor(
    @InjectRepository(FeedEntity)
    private readonly feedRepository: Repository<FeedEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  async create(userId: string, createFeedDto: CreateFeedDto): Promise<FeedEntity> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const feed = this.feedRepository.create({
      ...createFeedDto,
      author: user,
      status: FeedStatus.PENDING,
    });

    return this.feedRepository.save(feed);
  }

  async findAll(
    paginationDto: PaginationDto,
    includePending: boolean = false,
  ): Promise<PaginatedResponseDto<FeedEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.feedRepository
      .createQueryBuilder('feed')
      .leftJoinAndSelect('feed.author', 'author')
      .leftJoinAndSelect('feed.approvedBy', 'approvedBy')
      .orderBy('feed.createdAt', 'DESC');

    if (!includePending) {
      queryBuilder.where('feed.status = :status', { status: FeedStatus.APPROVED });
    }

    const [feeds, total] = await queryBuilder.skip(skip).take(limit).getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      items: feeds,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findOne(id: string): Promise<FeedEntity> {
    const feed = await this.feedRepository.findOne({
      where: { id },
      relations: ['author', 'approvedBy'],
    });

    if (!feed) {
      throw new NotFoundException('Feed not found');
    }

    return feed;
  }

  async updateStatus(
    id: string,
    adminId: string,
    status: FeedStatus,
    comment?: string,
  ): Promise<FeedEntity> {
    const feed = await this.feedRepository.findOne({
      where: { id },
      relations: ['author'],
    });

    if (!feed) {
      throw new NotFoundException('Feed not found');
    }

    const admin = await this.userRepository.findOne({
      where: { id: adminId },
    });

    if (!admin) {
      throw new NotFoundException('Admin not found');
    }

    if (![UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(admin.role)) {
      throw new ForbiddenException('Only admins can update feed status');
    }

    feed.status = status;
    feed.approvedBy = admin;
    feed.approvedAt = new Date();
    if (comment) {
      feed.adminComment = comment;
    }

    return this.feedRepository.save(feed);
  }

  async delete(id: string, userId: string, userRole: UserRole): Promise<void> {
    const feed = await this.feedRepository.findOne({
      where: { id },
      relations: ['author'],
    });

    if (!feed) {
      throw new NotFoundException('Feed not found');
    }

    if (
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN &&
      feed.author.id !== userId
    ) {
      throw new ForbiddenException('You can only delete your own feeds');
    }

    await this.feedRepository.remove(feed);
  }
}
