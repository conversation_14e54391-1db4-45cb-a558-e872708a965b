import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminActivityEntity } from '../entities/admin-activity.entity';
import { AdminActionType } from '../enums/admin.enum';
import { WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Between } from 'typeorm';

export interface ActivityFilter {
  adminId?: string;
  targetId?: string;
  actionType?: AdminActionType;
  startDate?: Date;
  endDate?: Date;
}

@Injectable()
export class ActivityService {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ActivityService.name);

  constructor(
    @InjectRepository(AdminActivityEntity)
    private readonly adminActivityRepository: Repository<AdminActivityEntity>,
  ) {}

  async logActivity(
    adminId: string,
    actionType: AdminActionType,
    targetId: string,
    details?: Record<string, any>,
  ): Promise<AdminActivityEntity> {
    const activity = this.adminActivityRepository.create({
      adminId,
      actionType,
      targetId,
      details,
    });
    return this.adminActivityRepository.save(activity);
  }

  async getUserActivities(userId: string) {
    return this.adminActivityRepository.find({
      where: { targetId: userId },
      relations: ['admin'],
      order: { createdAt: 'DESC' },
    });
  }

  async getAdminActivities(adminId: string) {
    return this.adminActivityRepository.find({
      where: { adminId },
      relations: ['admin'],
      order: { createdAt: 'DESC' },
    });
  }

  async getActivityTypes() {
    return Object.values(AdminActionType);
  }

  async getActivityDetails(activity: AdminActivityEntity): Promise<Record<string, any>> {
    return {
      id: activity.id,
      actionType: activity.actionType,
      targetId: activity.targetId,
      adminId: activity.admin?.id,
      adminEmail: activity.admin?.email,
      details: JSON.stringify(activity.details),
      createdAt: activity.createdAt,
    };
  }

  async trackActivity(userId: string, type: AdminActionType, details: Record<string, any> = {}) {
    try {
      const user = await this.adminActivityRepository.findOne({ where: { targetId: userId } });
      if (!user) {
        throw new Error('User not found');
      }

      const activity = this.adminActivityRepository.create({
        targetId: userId,
        actionType: type,
        details,
      });

      await this.adminActivityRepository.save(activity);

      // Broadcast activity to connected clients
      this.server?.emit('user-activity', {
        type,
        userId,
        userEmail: user.details.userEmail,
        userRole: user.details.userRole,
        details,
        timestamp: new Date(),
      });

      return activity;
    } catch (error) {
      this.logger.error(`Error tracking activity: ${error.message}`);
      throw error;
    }
  }

  async getRecentActivities(limit?: number) {
    return this.adminActivityRepository.find({
      order: { createdAt: 'DESC' },
      take: limit,
      relations: ['admin'],
    });
  }

  async searchActivities(filter: ActivityFilter, page?: number, limit?: number) {
    const query = this.adminActivityRepository
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.admin', 'admin');

    if (filter.adminId) {
      query.andWhere('activity.adminId = :adminId', { adminId: filter.adminId });
    }

    if (filter.targetId) {
      query.andWhere('activity.targetId = :targetId', { targetId: filter.targetId });
    }

    if (filter.actionType) {
      query.andWhere('activity.actionType = :actionType', { actionType: filter.actionType });
    }

    if (filter.startDate) {
      query.andWhere('activity.createdAt >= :startDate', { startDate: filter.startDate });
    }

    if (filter.endDate) {
      query.andWhere('activity.createdAt <= :endDate', { endDate: filter.endDate });
    }

    if (page && limit) {
      query.skip((page - 1) * limit).take(limit);
    }

    const [activities, total] = await query.getManyAndCount();

    return {
      activities,
      total,
      page,
      limit,
      totalPages: page && limit ? Math.ceil(total / limit) : undefined,
    };
  }

  async getActivityAnalytics(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const activities = await this.adminActivityRepository.find({
      where: {
        createdAt: Between(startDate, endDate),
      },
      relations: ['admin'],
    });

    const analytics = {
      totalActivities: activities.length,
      activitiesByType: {},
      activitiesByAdmin: {},
      activitiesByTarget: {},
    };

    activities.forEach((activity) => {
      // Count by type
      analytics.activitiesByType[activity.actionType] =
        (analytics.activitiesByType[activity.actionType] || 0) + 1;

      // Count by admin
      const adminId = activity.admin?.id;
      if (adminId) {
        analytics.activitiesByAdmin[adminId] = (analytics.activitiesByAdmin[adminId] || 0) + 1;
      }

      // Count by target
      const targetId = activity.targetId;
      if (targetId) {
        analytics.activitiesByTarget[targetId] = (analytics.activitiesByTarget[targetId] || 0) + 1;
      }
    });

    return analytics;
  }

  async getActivityTrends(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const activities = await this.adminActivityRepository
      .createQueryBuilder('activity')
      .select('DATE(activity.createdAt)', 'date')
      .addSelect('activity.actionType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('DATE(activity.createdAt)')
      .addGroupBy('activity.actionType')
      .orderBy('date', 'ASC')
      .getRawMany();

    const trends = {};
    activities.forEach((activity) => {
      if (!trends[activity.date]) {
        trends[activity.date] = {};
      }
      trends[activity.date][activity.type] = parseInt(activity.count, 10);
    });

    return trends;
  }

  async exportActivities(filter: ActivityFilter, format: 'csv' | 'excel' = 'csv') {
    const activities = await this.searchActivities(filter);

    const data = activities.activities.map((activity) => ({
      id: activity.id,
      actionType: activity.actionType,
      targetId: activity.targetId,
      adminId: activity.admin?.id,
      adminEmail: activity.admin?.email,
      details: JSON.stringify(activity.details),
      createdAt: activity.createdAt,
    }));

    if (format === 'excel') {
      // TODO: Implement Excel export using a library like exceljs
      return {
        format: 'excel',
        data,
        message: 'Excel export not implemented yet',
      };
    }

    // CSV export
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map((row) => Object.values(row).join(','));
    const csv = [headers, ...rows].join('\n');

    return {
      format: 'csv',
      data: csv,
      filename: `activities_export_${new Date().toISOString()}.csv`,
    };
  }

  async getVisualizationData(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const activities = await this.adminActivityRepository
      .createQueryBuilder('activity')
      .select('DATE(activity.createdAt)', 'date')
      .addSelect('activity.actionType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('DATE(activity.createdAt)')
      .addGroupBy('activity.actionType')
      .orderBy('date', 'ASC')
      .getRawMany();

    const visualizationData = {
      labels: [],
      datasets: {},
    };

    activities.forEach((activity) => {
      if (!visualizationData.labels.includes(activity.date)) {
        visualizationData.labels.push(activity.date);
      }

      if (!visualizationData.datasets[activity.type]) {
        visualizationData.datasets[activity.type] = [];
      }

      visualizationData.datasets[activity.type].push(parseInt(activity.count, 10));
    });

    return visualizationData;
  }
}
