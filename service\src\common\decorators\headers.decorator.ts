import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const CustomHeaders = createParamDecorator(
  (data: string | string[], ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const headers = request.headers;

    if (typeof data === 'string') {
      return headers[data.toLowerCase()];
    }

    if (Array.isArray(data)) {
      return data.reduce((acc, header) => {
        acc[header] = headers[header.toLowerCase()];
        return acc;
      }, {});
    }

    return headers;
  },
);
