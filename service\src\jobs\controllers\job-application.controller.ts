import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
  Patch,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { JobApplicationService } from '../services/job-application.service';
import { CreateJobApplicationDto } from '../dto/create-job-application.dto';
import { JobApplicationEntity, ApplicationStatus } from '../entities/job-application.entity';
import { JobEntity } from '../entities/job.entity';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';

@ApiTags('Job Applications')
@Controller('job-applications')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JobApplicationController {
  constructor(private readonly jobApplicationService: JobApplicationService) {}

  @Post('jobs/:jobId/apply')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Apply for a job' })
  @SwaggerResponse({
    status: HttpStatus.CREATED,
    description: 'Job application submitted successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request - Already applied or job is inactive',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Job seeker access required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
    type: ApiResponseDto,
  })
  async applyForJob(
    @Param('jobId') jobId: string,
    @Request() req,
    @Body() createApplicationDto: CreateJobApplicationDto,
  ): Promise<ApiResponseDto<JobApplicationEntity>> {
    try {
      const application = await this.jobApplicationService.applyForJob(
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        req.user.id,
        jobId,
        createApplicationDto,
      );
      return ApiResponseDto.success(application, HttpStatus.CREATED);
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      return ApiResponseDto.error('JOB_APPLICATION_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('my-applications')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Get all job applications for the authenticated user' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns all job applications',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Job seeker access required',
    type: ApiResponseDto,
  })
  async getMyApplications(
    @Request() req,
    @Query() paginationDto: PaginationDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<JobApplicationEntity>>> {
    try {
      const applications = await this.jobApplicationService.getMyApplications(
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        req.user.id,
        paginationDto,
      );
      return ApiResponseDto.success(applications, HttpStatus.OK);
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      return ApiResponseDto.error('MY_APPLICATIONS_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('jobs/:jobId/favorite')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Toggle job favorite status' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job favorite status toggled successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Job seeker access required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
    type: ApiResponseDto,
  })
  async toggleFavorite(
    @Param('jobId') jobId: string,
    @Request() req,
  ): Promise<ApiResponseDto<{ isFavorite: boolean }>> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      const result = await this.jobApplicationService.toggleFavorite(req.user.id, jobId);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      return ApiResponseDto.error('FAVORITE_TOGGLE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('my-favorites')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Get all favorite jobs for the authenticated user' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns all favorite jobs',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Job seeker access required',
    type: ApiResponseDto,
  })
  async getMyFavorites(
    @Request() req,
    @Query() paginationDto: PaginationDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<JobEntity>>> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      const favorites = await this.jobApplicationService.getMyFavorites(req.user.id, paginationDto);
      return ApiResponseDto.success(favorites, HttpStatus.OK);
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      return ApiResponseDto.error('MY_FAVORITES_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Patch(':applicationId/status')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update job application status' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Application status updated successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Employer or admin access required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Application not found',
    type: ApiResponseDto,
  })
  async updateApplicationStatus(
    @Param('applicationId') applicationId: string,
    @Request() req,
    @Body('status') status: ApplicationStatus,
    @Body('comment') comment?: string,
    @Body('interviewDate') interviewDate?: string,
    @Body('interviewTime') interviewTime?: string,
    @Body('interviewLocation') interviewLocation?: string,
    @Body('interviewNotes') interviewNotes?: string,
  ): Promise<ApiResponseDto<JobApplicationEntity>> {
    try {
      const application = await this.jobApplicationService.updateApplicationStatus(
        applicationId,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        req.user.id,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        req.user.role,
        status,
        comment,
        interviewDate,
        interviewTime,
        interviewLocation,
        interviewNotes,
      );
      return ApiResponseDto.success(application, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'APPLICATION_STATUS_UPDATE_ERROR',
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Patch(':applicationId/withdraw')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Withdraw a job application (by applicant)' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Application withdrawn successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Only applicant can withdraw',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Application not found',
    type: ApiResponseDto,
  })
  async withdrawApplication(
    @Param('applicationId') applicationId: string,
    @Request() req,
  ): Promise<ApiResponseDto<JobApplicationEntity>> {
    try {
      const application = await this.jobApplicationService.withdrawApplication(
        applicationId,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        req.user.id,
      );
      return ApiResponseDto.success(application, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'APPLICATION_WITHDRAW_ERROR',
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Patch(':applicationId')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update job application details' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job application details updated successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Employer or admin access required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Application not found',
    type: ApiResponseDto,
  })
  async updateApplication(
    @Param('applicationId') applicationId: string,
    @Body() updateDto: CreateJobApplicationDto, // or UpdateJobApplicationDto
  ): Promise<ApiResponseDto<JobApplicationEntity>> {
    try {
      const application = await this.jobApplicationService.updateApplication(
        applicationId,
        updateDto,
      );
      return ApiResponseDto.success(application, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'APPLICATION_UPDATE_ERROR',
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('job-applicants')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get all job applicants for jobs posted by the authenticated user' })
  @ApiQuery({
    name: 'jobId',
    required: false,
    type: String,
    description: 'Optional job ID to filter applicants for a specific job',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns all job applicants with job and applicant information',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Employer or admin access required',
    type: ApiResponseDto,
  })
  async getJobApplicants(
    @Request() req,
    @Query() paginationDto: PaginationDto,
    @Query('jobId') jobId?: string,
  ): Promise<ApiResponseDto<PaginatedResponseDto<JobApplicationEntity>>> {
    try {
      const applicants = await this.jobApplicationService.getJobApplicants(
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        req.user.id,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        req.user.role,
        paginationDto,
        jobId,
      );
      return ApiResponseDto.success(applicants, HttpStatus.OK);
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      return ApiResponseDto.error('JOB_APPLICANTS_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
