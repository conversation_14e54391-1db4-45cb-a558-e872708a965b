<script setup>
import { onMounted, ref, watch, onUnmounted } from 'vue';
import { useAppStore } from '@/stores/app'
import BottomNav from '@/components/nav/BottomNav.vue';
import TopNav from '@/components/nav/TopNav.vue';
import SideNav from '@/components/nav/SideNav.vue';

import { useAuthStore } from '@/stores/auth.js'
import { useNotificationSocket } from '@/utils/notificationSocket.js'
import { useGlobalToast } from '@/composables/useGlobalToast';


const authStore = useAuthStore()
const { showToast } = useGlobalToast();

const isMobile = ref(false);

const appStore = useAppStore();

const title = ref('');
const subTitle = ref('')

watch(() => appStore.pageInfo, (newValue) => {
    title.value = newValue.title;
    subTitle.value = newValue.subTitle
})

const handleResize = () => {
    isMobile.value = window.innerWidth < 768;

    appStore.isMobile = isMobile.value;
};
let notificationSocket

if (authStore.user && authStore.user.id) {

    notificationSocket = useNotificationSocket({
        userId: authStore.user.id,
        token: authStore.token || (document.cookie.match(/eac.t=([^;]+)/)?.[1] || '')
    })

    // Watch for new notifications and show a toast
    if (notificationSocket) {
        notificationSocket.socket.on('notification', (notification) => {
            showToast({
                severity: notification.type || 'info',
                summary: notification.title,
                detail: notification.message
            });
        });
    }
}

onMounted(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
});


</script>
<template>
    <div class="app-layout flex flex-col">
        <TopNav></TopNav>
        <div class="center-container flex flex-1" :class="{ 'mobile-view': isMobile }">
            <SideNav v-if="!isMobile" />
            <router-view />
        </div>
        <BottomNav v-if="isMobile"></BottomNav>
    </div>
</template>
<style scoped>
.app-layout {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

.center-container {
    overflow: hidden;
}

.title-container {
    text-align: center;
}

.title-el {
    font-size: 1.2rem;
    font-weight: 600;
}

.subtitle-el {
    font-size: 0.8rem;
}

.mobile-view {
    margin-bottom: 4rem;
}
</style>
