import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { JobEntity } from './job.entity';

export enum ApplicationStatus {
  PENDING = 'PENDING',
  REVIEWING = 'REVIEWING',
  SHORTLISTED = 'SHORTLISTED',
  INTERVIEW_SCHEDULED = 'INTERVIEW_SCHEDULED',
  REJECTED = 'REJECTED',
  OFFERED = 'OFFERED',
  WITHDRAWN = 'WITHDRAWN',
}

@Entity('job_applications')
export class JobApplicationEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => JobEntity, (job) => job.applications)
  @JoinColumn()
  job: JobEntity;

  @ManyToOne(() => UserEntity, (user) => user.jobApplications)
  @JoinColumn()
  applicant: UserEntity;

  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: ApplicationStatus.PENDING,
  })
  status: ApplicationStatus;

  @Column({ type: 'text', nullable: true })
  coverLetter: string;

  @Column({ type: 'text', nullable: true })
  resumeUrl: string;

  @Column({ type: 'text', nullable: true })
  adminComment: string;

  @Column({ type: 'date', nullable: true })
  interviewDate: Date;

  @Column({ type: 'varchar', length: 20, nullable: true })
  interviewTime: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  interviewLocation: string;

  @Column({ type: 'text', nullable: true })
  interviewNotes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
