// Mock API for Jobs List endpoint
// GET: /jobs

const delay = (ms = 400) => new Promise(resolve => setTimeout(resolve, ms))

export const jobsListMock = {
  async getJobs(params = {}) {
    await delay(400)
    
    const jobs = {
        "items": [
            {
                "id": "e3bf191c-1b63-4aad-a512-50d5b3697ed7",
                "createdAt": "2025-06-09T07:55:45.518Z",
                "updatedAt": "2025-06-09T08:11:00.062Z",
                "title": "Cleaner",
                "description": "1- gvftfygj hgygb hgyghukj nbjgbjhuy  hggyb gygj gj jgjnjb hjgh\n2- ytftyfhghh jjgiuhj hgfggkh.\n3- dytfhgg hjgfguy hjgyguy jhvghftyd jougytyrd ghdtdtygio fdtfjg.\n4- ufdytfghg yfytfhjf jhfgyg hjgfuy gftydytiuh jhftyfh uyghh jfyugiuh.\n5- byftrdrytf jhftyfyuhij hgfgfhguh hvfygiuhu bgfugkjj jhffyuguh hfgdrtdytvb vfyugibhbuyghbhgyt  ggguybbug hjfhtfhj.",
                "salary": "12000.00",
                "jobType": "FULL_TIME",
                "paymentType": "MONTHLY",
                "location": "Abc phase 1 Rahatni Pune ",
                "isDeleted": false,
                "isUserVerified": false,
                "status": "ACTIVE",
                "scheduledPostTime": "2025-06-09T08:10:45.517Z",
                "adminComment": null,
                "urgency": "WITHIN_10_DAYS",
                "experienceLevel": "FRESHER",
                "benefits": [
                    "jvgvygv",
                    "bv gfvf"
                ],
                "requirements": [
                    "gvuvvuub",
                    "jhhvfcvgh",
                    "jvfyrtfg",
                    "nbnb fgcfcgv"
                ],
                "responsibilities": [
                    "gyhyhhiyhihhuihiu",
                    "mjnuygtygt"
                ],
                "skills": [
                    "1- gvygyh",
                    "jbgvgb",
                    ",n hgvjbh",
                    "nbb hg hhn",
                    "khvghcfcyg+",
                    "hfhvhgv"
                ],
                "thumbnail": "",
                "images": [],
                "showContact": false,
                "contactDisplayType": "NONE",
                "contactPhone": null,
                "contactEmail": null,
                "contactPerson": null,
                "vacancies": 5,
                "workingHours": "10 hrs ",
                "accommodation": "hgyfdtrcyvvtyvty",
                "transportation": "tytfytytvytv",
                "foodProvided": "tvytvtyvty",
                "safetyEquipment": "yytytty",
                "trainingProvided": "ytvyytvyv",
                "employer": {
                    "id": "36d4e85f-fd20-4370-b38f-7120e7abae6e",
                    "createdAt": "2025-06-07T07:42:41.422Z",
                    "updatedAt": "2025-06-07T07:42:41.445Z",
                    "email": "<EMAIL>",
                    "phone": null,
                    "firstName": null,
                    "middleName": null,
                    "lastName": null,
                    "emailOtp": null,
                    "emailOtpExpiry": null,
                    "isPhoneVerified": false,
                    "isEmailVerified": true,
                    "isProfileComplete": true,
                    "isAadharVerified": false,
                    "isBlocked": false,
                    "role": "super_admin",
                    "subscriptionType": "default",
                    "profileId": "38cf5fa6-7697-472f-b281-ae9ca9b4f424",
                    "createdBy": null
                },
                "industry": {
                    "id": "65eacd40-0c09-4f85-931f-43add0b86aaf",
                    "createdAt": "2025-06-08T11:38:45.516Z",
                    "updatedAt": "2025-06-08T11:38:45.516Z",
                    "name": "Restaurent",
                    "description": "string",
                    "isActive": true
                }
            },
            {
                "id": "c44027fb-21cc-4789-ad83-0021dad91b03",
                "createdAt": "2025-06-08T17:04:01.658Z",
                "updatedAt": "2025-06-08T17:20:00.067Z",
                "title": "Testing job 2",
                "description": "Testing long text description",
                "salary": "4000.00",
                "jobType": "PART_TIME",
                "paymentType": "WEEKLY",
                "location": "Bangalore",
                "isDeleted": false,
                "isUserVerified": false,
                "status": "ACTIVE",
                "scheduledPostTime": "2025-06-08T17:19:01.651Z",
                "adminComment": null,
                "urgency": "WITHIN_10_DAYS",
                "experienceLevel": "EXPERT",
                "benefits": [],
                "requirements": [],
                "responsibilities": [],
                "skills": [],
                "thumbnail": null,
                "images": [],
                "showContact": true,
                "contactDisplayType": "NONE",
                "contactPhone": null,
                "contactEmail": null,
                "contactPerson": null,
                "vacancies": 1,
                "workingHours": "8 Hours per day",
                "accommodation": null,
                "transportation": null,
                "foodProvided": null,
                "safetyEquipment": null,
                "trainingProvided": null,
                "employer": {
                    "id": "a6ee5dd1-529d-495e-b59a-1ea2e4b6127f",
                    "createdAt": "2025-06-08T07:36:52.880Z",
                    "updatedAt": "2025-06-09T07:20:11.907Z",
                    "email": "<EMAIL>",
                    "phone": null,
                    "firstName": "Gaurav",
                    "middleName": "",
                    "lastName": "Pandey",
                    "emailOtp": null,
                    "emailOtpExpiry": null,
                    "isPhoneVerified": false,
                    "isEmailVerified": true,
                    "isProfileComplete": false,
                    "isAadharVerified": false,
                    "isBlocked": false,
                    "role": "employer",
                    "subscriptionType": "default",
                    "profileId": "c077a003-4863-4aa2-8b36-f86466e5f0bf",
                    "createdBy": null
                },
                "industry": {
                    "id": "65eacd40-0c09-4f85-931f-43add0b86aaf",
                    "createdAt": "2025-06-08T11:38:45.516Z",
                    "updatedAt": "2025-06-08T11:38:45.516Z",
                    "name": "Restaurent",
                    "description": "string",
                    "isActive": true
                }
            },
            {
                "id": "d0a5cf2e-a9ee-4185-8d0b-6a02c18423f9",
                "createdAt": "2025-06-08T14:47:07.625Z",
                "updatedAt": "2025-06-08T15:03:00.067Z",
                "title": "Test",
                "description": "test sescription",
                "salary": "5999.00",
                "jobType": "FULL_TIME",
                "paymentType": "DAILY",
                "location": "Banglore",
                "isDeleted": false,
                "isUserVerified": false,
                "status": "ACTIVE",
                "scheduledPostTime": "2025-06-08T15:02:07.614Z",
                "adminComment": null,
                "urgency": "URGENT",
                "experienceLevel": "FRESHER",
                "benefits": [],
                "requirements": [],
                "responsibilities": [],
                "skills": [],
                "thumbnail": null,
                "images": [],
                "showContact": true,
                "contactDisplayType": "NONE",
                "contactPhone": null,
                "contactEmail": null,
                "contactPerson": null,
                "vacancies": 1,
                "workingHours": "9-6",
                "accommodation": null,
                "transportation": null,
                "foodProvided": null,
                "safetyEquipment": null,
                "trainingProvided": null,
                "employer": {
                    "id": "a6ee5dd1-529d-495e-b59a-1ea2e4b6127f",
                    "createdAt": "2025-06-08T07:36:52.880Z",
                    "updatedAt": "2025-06-09T07:20:11.907Z",
                    "email": "<EMAIL>",
                    "phone": null,
                    "firstName": "Gaurav",
                    "middleName": "",
                    "lastName": "Pandey",
                    "emailOtp": null,
                    "emailOtpExpiry": null,
                    "isPhoneVerified": false,
                    "isEmailVerified": true,
                    "isProfileComplete": false,
                    "isAadharVerified": false,
                    "isBlocked": false,
                    "role": "employer",
                    "subscriptionType": "default",
                    "profileId": "c077a003-4863-4aa2-8b36-f86466e5f0bf",
                    "createdBy": null
                },
                "industry": {
                    "id": "65eacd40-0c09-4f85-931f-43add0b86aaf",
                    "createdAt": "2025-06-08T11:38:45.516Z",
                    "updatedAt": "2025-06-08T11:38:45.516Z",
                    "name": "Restaurent",
                    "description": "string",
                    "isActive": true
                }
            }
        ],
        "total": 3,
        "page": 1,
        "limit": 10,
        "totalPages": 1,
        "hasNextPage": false,
        "hasPreviousPage": false
    }
    
    return {
      data: jobs,
      success: true,
      status: 200,
      message: 'Jobs retrieved successfully'
    }
  }
}