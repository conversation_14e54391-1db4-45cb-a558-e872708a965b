import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { WelcomeModule } from './welcome/welcome.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UsersModule } from './users/users.module';
import { EmailModule } from './email/email.module';
import { SmsModule } from './sms/sms.module';
import { AppThrottlerModule } from './throttler/throttler.module';
import { AuthModule } from './auth/auth.module';
import { IndustriesModule } from './industries/industries.module';
import { JobsModule } from './jobs/jobs.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { FeedsModule } from './feeds/feeds.module';
import { ErrorLogModule } from './error-log/error-log.module';
import { FeedbackModule } from './feedback/feedback.module';
import { CompaniesModule } from './companies/companies.module';
import { DashboardController } from './users/controllers/dashboard.controller';
import { DashboardService } from './users/services/dashboard.service';
import { UserEntity } from './users/entities/user.entity';
import { IndustryEntity } from './industries/entities/industry.entity';
import { JobEntity } from './jobs/entities/job.entity';
import { FeedbackEntity } from './feedback/entities/feedback.entity';
import { ErrorLogEntity } from './error-log/entities/error-log.entity';
import { WelcomeEntity } from './welcome/entities/welcome.entity';
import { AdminActivityEntity } from './users/entities/admin-activity.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST'),
        port: +configService.get<number>('DB_PORT'),
        username: configService.get('DB_USERNAME'),
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_DATABASE'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') !== 'production',
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      UserEntity,
      IndustryEntity,
      JobEntity,
      FeedbackEntity,
      ErrorLogEntity,
      WelcomeEntity,
      AdminActivityEntity,
    ]),
    AppThrottlerModule,
    WelcomeModule,
    UsersModule,
    EmailModule,
    SmsModule,
    AuthModule,
    IndustriesModule,
    JobsModule,
    SchedulerModule,
    FeedsModule,
    ErrorLogModule,
    FeedbackModule,
    CompaniesModule,
  ],
  controllers: [AppController, DashboardController],
  providers: [AppService, DashboardService],
})
export class AppModule {}
