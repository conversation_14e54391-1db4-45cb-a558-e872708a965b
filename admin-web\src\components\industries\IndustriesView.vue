<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import Card from 'primevue/card'
import <PERSON><PERSON> from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Dialog from 'primevue/dialog'
import Select from 'primevue/select'
import Message from 'primevue/message'
import ConfirmDialog from 'primevue/confirmdialog'
import { useConfirm } from 'primevue/useconfirm'
import { api } from '@/api'
import { useAuthStore } from '@/stores/auth'
import IndustryFilterDrawer from './IndustryFilterDrawer.vue'
import SpeedDial from 'primevue/speeddial'
import Tag from 'primevue/tag'
import IndustriesTable from './IndustriesTable.vue'
import CategoriesTable from './CategoriesTable.vue'

const confirm = useConfirm()
const store = useAuthStore();

const industries = ref([])

const getIndustries = async () => {
    const result = await api.industries.getIndustries()
      if (result.success) {
        industries.value = result.data
      }
}

onMounted(async () => {
  getIndustries()
})

// Drawer state
const isFilterDrawerVisible = ref(false)
const filterState = reactive({
  searchTerm: '',
  selectedIndustryFilter: null
})

// Dialog states
const showIndustryDialog = ref(false)
const showCategoryDialog = ref(false)
const isEditMode = ref(false)
const selectedIndustry = ref(null)

// Form data
const industryForm = reactive({
  name: '',
  description: ''
})

const categoryForm = reactive({
  name: '',
  description: '',
  industryId: null,
  categoryId: null
})

// Computed properties
const filteredIndustries = computed(() => {
  let filtered = industries.value
  if (filterState.searchTerm) {
    const search = filterState.searchTerm.toLowerCase()
    filtered = filtered.filter(industry => 
      industry.name.toLowerCase().includes(search) ||
      industry.description.toLowerCase().includes(search) ||
      industry.subIndustries.some(cat => 
        cat.name.toLowerCase().includes(search) ||
        cat.description.toLowerCase().includes(search)
      )
    )
  }
  return filtered
})

const industryOptions = computed(() => 
  industries.value.map(industry => ({
    label: industry.name,
    value: industry.id
  }))
)

const allCategories = computed(() => {
  const categories = []
  industries.value.forEach(industry => {
    industry.subIndustries.forEach(category => {
      categories.push({
        ...category,
        industryName: industry.name,
        industryId: industry.id
      })
    })
  })
  if (filterState.selectedIndustryFilter) {
    return categories.filter(cat => cat.industryId === filterState.selectedIndustryFilter)
  }
  return categories
})

const industryStats = computed(() => {
  const totalIndustries = industries.value.length
  const totalCategories = industries.value.reduce((sum, industry) => sum + industry.subIndustries.length, 0)
  const avgCategoriesPerIndustry = totalIndustries > 0 ? Math.round(totalCategories / totalIndustries) : 0
  return {
    totalIndustries,
    totalCategories,
    avgCategoriesPerIndustry
  }
})

const clearFilters = () => {
  filterState.searchTerm = ''
  filterState.selectedIndustryFilter = null
}

const handleFilterApply = (filters) => {
  Object.assign(filterState, filters)
  isFilterDrawerVisible.value = false
}
const handleFilterReset = () => {
  clearFilters()
  isFilterDrawerVisible.value = false
}

const speedDialActions = [
  {
    label: 'Filter',
    icon: 'pi pi-filter',
    command: () => { isFilterDrawerVisible.value = true }
  }
]

// Industry management
const openIndustryDialog = (industry = null) => {
  isEditMode.value = !!industry
  if (industry) {
    industryForm.name = industry.name
    industryForm.description = industry.description
    selectedIndustry.value = industry
  } else {
    resetIndustryForm()
  }
  showIndustryDialog.value = true
}

const resetIndustryForm = () => {
  industryForm.name = ''
  industryForm.description = ''
  selectedIndustry.value = null
}

const saveIndustry = async () => {
  if (!industryForm.name.trim()) return

  if (isEditMode.value && selectedIndustry.value) {
    const response = await api.industries.update(selectedIndustry.value.id,{
      name: industryForm.name.trim(),
      description: industryForm.description.trim()
    })

    getIndustries()

  } else {
    const response = await api.industries.create({
      name: industryForm.name.trim(),
      description: industryForm.description.trim(),
    })

    getIndustries()

  }

  showIndustryDialog.value = false
  resetIndustryForm()
}

const deleteIndustry = (industry) => {
  confirm.require({
    message: `Are you sure you want to ${industry.isActive ? 'De-activate' : 'Activate'} the "${industry.name}"`,
    header: `${industry.isActive ? 'De-activate' : 'Activate'} Industry`,
    icon: 'pi pi-eye-slash',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: `${industry.isActive ? 'De-activate' : 'Activate'}`,
    accept: async () => {
      const response = await api.industries.update(industry.id, {
      isActive: !industry.isActive
    })

    getIndustries()
    }
  })
}

// Category management
const openCategoryDialog = (category = null, industryId = null) => {
  if (category) {
    categoryForm.name = category.name
    categoryForm.description = category.description
    categoryForm.industryId = category.industryId
    categoryForm.categoryId = category.id
    selectedIndustry.value = category
  } else {
    categoryForm.name = ''
    categoryForm.description = ''
    categoryForm.industryId = industryId
    categoryForm.categoryId = category?.id
    selectedIndustry.value = null
  }
  showCategoryDialog.value = true
}

const saveCategory = async () => {
  if (!categoryForm.name.trim() || !categoryForm.industryId) return

  const industryIndex = industries.value.findIndex(i => i.id === categoryForm.industryId)
  if (industryIndex === -1) return

  if (selectedIndustry.value && selectedIndustry.value.id) {
    // Update existing category
    const response = await api.industries.updateCategory(categoryForm.categoryId,{
      name: categoryForm.name.trim(),
      description: categoryForm.description.trim()
    })

    getIndustries()
  } else {
    const response = await api.industries.createCategory({
      industryId: categoryForm.industryId,
      name: categoryForm.name.trim(),
      description: categoryForm.description.trim(),
    })

    getIndustries()
  }

  showCategoryDialog.value = false
  resetCategoryForm()
}

const resetCategoryForm = () => {
  categoryForm.name = ''
  categoryForm.description = ''
  categoryForm.industryId = null
  selectedIndustry.value = null
}

const deleteCategory = (category) => {
  confirm.require({
    message: `Are you sure you want to ${category.isActive ? 'De-activate' : 'Activate'} the category "${category.name}"?`,
    header: 'Delete Category',
    icon: 'pi pi-eye-slash',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: `${category.isActive ? 'De-activate' : 'Activate'}`,
    accept: async () => {
      const response = await api.industries.updateCategory(category.id,{
        isActive: false
      })

      getIndustries()
    }
  })
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date))
}
</script>

<template>
  <div class="industries-view flex-1">
    <!-- Filter Drawer -->
    <IndustryFilterDrawer
      v-model="isFilterDrawerVisible"
      :initialFilters="filterState"
      :industryOptions="industryOptions"
      :stats="industryStats"
      @apply="handleFilterApply"
      @reset="handleFilterReset"
      @close="isFilterDrawerVisible = false"
    />
    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Content Tabs -->
      <div class="content-tabs">
        <!-- Industries Section -->
        <IndustriesTable
          :industries="filteredIndustries"
          :industry-stats="industryStats"
          @add-industry="openIndustryDialog"
          @edit-industry="openIndustryDialog"
          @toggle-industry="deleteIndustry"
          @add-category="openCategoryDialog"
        />
        <!-- Categories Section -->
        <CategoriesTable
          :categories="allCategories"
          :industry-options="industryOptions"
          @add-category="openCategoryDialog"
          @edit-category="openCategoryDialog"
          @toggle-category="deleteCategory"
        />
      </div>
    </div>

    <!-- Industry Dialog -->
    <Dialog 
      v-model:visible="showIndustryDialog" 
      :header="isEditMode ? 'Edit Industry' : 'Add Industry'"
      :modal="true" 
      class="industry-dialog"
      :style="{ width: '500px' }"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="industry-name">Industry Name *</label>
          <InputText 
            id="industry-name"
            v-model="industryForm.name" 
            placeholder="Enter industry name"
            class="w-full"
            :class="{ 'p-invalid': !industryForm.name.trim() }"
          />
        </div>
        
        <div class="form-group">
          <label for="industry-details">Details</label>
          <Textarea 
            id="industry-details"
            v-model="industryForm.description" 
            placeholder="Enter industry details and description"
            rows="4"
            class="w-full"
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showIndustryDialog = false"
          />
          <Button 
            :label="isEditMode ? 'Update' : 'Create'" 
            @click="saveIndustry"
            :disabled="!industryForm.name.trim()"
          />
        </div>
      </template>
    </Dialog>

    <!-- Category Dialog -->
    <Dialog 
      v-model:visible="showCategoryDialog" 
      :header="selectedIndustry?.id ? 'Edit Category' : 'Add Category'"
      :modal="true" 
      class="category-dialog"
      :style="{ width: '500px' }"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="category-industry">Industry *</label>
          <Select
            id="category-industry"
            v-model="categoryForm.industryId"
            :options="industryOptions"
            option-label="label"
            option-value="value"
            placeholder="Select an industry"
            class="w-full"
            :class="{ 'p-invalid': !categoryForm.industryId }"
          />
        </div>
        
        <div class="form-group">
          <label for="category-name">Category Name *</label>
          <InputText 
            id="category-name"
            v-model="categoryForm.name" 
            placeholder="Enter category name"
            class="w-full"
            :class="{ 'p-invalid': !categoryForm.name.trim() }"
          />
        </div>
        
        <div class="form-group">
          <label for="category-details">Details</label>
          <Textarea 
            id="category-details"
            v-model="categoryForm.description" 
            placeholder="Enter category details and description"
            rows="4"
            class="w-full"
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showCategoryDialog = false"
          />
          <Button 
            :label="selectedIndustry?.id ? 'Update' : 'Create'" 
            @click="saveCategory"
            :disabled="!categoryForm.name.trim() || !categoryForm.industryId"
          />
        </div>
      </template>
    </Dialog>

    <ConfirmDialog />

    <!-- Speed Dial Button -->
    <SpeedDial
      :model="speedDialActions"
      direction="up"
      :style="{ position: 'fixed', right: '1.5rem', bottom: '1.2rem', zIndex: 1000 }"
      :tooltipOptions="{ position: 'left' }"
      showIcon="pi pi-bars"
      hideIcon="pi pi-times"
    />
  </div>
</template>

<style scoped>
.industries-view {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Left Filters Sidebar */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filters-header {
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  font-weight: 600;
  color: var(--p-text-color);
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  /* flex-direction: column;
  gap: 0.75rem; */
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-content p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.content-tabs {
  flex: 1;
  padding: 1rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tab-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.section-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

:deep(.section-header h3) {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--p-text-color);
}

:deep(.table-container) {
  flex: 1;
  overflow: hidden;
}

:deep(.industries-table,
.categories-table) {
  height: 100%;
}

:deep(.industry-name,
.category-name) {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

:deep(.industry-icon) {
  color: var(--p-primary-color);
  font-size: 1rem;
}

:deep(.category-icon) {
  color: var(--p-orange-500);
  font-size: 0.875rem;
}

:deep(.industry-details,
.category-details) {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
  line-height: 1.4;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.categories-count) {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

:deep(.count-badge) {
  background: var(--p-primary-100);
  color: var(--p-primary-700);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

:deep(.industry-badge) {
  background: var(--p-surface-100);
  color: var(--p-text-color);
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
}

:global(.dark) .industry-badge {
  background: var(--p-surface-700);
}

:deep(.created-date) {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

/* .action-buttons {
  display: flex;
  gap: 0.25rem;
} */

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
}

:deep(.form-group) {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

:deep(.form-group label) {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

:deep(.dialog-footer) {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .industries-view {
    flex-direction: column;
    height: auto;
  }
  
  .filters-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    order: 2;
  }
  
  .main-content {
    order: 1;
    height: 60vh;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .content-tabs {
    padding: 1rem;
  }
  
  .industry-details,
  .category-details {
    max-width: 200px;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>