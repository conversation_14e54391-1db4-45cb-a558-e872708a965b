import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FeedbackEntity, FeedbackStatus, FeedbackType } from '../entities/feedback.entity';
import { CreateFeedbackDto } from '../dto/create-feedback.dto';
import { UpdateFeedbackDto } from '../dto/update-feedback.dto';

@Injectable()
export class FeedbackService {
  constructor(
    @InjectRepository(FeedbackEntity)
    private readonly feedbackRepository: Repository<FeedbackEntity>,
  ) {}

  async create(createFeedbackDto: CreateFeedbackDto, userId: string): Promise<FeedbackEntity> {
    const feedback = this.feedbackRepository.create({
      ...createFeedbackDto,
      userId,
      status: FeedbackStatus.PENDING,
    });
    return this.feedbackRepository.save(feedback);
  }

  async findAll(
    page = 1,
    limit = 10,
    type?: FeedbackType,
    status?: FeedbackStatus,
    userId?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{ data: FeedbackEntity[]; total: number }> {
    const queryBuilder = this.feedbackRepository
      .createQueryBuilder('feedback')
      .leftJoinAndSelect('feedback.user', 'user');

    if (type) {
      queryBuilder.andWhere('feedback.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('feedback.status = :status', { status });
    }

    if (userId) {
      queryBuilder.andWhere('feedback.userId = :userId', { userId });
    }

    if (startDate) {
      queryBuilder.andWhere('feedback.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('feedback.createdAt <= :endDate', { endDate });
    }

    const [data, total] = await queryBuilder
      .orderBy('feedback.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return { data, total };
  }

  async findOne(id: string): Promise<FeedbackEntity> {
    const feedback = await this.feedbackRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!feedback) {
      throw new NotFoundException(`Feedback with ID ${id} not found`);
    }

    return feedback;
  }

  async update(
    id: string,
    updateFeedbackDto: UpdateFeedbackDto,
    adminId: string,
  ): Promise<FeedbackEntity> {
    const feedback = await this.findOne(id);

    Object.assign(feedback, {
      ...updateFeedbackDto,
      updatedBy: adminId,
    });

    return this.feedbackRepository.save(feedback);
  }

  async delete(id: string): Promise<void> {
    const result = await this.feedbackRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Feedback with ID ${id} not found`);
    }
  }

  async getFeedbackStats(): Promise<{
    total: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
  }> {
    const total = await this.feedbackRepository.count();

    const typeStats = await this.feedbackRepository
      .createQueryBuilder('feedback')
      .select('feedback.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('feedback.type')
      .getRawMany();

    const statusStats = await this.feedbackRepository
      .createQueryBuilder('feedback')
      .select('feedback.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('feedback.status')
      .getRawMany();

    return {
      total,
      byType: typeStats.reduce((acc, curr) => {
        acc[curr.type] = parseInt(curr.count);
        return acc;
      }, {}),
      byStatus: statusStats.reduce((acc, curr) => {
        acc[curr.status] = parseInt(curr.count);
        return acc;
      }, {}),
    };
  }
}
