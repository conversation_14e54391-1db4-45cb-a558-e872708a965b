import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { UserEntity } from '../../users/entities/user.entity';

export enum FeedType {
  NEWS = 'NEWS',
  ARTICLE = 'ARTICLE',
}

export enum FeedStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

@Entity('feeds')
export class FeedEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text')
  content: string;

  @Column({
    type: 'enum',
    enum: FeedType,
    default: FeedType.NEWS,
  })
  type: FeedType;

  @Column({
    type: 'enum',
    enum: FeedStatus,
    default: FeedStatus.PENDING,
  })
  status: FeedStatus;

  @Column({ type: 'text', nullable: true })
  imageUrl: string;

  @Column({ type: 'text', nullable: true })
  adminComment: string;

  @ManyToOne(() => UserEntity, (user) => user.feeds)
  @JoinColumn()
  author: UserEntity;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn()
  approvedBy: UserEntity;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
