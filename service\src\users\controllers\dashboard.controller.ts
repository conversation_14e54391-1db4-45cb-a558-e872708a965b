import { Controller, Get, Query, UseGuards, <PERSON><PERSON>, Header } from '@nestjs/common';
import { Response } from 'express';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { DashboardService } from '../services/dashboard.service';

@Controller('dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('overview')
  async getDashboardOverview() {
    return this.dashboardService.getDashboardOverview();
  }

  @Get('user-growth')
  async getUserGrowth(@Query('days') days?: number) {
    return this.dashboardService.getUserGrowth(days);
  }

  @Get('subscription-stats')
  async getSubscriptionStats() {
    return this.dashboardService.getSubscriptionStats();
  }

  @Get('role-distribution')
  async getRoleDistribution() {
    return this.dashboardService.getRoleDistribution();
  }

  @Get('admin-activity')
  async getAdminActivity(@Query('days') days?: number) {
    return this.dashboardService.getAdminActivity(days);
  }

  @Get('user-engagement')
  async getUserEngagementMetrics(@Query('days') days?: number) {
    return this.dashboardService.getUserEngagementMetrics(days ? Number(days) : undefined);
  }

  @Get('retention-rates')
  async getRetentionRates(@Query('days') days?: number) {
    return this.dashboardService.getRetentionRates(days ? Number(days) : undefined);
  }

  @Get('export/users')
  @Header('Content-Type', 'text/csv')
  @Header('Content-Disposition', 'attachment; filename=users.csv')
  async exportUserData(@Query('format') format: 'csv' | 'excel' = 'csv', @Res() res: Response) {
    const result = await this.dashboardService.exportUserData(format);
    if (format === 'csv') {
      res.send(result.data);
    } else {
      res.json(result);
    }
  }

  @Get('export/admin-activity')
  @Header('Content-Type', 'text/csv')
  @Header('Content-Disposition', 'attachment; filename=admin_activity.csv')
  async exportAdminActivity(
    @Query('format') format: 'csv' | 'excel' = 'csv',
    @Res() res: Response,
  ) {
    const result = await this.dashboardService.exportAdminActivity(format);
    if (format === 'csv') {
      res.send(result.data);
    } else {
      res.json(result);
    }
  }

  @Get('admin/dashboard')
  async getAdminDashboard() {
    return this.dashboardService.getAdminDashboard();
  }
}
