import { createApp } from 'vue'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import Aura from '@primeuix/themes/aura'
import router from './router'
import './style.css'
import 'primeicons/primeicons.css'
import i18n from './i18n'
import Tooltip from 'primevue/tooltip'
import ToastService from 'primevue/toastservice';
import App from './App.vue'


const app = createApp(App)
const pinia = createPinia()
app.use(pinia)
app.use(router)
app.use(ToastService);
app.use(i18n)
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      prefix: 'p',
      darkModeSelector: '.dark',
      cssLayer: {
        name: 'primevue',
        order: 'tailwind-base, primevue, tailwind-utilities'
      }
    }
  }
})

app.use(ConfirmationService)

// Register global directives
app.directive('tooltip', Tooltip)

app.mount('#app')