import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import * as session from 'express-session';
import * as basicAuth from 'express-basic-auth';
import * as cookieParser from 'cookie-parser';
import { WinstonLoggerService } from './winston-logger.service';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: new WinstonLoggerService(),
  });

  app.set('trust proxy', 1); // 1 = trust first proxy (Cloudflare)
  app.use(cookieParser());

  // Configure session middleware
  app.use(
    session({
      secret: process.env.SESSION_SECRET || 'your-secret-key',
      resave: true,
      saveUninitialized: true,
      cookie: {
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: 'lax',
        httpOnly: true,
      },
    }),
  );

  app.use(
    ['/api'], // Protect the Swagger route
    basicAuth({
      challenge: true,
      users: { jobdalal: 'i2ppass#word#!!2!' }, // set your desired username/password
    }),
  );

  // Enable CORS with credentials
  app.enableCors({
    origin: [
      process.env.JOB_SEEKER_FRONTEND_URL || 'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'jdu', 'usertype', 'deviceId'],
  });

  // Serve static files from the 'public/admin/dist' directory
  app.useStaticAssets(join(__dirname, '..', 'public', 'admin', 'dist'), {
    index: false, // Don't serve index.html for directory requests
  });

  app.useStaticAssets(join(__dirname, '..', 'public', 'employer', 'dist'), {
    index: false, // Don't serve index.html for directory requests
  });

  app.useStaticAssets(join(__dirname, '..', 'public', 'jobseeker', 'dist'), {
    index: false, // Don't serve index.html for directory requests
  });

  app.useStaticAssets(join(__dirname, '..', 'public', 'welcome', 'dist'), {
    index: false, // Don't serve index.html for directory requests
  });

  // Apply global validation pipe
  app.useGlobalPipes(new ValidationPipe({ transform: true }));

  // Configure Swagger
  const config = new DocumentBuilder()
    .setTitle('Job Dalal API')
    .setDescription('The Job Dalal API documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('Auth', 'Authentication endpoints')
    .addTag('Users', 'User management endpoints')
    .addTag('Industries', 'Industry management endpoints')
    .addTag('Jobs', 'Job management endpoints')
    .addTag('Feeds', 'News and article feed endpoints')
    .addTag('Welcome', 'Welcome endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    deepScanRoutes: true,
    extraModels: [],
  });

  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
