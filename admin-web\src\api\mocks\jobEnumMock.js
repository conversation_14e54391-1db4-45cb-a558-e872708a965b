// Mock API for Job Enums endpoint
// GET: /jobs/job-enums

const delay = (ms = 300) => new Promise(resolve => setTimeout(resolve, ms))

export const jobEnumMock = {
  async getJobEnums() {
    await delay(300)
    
    const jobEnums = {
      // Job Categories/Departments
      departments: [
        { 
          id: 1, 
          name: 'Construction', 
          code: 'construction', 
          color: '#3b82f6',
          description: 'Building, renovation, and infrastructure projects',
          jobCount: 3245
        },
        { 
          id: 2, 
          name: 'Manufacturing', 
          code: 'manufacturing', 
          color: '#10b981',
          description: 'Production, assembly, and quality control',
          jobCount: 2890
        },
        { 
          id: 3, 
          name: 'Logistics', 
          code: 'logistics', 
          color: '#f59e0b',
          description: 'Transportation, warehousing, and supply chain',
          jobCount: 2156
        },
        { 
          id: 4, 
          name: 'Maintenance', 
          code: 'maintenance', 
          color: '#ef4444',
          description: 'Equipment repair, facility upkeep, and technical support',
          jobCount: 1834
        },
        { 
          id: 5, 
          name: 'Security', 
          code: 'security', 
          color: '#8b5cf6',
          description: 'Safety, surveillance, and protection services',
          jobCount: 1456
        },
        { 
          id: 6, 
          name: 'Hospitality', 
          code: 'hospitality', 
          color: '#06b6d4',
          description: 'Food service, cleaning, and customer service',
          jobCount: 1266
        },
        { 
          id: 7, 
          name: 'Healthcare Support', 
          code: 'healthcare-support', 
          color: '#ec4899',
          description: 'Medical assistance, patient care, and facility support',
          jobCount: 987
        },
        { 
          id: 8, 
          name: 'Automotive', 
          code: 'automotive', 
          color: '#84cc16',
          description: 'Vehicle repair, maintenance, and manufacturing',
          jobCount: 876
        }
      ],

      // Job Types
      jobTypes: [
        { 
          id: 1, 
          name: 'Full-time', 
          code: 'full-time',
          description: '40+ hours per week, benefits included',
          isDefault: true
        },
        { 
          id: 2, 
          name: 'Part-time', 
          code: 'part-time',
          description: 'Less than 40 hours per week'
        },
        { 
          id: 3, 
          name: 'Contract', 
          code: 'contract',
          description: 'Fixed-term employment with specific end date'
        },
        { 
          id: 4, 
          name: 'Temporary', 
          code: 'temporary',
          description: 'Short-term employment, usually through agencies'
        },
        { 
          id: 5, 
          name: 'Seasonal', 
          code: 'seasonal',
          description: 'Employment during specific seasons or periods'
        },
        { 
          id: 6, 
          name: 'Freelance', 
          code: 'freelance',
          description: 'Project-based independent contractor work'
        },
        { 
          id: 7, 
          name: 'Apprenticeship', 
          code: 'apprenticeship',
          description: 'Training program with hands-on experience'
        }
      ],

      // Experience Levels
      experienceLevels: [
        { 
          id: 1, 
          name: 'Entry Level', 
          code: 'entry', 
          years: '0-1',
          description: 'No prior experience required, training provided'
        },
        { 
          id: 2, 
          name: 'Some Experience', 
          code: 'some', 
          years: '1-3',
          description: 'Basic knowledge and some hands-on experience'
        },
        { 
          id: 3, 
          name: 'Experienced', 
          code: 'experienced', 
          years: '3-5',
          description: 'Solid experience with proven track record'
        },
        { 
          id: 4, 
          name: 'Senior Level', 
          code: 'senior', 
          years: '5-10',
          description: 'Advanced skills with leadership capabilities'
        },
        { 
          id: 5, 
          name: 'Expert Level', 
          code: 'expert', 
          years: '10+',
          description: 'Highly specialized with extensive experience'
        }
      ],

      // Job Statuses
      jobStatuses: [
        { 
          id: 1, 
          name: 'Active', 
          code: 'active', 
          color: '#10b981',
          description: 'Currently accepting applications'
        },
        { 
          id: 2, 
          name: 'Draft', 
          code: 'draft', 
          color: '#f59e0b',
          description: 'Not yet published'
        },
        { 
          id: 3, 
          name: 'Closed', 
          code: 'closed', 
          color: '#ef4444',
          description: 'No longer accepting applications'
        },
        { 
          id: 4, 
          name: 'On Hold', 
          code: 'on-hold', 
          color: '#6b7280',
          description: 'Temporarily paused'
        },
        { 
          id: 5, 
          name: 'Filled', 
          code: 'filled', 
          color: '#8b5cf6',
          description: 'Position has been filled'
        }
      ],

      // Application Statuses
      applicationStatuses: [
        { 
          id: 1, 
          name: 'Submitted', 
          code: 'submitted', 
          color: '#3b82f6',
          description: 'Application has been submitted'
        },
        { 
          id: 2, 
          name: 'Under Review', 
          code: 'under-review', 
          color: '#f59e0b',
          description: 'Application is being reviewed'
        },
        { 
          id: 3, 
          name: 'Interview Scheduled', 
          code: 'interview-scheduled', 
          color: '#06b6d4',
          description: 'Interview has been scheduled'
        },
        { 
          id: 4, 
          name: 'Interview Completed', 
          code: 'interview-completed', 
          color: '#8b5cf6',
          description: 'Interview process completed'
        },
        { 
          id: 5, 
          name: 'Offer Extended', 
          code: 'offer-extended', 
          color: '#10b981',
          description: 'Job offer has been made'
        },
        { 
          id: 6, 
          name: 'Hired', 
          code: 'hired', 
          color: '#22c55e',
          description: 'Successfully hired'
        },
        { 
          id: 7, 
          name: 'Rejected', 
          code: 'rejected', 
          color: '#ef4444',
          description: 'Application was not successful'
        },
        { 
          id: 8, 
          name: 'Withdrawn', 
          code: 'withdrawn', 
          color: '#6b7280',
          description: 'Application withdrawn by candidate'
        }
      ],

      // Work Schedules
      workSchedules: [
        { 
          id: 1, 
          name: 'Day Shift', 
          code: 'day-shift',
          hours: '6:00 AM - 2:00 PM',
          description: 'Standard daytime hours'
        },
        { 
          id: 2, 
          name: 'Evening Shift', 
          code: 'evening-shift',
          hours: '2:00 PM - 10:00 PM',
          description: 'Afternoon to evening hours'
        },
        { 
          id: 3, 
          name: 'Night Shift', 
          code: 'night-shift',
          hours: '10:00 PM - 6:00 AM',
          description: 'Overnight hours'
        },
        { 
          id: 4, 
          name: 'Rotating Shifts', 
          code: 'rotating-shifts',
          hours: 'Varies',
          description: 'Alternating between different shift times'
        },
        { 
          id: 5, 
          name: 'Flexible Hours', 
          code: 'flexible-hours',
          hours: 'Flexible',
          description: 'Adjustable work schedule'
        },
        { 
          id: 6, 
          name: 'Weekend Only', 
          code: 'weekend-only',
          hours: 'Saturday & Sunday',
          description: 'Weekend work only'
        }
      ],

      // Education Levels
      educationLevels: [
        { 
          id: 1, 
          name: 'No Formal Education', 
          code: 'none',
          description: 'No educational requirements'
        },
        { 
          id: 2, 
          name: 'High School Diploma', 
          code: 'high-school',
          description: 'High school diploma or equivalent (GED)'
        },
        { 
          id: 3, 
          name: 'Trade School Certificate', 
          code: 'trade-school',
          description: 'Vocational or technical school certification'
        },
        { 
          id: 4, 
          name: 'Associate Degree', 
          code: 'associate',
          description: '2-year college degree'
        },
        { 
          id: 5, 
          name: 'Bachelor\'s Degree', 
          code: 'bachelor',
          description: '4-year college degree'
        },
        { 
          id: 6, 
          name: 'Professional Certification', 
          code: 'certification',
          description: 'Industry-specific professional certification'
        }
      ],

      // Salary Ranges (Annual USD)
      salaryRanges: [
        { 
          id: 1, 
          name: 'Under $30,000', 
          code: 'under-30k',
          min: 0,
          max: 29999
        },
        { 
          id: 2, 
          name: '$30,000 - $40,000', 
          code: '30k-40k',
          min: 30000,
          max: 40000
        },
        { 
          id: 3, 
          name: '$40,000 - $50,000', 
          code: '40k-50k',
          min: 40000,
          max: 50000
        },
        { 
          id: 4, 
          name: '$50,000 - $60,000', 
          code: '50k-60k',
          min: 50000,
          max: 60000
        },
        { 
          id: 5, 
          name: '$60,000 - $75,000', 
          code: '60k-75k',
          min: 60000,
          max: 75000
        },
        { 
          id: 6, 
          name: '$75,000 - $100,000', 
          code: '75k-100k',
          min: 75000,
          max: 100000
        },
        { 
          id: 7, 
          name: 'Over $100,000', 
          code: 'over-100k',
          min: 100000,
          max: 999999
        }
      ],

      // Common Skills by Category
      skills: {
        construction: [
          'Blueprint Reading', 'Safety Protocols', 'Heavy Machinery Operation', 
          'Concrete Work', 'Electrical Wiring', 'Plumbing', 'Carpentry', 
          'Welding', 'Roofing', 'Drywall Installation', 'Painting', 
          'OSHA Compliance', 'Power Tools', 'Measuring & Layout'
        ],
        manufacturing: [
          'Quality Control', 'Assembly Line Work', 'Machine Operation', 
          'Inventory Management', 'Lean Manufacturing', 'Six Sigma', 
          'Forklift Operation', 'CNC Operation', 'Packaging', 
          'Production Planning', 'Safety Procedures', 'Equipment Maintenance'
        ],
        logistics: [
          'Warehouse Operations', 'Inventory Management', 'Shipping & Receiving', 
          'Forklift Operation', 'Order Fulfillment', 'Supply Chain', 
          'Transportation', 'Loading & Unloading', 'Package Handling', 
          'Route Planning', 'Customer Service', 'Documentation'
        ],
        maintenance: [
          'Preventive Maintenance', 'Troubleshooting', 'Electrical Systems', 
          'HVAC Systems', 'Plumbing', 'Mechanical Repair', 'Welding', 
          'Equipment Calibration', 'Safety Procedures', 'Hand Tools', 
          'Power Tools', 'Facility Management'
        ],
        security: [
          'Surveillance', 'Access Control', 'Emergency Response', 
          'Report Writing', 'Customer Service', 'Conflict Resolution', 
          'First Aid/CPR', 'Security Systems', 'Patrol Procedures', 
          'Communication Skills', 'Attention to Detail', 'Physical Fitness'
        ],
        hospitality: [
          'Customer Service', 'Food Safety', 'Cleaning Procedures', 
          'Time Management', 'Team Work', 'Communication Skills', 
          'Cash Handling', 'Inventory Management', 'Equipment Operation', 
          'Sanitation Standards', 'Multi-tasking', 'Problem Solving'
        ]
      },

      // Benefits Categories
      benefitCategories: [
        {
          id: 1,
          name: 'Health & Wellness',
          code: 'health-wellness',
          benefits: [
            'Health Insurance', 'Dental Insurance', 'Vision Insurance',
            'Mental Health Support', 'Gym Membership', 'Wellness Programs',
            'Employee Assistance Program', 'Health Savings Account'
          ]
        },
        {
          id: 2,
          name: 'Financial',
          code: 'financial',
          benefits: [
            '401(k) Matching', 'Retirement Plan', 'Life Insurance',
            'Disability Insurance', 'Stock Options', 'Profit Sharing',
            'Performance Bonuses', 'Overtime Pay'
          ]
        },
        {
          id: 3,
          name: 'Time Off',
          code: 'time-off',
          benefits: [
            'Paid Time Off', 'Vacation Days', 'Sick Leave',
            'Personal Days', 'Parental Leave', 'Bereavement Leave',
            'Jury Duty Leave', 'Military Leave'
          ]
        },
        {
          id: 4,
          name: 'Work-Life Balance',
          code: 'work-life',
          benefits: [
            'Flexible Schedule', 'Remote Work Options', 'Compressed Work Week',
            'Job Sharing', 'Telecommuting', 'Flexible Start Times'
          ]
        },
        {
          id: 5,
          name: 'Professional Development',
          code: 'professional-dev',
          benefits: [
            'Training Programs', 'Tuition Reimbursement', 'Certification Support',
            'Conference Attendance', 'Skill Development', 'Career Advancement',
            'Mentorship Programs', 'Learning Stipend'
          ]
        },
        {
          id: 6,
          name: 'Transportation & Commute',
          code: 'transportation',
          benefits: [
            'Commuter Benefits', 'Parking Allowance', 'Public Transit Pass',
            'Company Vehicle', 'Mileage Reimbursement', 'Bike Storage',
            'Shuttle Service', 'Gas Allowance'
          ]
        }
      ],

      // Location Types
      locationTypes: [
        { 
          id: 1, 
          name: 'On-site', 
          code: 'on-site',
          description: 'Work performed at employer location'
        },
        { 
          id: 2, 
          name: 'Remote', 
          code: 'remote',
          description: 'Work performed from home or any location'
        },
        { 
          id: 3, 
          name: 'Hybrid', 
          code: 'hybrid',
          description: 'Combination of on-site and remote work'
        },
        { 
          id: 4, 
          name: 'Travel Required', 
          code: 'travel',
          description: 'Position requires travel to various locations'
        },
        { 
          id: 5, 
          name: 'Multiple Locations', 
          code: 'multiple',
          description: 'Work at different company locations'
        }
      ],

      // Company Sizes
      companySizes: [
        { 
          id: 1, 
          name: 'Startup (1-10 employees)', 
          code: 'startup',
          min: 1,
          max: 10
        },
        { 
          id: 2, 
          name: 'Small (11-50 employees)', 
          code: 'small',
          min: 11,
          max: 50
        },
        { 
          id: 3, 
          name: 'Medium (51-200 employees)', 
          code: 'medium',
          min: 51,
          max: 200
        },
        { 
          id: 4, 
          name: 'Large (201-1000 employees)', 
          code: 'large',
          min: 201,
          max: 1000
        },
        { 
          id: 5, 
          name: 'Enterprise (1000+ employees)', 
          code: 'enterprise',
          min: 1000,
          max: 999999
        }
      ],

      // Popular US Cities for Blue Collar Jobs
      popularCities: [
        { id: 1, name: 'New York, NY', state: 'NY', jobCount: 15420 },
        { id: 2, name: 'Los Angeles, CA', state: 'CA', jobCount: 12890 },
        { id: 3, name: 'Chicago, IL', state: 'IL', jobCount: 9876 },
        { id: 4, name: 'Houston, TX', state: 'TX', jobCount: 8765 },
        { id: 5, name: 'Phoenix, AZ', state: 'AZ', jobCount: 7654 },
        { id: 6, name: 'Philadelphia, PA', state: 'PA', jobCount: 6543 },
        { id: 7, name: 'San Antonio, TX', state: 'TX', jobCount: 5432 },
        { id: 8, name: 'San Diego, CA', state: 'CA', jobCount: 5321 },
        { id: 9, name: 'Dallas, TX', state: 'TX', jobCount: 5210 },
        { id: 10, name: 'Austin, TX', state: 'TX', jobCount: 4987 },
        { id: 11, name: 'Jacksonville, FL', state: 'FL', jobCount: 4876 },
        { id: 12, name: 'Fort Worth, TX', state: 'TX', jobCount: 4765 },
        { id: 13, name: 'Columbus, OH', state: 'OH', jobCount: 4654 },
        { id: 14, name: 'Charlotte, NC', state: 'NC', jobCount: 4543 },
        { id: 15, name: 'Indianapolis, IN', state: 'IN', jobCount: 4432 },
        { id: 16, name: 'Seattle, WA', state: 'WA', jobCount: 4321 },
        { id: 17, name: 'Denver, CO', state: 'CO', jobCount: 4210 },
        { id: 18, name: 'Boston, MA', state: 'MA', jobCount: 4098 },
        { id: 19, name: 'Nashville, TN', state: 'TN', jobCount: 3987 },
        { id: 20, name: 'Atlanta, GA', state: 'GA', jobCount: 3876 }
      ]
    }
    
    return {
      data: jobEnums,
      success: true,
      status: 200,
      message: 'Job enums retrieved successfully'
    }
  }
}