import { nextTick } from 'vue';
import { createRouter, create<PERSON>eb<PERSON><PERSON><PERSON> } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

import { useLoader } from '@/composables/useLoader';

const routes = [
  //   {
  //   path: '/login',
  //   redirect: '/jobseeker/login'
  // },
  {
    path: '/jobseeker/login',
    component: () => import('@/views/Login.vue'),
    meta: {
      app: 'Jobseeker',
      title: 'Login'
    },
  },
  {
    path: '/jobseeker',
    component: () => import('@/views/RootLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          app: 'Jobseeker',
          requiresAuth: true,
          title: 'Dashboard'
        },
      },
      {
        path: '/jobseeker/jobs',
        name: 'JobSearch',
        component: () => import('@/views/JobsList.vue'),
        meta: {
          requiresAuth: true,
          app: 'Jobseeker',
          title: 'Find Jobs'
        }
      },
      {
        path: '/jobseeker/jobs/saved',
        name: 'SavedJobs',
        component: () => import('@/views/SavedJobs.vue'),
        meta: {
          requiresAuth: true,
          app: 'Jobseeker',
          title: 'Saved Jobs'
        }
      },
      {
        path: '/jobseeker/jobs/:id',
        name: 'JobDetails',
        component: () => import('@/views/JobDetails.vue'),
        meta: {
          requiresAuth: true,
          app: 'Jobseeker',
          title: 'Job Details'
        }
      },
      {
        path: '/jobseeker/applications',
        name: 'MyApplications',
        component: () => import('@/views/Applications.vue'),
        meta: {
          requiresAuth: true,
          app: 'Jobseeker',
          title: 'Applied Jobs'
        }
      },
      {
        path: '/jobseeker/profile',
        name: 'JobSeekerProfile',
        component: () => import('@/views/Profile.vue'),
        meta: {
          requiresAuth: true,
          app: 'Jobseeker',
          title: 'Profile'
        }
      },
      {
        path: '/jobseeker/notifications',
        name: 'Notifications',
        component: () => import('@/views/Notifications.vue'),
        meta: {
          requiresAuth: true,
          app: 'Jobseeker',
          title: 'Notifications'
        }
      },
    ],
    redirect: '/jobseeker/dashboard',
  },
  {
    path: '/profile/:slug',
    name: 'PublicProfile',
    component: () => import('@/views/PublicProfile.vue'),
    meta: {
      app: 'Jobseeker',
      title: 'Profile',
      requiresAuth: false
    }
  },
  {
    path: '/jobseeker/:catchAll(.*)',
    redirect: '/jobseeker/dashboard',
  },
]

const {startLoading, stopLoading } = useLoader();

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  startLoading();
  const authStore = useAuthStore()

  if (!authStore.isInitialised) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.log('Failed to get the profile')
    }
  }

  if (authStore.isAuthenticated) {
    if (!authStore.enums) {
      await authStore.getEnumData()
    }

    if (!authStore.industries) {
      await authStore.getIndustries()
    }
  }

  // Set document title based on route meta or route name
  if (to.meta?.title) {
    document.title = `${to.meta.app} - ${to.meta.title} - Job Dalal`
  } else if (to.name) {
    // Convert route name to title case
    const title = to.name
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .trim()
    document.title = `Jobseeker - ${title} - Job Dalal`
  } else {
    document.title = 'Jobseeker - Job Dalal'
  }

  if (!authStore.isAuthenticated && to?.path !== '/jobseeker/login') {
    next('/jobseeker/login')
  } else {
    if (false && authStore.isAuthenticated && !authStore.isProfileComplete && to?.path !== '/jobseeker/profile') {
      next('/jobseeker/profile')
    } else {
      next()
    }
  }
})

router.afterEach(() => {
  nextTick(() => {
    stopLoading()
  });
});

export default router