<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import Dropdown from 'primevue/dropdown'
import <PERSON><PERSON> from 'primevue/button'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  job: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'status-updated'])

const authStore = useAuthStore()
const jobStatuses = authStore.enums.statuses

const selectedStatus = ref(null)
const loading = ref(false)

// Computed property for dialog visibility
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// Watch for job changes and set default status
watch(() => props.job, (newJob) => {
  if (newJob) {
    selectedStatus.value = newJob.status
  }
}, { immediate: true })

// Watch for dialog visibility changes
watch(() => props.visible, (visible) => {
  if (visible && props.job) {
    selectedStatus.value = props.job.status
  }
})

const handleUpdate = async () => {
  if (!selectedStatus.value || selectedStatus.value === props.job.status) {
    emit('update:visible', false)
    return
  }

  loading.value = true
  try {
    // Emit the status update event
    emit('status-updated', {
      jobId: props.job.id,
      newStatus: selectedStatus.value
    })
    emit('update:visible', false)
  } catch (error) {
    console.error('Error updating job status:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  selectedStatus.value = props.job?.status
  emit('update:visible', false)
}

const getStatusOptions = () => {
  return Object.values(jobStatuses).map(status => ({
    value: status.id,
    label: status.label
  }))
}
</script>

<template>
  <Dialog 
    v-model:visible="dialogVisible" 
    modal 
    header="Change Job Status" 
    :style="{ width: '450px' }"
    :closable="false"
    :closeOnEscape="false"
  >
    <div class="status-dialog-content">
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Job Title
        </label>
        <div class="text-gray-900 font-medium">
          {{ job?.title }}
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Current Status
        </label>
        <div class="text-sm text-gray-600 mb-3">
          {{ jobStatuses[job?.status]?.label || job?.status }}
        </div>
      </div>

      <div class="mb-4">
        <label for="status-dropdown" class="block text-sm font-medium text-gray-700 mb-2">
          New Status
        </label>
        <Dropdown
          id="status-dropdown"
          v-model="selectedStatus"
          :options="getStatusOptions()"
          option-label="label"
          option-value="value"
          placeholder="Select a status"
          class="w-full"
          :class="{ 'p-invalid': !selectedStatus }"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button 
          label="Cancel" 
          text 
          @click="handleCancel"
          :disabled="loading"
        />
        <Button 
          label="Update" 
          @click="handleUpdate"
          :loading="loading"
          :disabled="!selectedStatus || selectedStatus === job?.status"
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
.status-dialog-content {
  padding: 0;
}

:deep(.p-dialog-content) {
  padding: 1.5rem;
}

:deep(.p-dialog-footer) {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

:deep(.p-dropdown) {
  width: 100%;
}

:deep(.p-dropdown-panel) {
  z-index: 1000;
}
</style> 