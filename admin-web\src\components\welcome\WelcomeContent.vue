<template>
  <div class="main-content">
    <!-- Welcome Pages Table -->
    <div class="table-container">
      <DataTable 
        :value="filteredWelcomePages" 
        class="welcome-table"
        :paginator="true" 
        :rows="10"
        :rows-per-page-options="[10, 20, 50]"
        stripedRows
        :rowHover="true"
        sort-field="updatedAt"
        :sort-order="-1"
        :scrollable="true"
        scroll-height="flex"
      >
        <Column field="cmp_name" header="Company" sortable>
          <template #body="slotProps">
            <div class="company-cell">
              <div class="company-logo" v-if="slotProps.data.logo_path">
                <img :src="slotProps.data.logo_path" :alt="slotProps.data.cmp_name" />
              </div>
              <div class="company-info">
                <span class="company-name">{{ slotProps.data.cmp_name }}</span>
                <span class="company-url">{{ slotProps.data.base_url || 'No URL' }}</span>
              </div>
            </div>
          </template>
        </Column>
        <Column field="message" header="Welcome Message" sortable>
          <template #body="slotProps">
            <span class="message-text" :title="slotProps.data.message">
              {{ slotProps.data.message }}
            </span>
          </template>
        </Column>
        <Column header="Features" style="width: 150px">
          <template #body="slotProps">
            <div class="features-tags">
              <Tag 
                v-if="slotProps.data.show_signup" 
                value="Signup" 
                severity="success" 
                class="feature-tag"
              />
              <Tag 
                v-if="slotProps.data.show_login" 
                value="Login" 
                severity="info" 
                class="feature-tag"
              />
            </div>
          </template>
        </Column>
        <Column header="Content" style="width: 120px">
          <template #body="slotProps">
            <div class="content-counts">
              <span class="count-item">{{ slotProps.data.descriptions?.length || 0 }} desc</span>
              <span class="count-item">{{ slotProps.data.testimonials?.length || 0 }} test</span>
            </div>
          </template>
        </Column>
        <Column field="updatedAt" header="Updated" sortable style="width: 150px">
          <template #body="slotProps">
            <span>{{ formatDate(slotProps.data.updatedAt) }}</span>
          </template>
        </Column>
        <Column header="Actions" style="width: 120px">
          <template #body="slotProps">
            <Button icon="pi pi-pencil" text rounded size="small" @click="$emit('edit', slotProps.data)" />
            <Button icon="pi pi-trash" text rounded size="small" severity="danger" @click="$emit('delete', slotProps.data)" />
            <Button icon="pi pi-check" text rounded size="small" :severity="slotProps.data.isActive ? 'success' : 'secondary'" @click="$emit('toggle', slotProps.data)" v-tooltip="slotProps.data.isActive ? 'Deactivate' : 'Activate'" />
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template>

<script setup>
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import { defineProps, defineEmits } from 'vue'

defineProps({
  filteredWelcomePages: Array,
  formatDate: Function
})
defineEmits(['edit', 'delete', 'toggle'])
</script>

<style scoped>
/* Use the same styles as the original main content/table */
</style> 