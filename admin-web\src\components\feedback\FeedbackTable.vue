<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Tag from 'primevue/tag';

const props = defineProps({
  feedbacks: Array,
  feedbackTypeOptions: Array,
  statusOptions: Array,
  searchTerm: String,
  selectedTypeFilter: Array,
  selectedStatusFilter: Array,
  dateRange: Array,
  loading: <PERSON><PERSON><PERSON>
});
const emit = defineEmits([
  'edit', 'delete', 'status-change', 'filter-change', 'clear-filters', 'create', 'export', 'view', 'resolve'
]);

const localSearch = ref(props.searchTerm || '');
const localType = ref(props.selectedTypeFilter ? [...props.selectedTypeFilter] : []);
const localStatus = ref(props.selectedStatusFilter ? [...props.selectedStatusFilter] : []);
const localDateRange = ref(props.dateRange ? [...props.dateRange] : []);

watch(() => [props.searchTerm, props.selectedTypeFilter, props.selectedStatusFilter, props.dateRange],
  ([search, type, status, date]) => {
    localSearch.value = search || '';
    localType.value = type ? [...type] : [];
    localStatus.value = status ? [...status] : [];
    localDateRange.value = date ? [...date] : [];
  },
  { immediate: true }
);

function onFilterChange() {
  emit('filter-change', {
    searchTerm: localSearch.value,
    selectedTypeFilter: localType.value,
    selectedStatusFilter: localStatus.value,
    dateRange: localDateRange.value
  });
}

function clearFilters() {
  localSearch.value = '';
  localType.value = [];
  localStatus.value = [];
  localDateRange.value = [];
  emit('clear-filters');
}

function getFeedbackTypeColor(type) {
  const colors = {
    SUGGESTION: 'info',
    BUG_REPORT: 'danger',
    FEATURE_REQUEST: 'success',
    COMPLAINT: 'warning',
    COMPLIMENT: 'success',
    GENERAL: 'secondary'
  };
  return colors[type] || 'secondary';
}

function getStatusColor(status) {
  const colors = {
    PENDING: 'info',
    IN_REVIEW: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger',
    IMPLEMENTED: 'success'
  };
  return colors[status] || 'secondary';
}

function getStatusLabel(status) {
  const labels = {
    PENDING: 'Pending',
    IN_REVIEW: 'In Review',
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    IMPLEMENTED: 'Implemented'
  };
  return labels[status] || status;
}

function formatDate(date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
}
</script>

<template>
  <div class="feedback-table flex flex-1 min-h-0">
    <!-- Feedback Table -->
    <div class="flex-1 min-h-0 flex flex-col">
      <DataTable :value="feedbacks" class="feedbacks-table flex-1 min-h-0" :loading="loading" scrollHeight="flex">
        <Column field="type" header="Type" :sortable="true">
          <template #body="slotProps">
            <Tag :value="slotProps.data.type.replace(/_/g, ' ')" :severity="getFeedbackTypeColor(slotProps.data.type)" />
          </template>
        </Column>
        <Column field="title" header="Title" :sortable="true" />
        <Column field="description" header="Description" :sortable="true">
          <template #body="slotProps">
            <span class="line-clamp-2">{{ slotProps.data.description }}</span>
          </template>
        </Column>
        <Column field="status" header="Status" :sortable="true">
          <template #body="slotProps">
            <Tag :value="getStatusLabel(slotProps.data.status)" :severity="getStatusColor(slotProps.data.status)" />
          </template>
        </Column>
        <Column field="userId" header="Submitted By" :sortable="true" />
        <Column field="createdAt" header="Created" :sortable="true">
          <template #body="slotProps">
            {{ formatDate(slotProps.data.createdAt) }}
          </template>
        </Column>
        <Column header="Actions">
          <template #body="slotProps">
            <Button icon="pi pi-eye" text rounded size="small" @click="$emit('view', slotProps.data)" v-tooltip="'View'" />
            <Button icon="pi pi-pencil" text rounded size="small" @click="$emit('edit', slotProps.data)" v-tooltip="'Edit'" />
            <Button
              v-if="!['completed','acknowledged'].includes(slotProps.data.status)"
              icon="pi pi-check"
              text rounded size="small" severity="success"
              @click="$emit('resolve', slotProps.data)"
              v-tooltip="'Mark as Resolved'"
            />
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template> 