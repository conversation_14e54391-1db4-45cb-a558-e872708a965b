const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'


// API Configuration
export const API_CONFIG = {
  baseURL: API_BASE_URL,
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
  
  // Feature flags for development
  USE_MOCK_DATA: false, // Set to false to use real API
  
  // Mock configuration
  MOCK_DELAY: 400, // Simulate network delay in ms
  
  // Service-specific mock flags (for granular control)
  MOCK_FLAGS: {
    auth: true,
    jobs: true,
    dashboard: true,
    jobEnums: true,
    industries: true,
    applications: false, // Example: use real API for applications
    profile: false
  }
}

// Helper function to check if mock should be used for a service
export const shouldUseMock = (serviceName) => {
  // Global flag takes precedence
  if (!API_CONFIG.USE_MOCK_DATA) {
    return false
  }
  
  // Check service-specific flag
  return API_CONFIG.MOCK_FLAGS[serviceName] ?? false
}

// Environment-based configuration
if (import.meta.env.PROD) {
  // In production, disable all mocks by default
  API_CONFIG.USE_MOCK_DATA = false
  Object.keys(API_CONFIG.MOCK_FLAGS).forEach(key => {
    API_CONFIG.MOCK_FLAGS[key] = false
  })
}

export default API_CONFIG