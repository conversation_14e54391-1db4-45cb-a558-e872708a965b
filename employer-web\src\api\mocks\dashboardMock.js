// Mock API for Dashboard endpoint
// GET: /jobs/employer/dashboard

const delay = (ms = 400) => new Promise(resolve => setTimeout(resolve, ms))

export const dashboardMock = {
  async getStats() {
    await delay(400)
    
    const now = new Date()
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    
    // Mock dashboard statistics
    const mockStats = {
      // Job statistics
      totalJobs: 12847,
      activeJobs: 8934,
      recentJobs: 1247, // Jobs posted in last 30 days
      featuredJobs: 234,
      urgentJobs: 89,
      
      // User statistics
      totalApplications: 12,
      pendingApplications: 5,
      interviewInvitations: 2,
      jobOffers: 1,
      profileViews: 28,
      jobViews: 1234,
      savedJobs: 8,
      
      // Salary insights
      avgSalary: 65000,
      salaryRange: {
        min: 35000,
        max: 120000
      },
      
      // Growth metrics
      jobGrowthRate: 15, // Percentage growth this month
      applicationSuccessRate: 8.3, // Percentage of applications that lead to interviews
      
      // Top departments with job counts
      topDepartments: [
        { name: 'Construction', count: 3245, growth: 12 },
        { name: 'Manufacturing', count: 2890, growth: 8 },
        { name: 'Logistics', count: 2156, growth: 18 },
        { name: 'Maintenance', count: 1834, growth: 5 },
        { name: 'Security', count: 1456, growth: 22 },
        { name: 'Hospitality', count: 1266, growth: -3 }
      ],
      
      // Recent activity for the user
      recentActivity: [
        {
          id: 1,
          type: 'application',
          title: 'Applied to Construction Worker',
          description: 'ABC Construction Company',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          icon: 'pi pi-send',
          color: '#3b82f6'
        },
        {
          id: 2,
          type: 'save',
          title: 'Saved Warehouse Associate',
          description: 'XYZ Logistics',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          icon: 'pi pi-heart',
          color: '#ef4444'
        },
        {
          id: 3,
          type: 'view',
          title: 'Profile viewed by employer',
          description: 'TechCorp Inc.',
          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          icon: 'pi pi-eye',
          color: '#10b981'
        },
        {
          id: 4,
          type: 'application',
          title: 'Applied to Maintenance Technician',
          description: 'Industrial Solutions Ltd.',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          icon: 'pi pi-send',
          color: '#3b82f6'
        },
        {
          id: 5,
          type: 'interview',
          title: 'Interview scheduled',
          description: 'Phone interview with BuildCorp',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          icon: 'pi pi-users',
          color: '#f59e0b'
        }
      ],
      
      // Recommended jobs (simplified)
      recommendedJobs: [
        {
          id: 1,
          title: 'Construction Worker',
          company: 'ABC Construction',
          location: 'New York, NY',
          salaryMin: 45000,
          salaryMax: 65000,
          color: '#3b82f6',
          postedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          isUrgent: false,
          isFeatured: true
        },
        {
          id: 2,
          title: 'Warehouse Associate',
          company: 'XYZ Logistics',
          location: 'Los Angeles, CA',
          salaryMin: 38000,
          salaryMax: 52000,
          color: '#10b981',
          postedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          isUrgent: true,
          isFeatured: false
        },
        {
          id: 3,
          title: 'Maintenance Technician',
          company: 'Industrial Solutions',
          location: 'Chicago, IL',
          salaryMin: 50000,
          salaryMax: 70000,
          color: '#f59e0b',
          postedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          isUrgent: false,
          isFeatured: true
        },
        {
          id: 4,
          title: 'Security Guard',
          company: 'SecureTech Services',
          location: 'Miami, FL',
          salaryMin: 35000,
          salaryMax: 48000,
          color: '#ef4444',
          postedDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
          isUrgent: false,
          isFeatured: false
        },
        {
          id: 5,
          title: 'Delivery Driver',
          company: 'FastDelivery Inc.',
          location: 'Phoenix, AZ',
          salaryMin: 40000,
          salaryMax: 55000,
          color: '#8b5cf6',
          postedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          isUrgent: true,
          isFeatured: false
        }
      ],
      
      // Market insights
      marketInsights: {
        hotSkills: ['Safety Protocols', 'Heavy Machinery', 'Blueprint Reading', 'Forklift Operation', 'Quality Control'],
        trendingLocations: ['Austin, TX', 'Denver, CO', 'Nashville, TN', 'Phoenix, AZ', 'Tampa, FL'],
        salaryTrends: {
          construction: { current: 58000, change: 5.2 },
          manufacturing: { current: 52000, change: 3.8 },
          logistics: { current: 48000, change: 7.1 },
          maintenance: { current: 55000, change: 4.5 },
          security: { current: 42000, change: 2.9 }
        }
      },
      
      // Application insights
      applicationInsights: {
        bestTimeToApply: 'Tuesday-Thursday, 9-11 AM',
        avgResponseTime: '3-5 business days',
        competitionLevel: 'Moderate', // Low, Moderate, High
        successTips: [
          'Complete your profile to increase visibility',
          'Add relevant skills and certifications',
          'Apply within 24 hours of job posting',
          'Customize your application for each job'
        ]
      },
      
      // Applications received for employer jobs (mock)
      applications: [
        {
          id: 1,
          jobTitle: 'Frontend Developer',
          applicantName: 'John Doe',
          applicationDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'New'
        },
        {
          id: 2,
          jobTitle: 'Backend Developer',
          applicantName: 'Jane Smith',
          applicationDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'Reviewed'
        },
        {
          id: 3,
          jobTitle: 'UI/UX Designer',
          applicantName: 'Alice Johnson',
          applicationDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'Shortlisted'
        }
      ]
    }
    
    return {
      data: mockStats,
      success: true,
      status: 200,
      message: 'Dashboard statistics retrieved successfully'
    }
  },

  async getRecentActivity(limit = 10) {
    await delay(300)
    
    const activities = [
      {
        id: 1,
        type: 'application',
        title: 'Applied to Construction Worker',
        description: 'ABC Construction Company',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-send',
        color: '#3b82f6'
      },
      {
        id: 2,
        type: 'save',
        title: 'Saved Warehouse Associate',
        description: 'XYZ Logistics',
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-heart',
        color: '#ef4444'
      },
      {
        id: 3,
        type: 'view',
        title: 'Profile viewed by employer',
        description: 'TechCorp Inc.',
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-eye',
        color: '#10b981'
      },
      {
        id: 4,
        type: 'application',
        title: 'Applied to Maintenance Technician',
        description: 'Industrial Solutions Ltd.',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-send',
        color: '#3b82f6'
      },
      {
        id: 5,
        type: 'interview',
        title: 'Interview scheduled',
        description: 'Phone interview with BuildCorp',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-users',
        color: '#f59e0b'
      },
      {
        id: 6,
        type: 'profile_update',
        title: 'Profile updated',
        description: 'Added new skills and experience',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-user',
        color: '#8b5cf6'
      },
      {
        id: 7,
        type: 'save',
        title: 'Saved Forklift Operator',
        description: 'Warehouse Solutions Inc.',
        timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-heart',
        color: '#ef4444'
      },
      {
        id: 8,
        type: 'application',
        title: 'Applied to Security Guard',
        description: 'SecureTech Services',
        timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'pi pi-send',
        color: '#3b82f6'
      }
    ]
    
    return {
      data: activities.slice(0, limit),
      success: true,
      status: 200,
      message: 'Recent activity retrieved successfully'
    }
  },

  async getRecommendations(limit = 5) {
    await delay(350)
    
    const recommendations = [
      {
        id: 1,
        title: 'Construction Worker',
        company: 'ABC Construction',
        location: 'New York, NY',
        salaryMin: 45000,
        salaryMax: 65000,
        color: '#3b82f6',
        postedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        isUrgent: false,
        isFeatured: true,
        matchScore: 95,
        matchReasons: ['Skills match', 'Location preference', 'Salary range']
      },
      {
        id: 2,
        title: 'Warehouse Associate',
        company: 'XYZ Logistics',
        location: 'Los Angeles, CA',
        salaryMin: 38000,
        salaryMax: 52000,
        color: '#10b981',
        postedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        isUrgent: true,
        isFeatured: false,
        matchScore: 88,
        matchReasons: ['Experience level', 'Industry preference']
      },
      {
        id: 3,
        title: 'Maintenance Technician',
        company: 'Industrial Solutions',
        location: 'Chicago, IL',
        salaryMin: 50000,
        salaryMax: 70000,
        color: '#f59e0b',
        postedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        isUrgent: false,
        isFeatured: true,
        matchScore: 82,
        matchReasons: ['Skills match', 'Career growth potential']
      },
      {
        id: 4,
        title: 'Security Guard',
        company: 'SecureTech Services',
        location: 'Miami, FL',
        salaryMin: 35000,
        salaryMax: 48000,
        color: '#ef4444',
        postedDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        isUrgent: false,
        isFeatured: false,
        matchScore: 75,
        matchReasons: ['Schedule flexibility', 'Entry level friendly']
      },
      {
        id: 5,
        title: 'Delivery Driver',
        company: 'FastDelivery Inc.',
        location: 'Phoenix, AZ',
        salaryMin: 40000,
        salaryMax: 55000,
        color: '#8b5cf6',
        postedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        isUrgent: true,
        isFeatured: false,
        matchScore: 70,
        matchReasons: ['Location match', 'Immediate start']
      }
    ]
    
    return {
      data: recommendations.slice(0, limit),
      success: true,
      status: 200,
      message: 'Job recommendations retrieved successfully'
    }
  }
}