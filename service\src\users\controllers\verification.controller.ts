import { Controller, Get, Post, Put, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { VerificationService } from '../services/verification.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { VerificationEntity } from '../entities/verification.entity';

@ApiTags('Verification')
@Controller('verification')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class VerificationController {
  constructor(private readonly verificationService: VerificationService) {}

  @Post()
  @ApiOperation({ summary: 'Create verification details' })
  @ApiResponse({ status: 201, description: 'Verification details created successfully' })
  async create(@Request() req, @Body() verificationData: Partial<VerificationEntity>) {
    return this.verificationService.create(req.user.id, verificationData);
  }

  @Put()
  @ApiOperation({ summary: 'Update verification details' })
  @ApiResponse({ status: 200, description: 'Verification details updated successfully' })
  async update(@Request() req, @Body() verificationData: Partial<VerificationEntity>) {
    return this.verificationService.update(req.user.id, verificationData);
  }

  @Get('my-details')
  @ApiOperation({ summary: 'Get own verification details' })
  @ApiResponse({ status: 200, description: 'Verification details retrieved successfully' })
  async getMyDetails(@Request() req) {
    return this.verificationService.getByUserId(req.user.id);
  }

  @Get('search')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Search verifications' })
  @ApiResponse({ status: 200, description: 'Verifications retrieved successfully' })
  async searchVerifications(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('searchTerm') searchTerm?: string,
    @Query('isVerified') isVerified?: string,
  ) {
    return this.verificationService.searchVerifications(
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined,
      searchTerm,
      isVerified ? isVerified === 'true' : undefined,
    );
  }

  @Post(':userId/verify-aadhar')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Verify Aadhar' })
  @ApiResponse({ status: 200, description: 'Aadhar verified successfully' })
  async verifyAadhar(@Param('userId') userId: string, @Request() req) {
    return this.verificationService.verifyAadhar(userId, req.user.id);
  }

  @Post(':userId/verify-pan')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Verify PAN' })
  @ApiResponse({ status: 200, description: 'PAN verified successfully' })
  async verifyPan(@Param('userId') userId: string, @Request() req) {
    return this.verificationService.verifyPan(userId, req.user.id);
  }
}
