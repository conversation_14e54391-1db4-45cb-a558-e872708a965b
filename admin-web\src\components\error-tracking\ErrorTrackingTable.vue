<script setup>
import { defineProps } from 'vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Tag from 'primevue/tag';
import Button from 'primevue/button';

const props = defineProps({
  errors: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  total: {
    type: Number,
    default: 0,
  },
  page: {
    type: Number,
    default: 1,
  },
  limit: {
    type: Number,
    default: 10,
  },
  sortField: {
    type: String,
    default: 'createdAt',
  },
  sortOrder: {
    type: Number,
    default: -1,
  },
});

const emit = defineEmits(['view-error', 'toggle-resolution', 'page', 'sort', 'edit-error']);

const getSeverityColor = (severity) => {
  const colors = {
    LOW: 'info',
    MEDIUM: 'warning',
    HIGH: 'danger',
    CRITICAL: 'danger',
  };
  return colors[severity] || 'info';
};

const formatDate = (date) => {
  if (!date) return '';
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

const formatUserAgent = (userAgent) => {
  if (!userAgent) return 'Unknown';
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  return 'Other';
};
</script>

<template>
  <div class="flex-1 min-h-0 flex flex-col">
    <DataTable
      :value="errors"
      :loading="loading"
      paginator
      :rows="limit"
      :first="(page - 1) * limit"
      :totalRecords="total"
      :rowsPerPageOptions="[10, 20, 50]"
      class="p-datatable-customers flex-1 min-h-0"
      data-key="id"
      :sortField="sortField"
      :sortOrder="sortOrder"
      scrollHeight="flex"
      @page="emit('page', $event)"
      @sort="emit('sort', $event)"
    >
      <template #empty>No errors found.</template>
      <template #loading>Loading error data...</template>

      <Column header="Status" style="width: 8rem">
        <template #body="slotProps">
          <Tag
            :value="slotProps.data.resolved ? 'Resolved' : 'Active'"
            :severity="slotProps.data.resolved ? 'success' : 'danger'"
          />
        </template>
      </Column>

      <Column field="errorMessage" header="Error Message" :sortable="true">
        <template #body="slotProps">
          <div class="font-bold">{{ slotProps.data.errorMessage }}</div>
          <div class="text-sm text-gray-500">
            in <span class="font-medium">{{ slotProps.data.screen || 'N/A' }}</span>
          </div>
        </template>
      </Column>

      <Column header="Severity" field="severity" :sortable="true" style="width: 10rem">
          <template #body="slotProps">
              <Tag :value="slotProps.data.severity" :severity="getSeverityColor(slotProps.data.severity)" />
          </template>
      </Column>

      <Column header="Occurrences" field="occurrences" :sortable="true" style="width: 10rem" />

      <Column header="Detected On" field="createdAt" :sortable="true" style="width: 12rem">
        <template #body="slotProps">
          {{ formatDate(slotProps.data.createdAt) }}
        </template>
      </Column>
      
      <Column header="Browser" style="width: 8rem">
          <template #body="slotProps">
              <span>{{ formatUserAgent(slotProps.data.userAgent) }}</span>
          </template>
      </Column>

      <Column header="Actions" style="width: 12rem; text-align: center">
        <template #body="slotProps">
          <Button
            icon="pi pi-search"
            class="p-button-rounded p-button-text"
            @click="emit('view-error', slotProps.data)"
            v-tooltip="'View Details'"
          />
          <Button
            :icon="slotProps.data.resolved ? 'pi pi-undo' : 'pi pi-check'"
            class="p-button-rounded p-button-text"
            :class="slotProps.data.resolved ? 'p-button-warning' : 'p-button-success'"
            @click="emit('toggle-resolution', slotProps.data)"
            :v-tooltip="slotProps.data.resolved ? 'Mark as Unresolved' : 'Mark as Resolved'"
          />
          <Button
            icon="pi pi-pencil"
            class="p-button-rounded p-button-text p-button-info"
            @click="emit('edit-error', slotProps.data)"
            v-tooltip="'Edit Error Log'"
          />
        </template>
      </Column>
    </DataTable>
  </div>
</template> 