import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
  Delete,
  Patch,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { FeedService } from '../services/feed.service';
import { CreateFeedDto } from '../dto/create-feed.dto';
import { FeedEntity, FeedStatus } from '../entities/feed.entity';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';

@ApiTags('Feeds')
@Controller('feeds')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class FeedController {
  constructor(private readonly feedService: FeedService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.EMPLOYER)
  @ApiOperation({
    summary: 'Create a new feed',
    description:
      'Create a new news or article feed. The feed will be in PENDING status until approved by an admin.',
  })
  @ApiResponse({
    status: 201,
    description: 'Feed created successfully',
    type: FeedEntity,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  async create(@Request() req, @Body() createFeedDto: CreateFeedDto): Promise<FeedEntity> {
    return this.feedService.create(req.user.id, createFeedDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all feeds',
    description:
      'Retrieve a paginated list of feeds. By default, only approved feeds are returned.',
  })
  @ApiQuery({
    name: 'includePending',
    required: false,
    type: Boolean,
    description: 'Include pending feeds in the response (admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all approved feeds',
    type: PaginatedResponseDto<FeedEntity>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(
    @Query() paginationDto: PaginationDto,
    @Query('includePending') includePending?: boolean,
  ): Promise<PaginatedResponseDto<FeedEntity>> {
    return this.feedService.findAll(paginationDto, includePending);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a feed by ID',
    description: 'Retrieve detailed information about a specific feed.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the feed',
    type: FeedEntity,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Feed not found' })
  async findOne(@Param('id') id: string): Promise<FeedEntity> {
    return this.feedService.findOne(id);
  }

  @Patch(':id/status')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Update feed status',
    description: 'Approve or reject a feed. Only admins can perform this action.',
  })
  @ApiResponse({
    status: 200,
    description: 'Feed status updated successfully',
    type: FeedEntity,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Feed not found' })
  async updateStatus(
    @Param('id') id: string,
    @Request() req,
    @Body('status') status: FeedStatus,
    @Body('comment') comment?: string,
  ): Promise<FeedEntity> {
    return this.feedService.updateStatus(id, req.user.id, status, comment);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.EMPLOYER)
  @ApiOperation({
    summary: 'Delete a feed',
    description:
      'Delete a feed. Admins can delete any feed, while employers can only delete their own feeds.',
  })
  @ApiResponse({ status: 200, description: 'Feed deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Feed not found' })
  async delete(@Param('id') id: string, @Request() req): Promise<void> {
    return this.feedService.delete(id, req.user.id, req.user.role);
  }
}
