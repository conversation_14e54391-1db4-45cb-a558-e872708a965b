<script setup>
import { computed, defineProps, defineEmits, ref } from 'vue';
import Drawer from 'primevue/drawer';
import Select from 'primevue/select';
import Float<PERSON>abel from 'primevue/floatlabel';
import InputNumber from 'primevue/inputnumber';
import Button from 'primevue/button';

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  industries: {
    type: Array,
    default: () => []
  },
  jobTypes: {
    type: Array,
    default: () => []
  },
  urgencies: {
    type: Array,
    default: () => []
  },
  experienceLevels: {
    type: Array,
    default: () => []
  },
  paymentTypes: {
    type: Array,
    default: () => []
  },
  jobStatuses: {
    type: Array,
    default: () => []
  },
  selectedIndustry: String,
  selectedJobType: String,
  selectedUrgency: String,
  selectedExperienceLevel: String,
  selectedPaymentType: String,
  selectedJobStatus: String,
  minSalary: Number,
  maxSalary: Number
});

const emit = defineEmits([
  'update:visible',
  'update:selectedIndustry',
  'update:selectedJobType',
  'update:selectedUrgency',
  'update:selectedExperienceLevel',
  'update:selectedPaymentType',
  'update:selectedJobStatus',
  'update:minSalary',
  'update:maxSalary',
  'apply-filters',
  'clear-filters'
]);

const internalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const industry = computed({
  get: () => props.selectedIndustry,
  set: (value) => emit('update:selectedIndustry', value)
});

const jobType = computed({
  get: () => props.selectedJobType,
  set: (value) => emit('update:selectedJobType', value)
});

const urgency = computed({
  get: () => props.selectedUrgency,
  set: (value) => emit('update:selectedUrgency', value)
});

const experienceLevel = computed({
  get: () => props.selectedExperienceLevel,
  set: (value) => emit('update:selectedExperienceLevel', value)
});

const paymentType = computed({
  get: () => props.selectedPaymentType,
  set: (value) => emit('update:selectedPaymentType', value)
});

const jobStatus = computed({
  get: () => props.selectedJobStatus,
  set: (value) => emit('update:selectedJobStatus', value)
});

const minSal = computed({
  get: () => props.minSalary,
  set: (value) => emit('update:minSalary', value)
});

const maxSal = computed({
  get: () => props.maxSalary,
  set: (value) => emit('update:maxSalary', value)
});

const applyFilters = () => {
  emit('apply-filters');
  internalVisible.value = false;
};

const clearFilters = () => {
  emit('clear-filters');
  internalVisible.value = false;
};
</script>

<template>
  <Drawer v-model:visible="internalVisible" header="Filters" position="right" :pt="{
    root: { class: 'w-[500px] p-4' }
  }">
    <div class="flex flex-col h-full">
      <div class="flex-grow space-y-6">
        <FloatLabel>
          <Select v-model="industry" :options="industries" optionLabel="label" optionValue="value" class="w-full"
            placeholder="Select Industry" />
          <label>Industry</label>
        </FloatLabel>

        <FloatLabel>
          <Select v-model="jobType" :options="jobTypes" optionLabel="label" optionValue="value" class="w-full"
            placeholder="Select Job Type" />
          <label>Job Type</label>
        </FloatLabel>

        <FloatLabel>
          <Select v-model="urgency" :options="urgencies" optionLabel="label" optionValue="value" class="w-full"
            placeholder="Select Urgency" />
          <label>Urgency</label>
        </FloatLabel>

        <FloatLabel>
          <Select v-model="experienceLevel" :options="experienceLevels" optionLabel="label" optionValue="value"
            class="w-full" placeholder="Select Experience Level" />
          <label>Experience Level</label>
        </FloatLabel>

        <FloatLabel>
          <Select v-model="paymentType" :options="paymentTypes" optionLabel="label" optionValue="value" class="w-full"
            placeholder="Select Payment Type" />
          <label>Payment Type</label>
        </FloatLabel>

        <FloatLabel>
          <Select v-model="jobStatus" :options="jobStatuses" optionLabel="label" optionValue="value" class="w-full"
            placeholder="Select Job Status" />
          <label>Job Status</label>
        </FloatLabel>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Salary Range</label>
          <div class="flex items-center space-x-2">
            <InputNumber v-model="minSal" placeholder="Min" class="w-full" />
            <span>-</span>
            <InputNumber v-model="maxSal" placeholder="Max" class="w-full" />
          </div>
        </div>
      </div>
      <div class="flex justify-end items-center pt-4 border-t mt-4">
        <Button label="Clear" severity="secondary" @click="clearFilters" class="mr-2" />
        <Button label="Apply" @click="applyFilters" />
      </div>
    </div>
  </Drawer>
</template>



