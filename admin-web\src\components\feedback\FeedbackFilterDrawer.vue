<script setup>
import { ref, reactive, watch, computed } from 'vue';
import Drawer from 'primevue/drawer';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import MultiSelect from 'primevue/multiselect';
import Calendar from 'primevue/calendar';

const props = defineProps({
  visible: Boolean,
  filters: Object,
  feedbackTypeOptions: Array,
  statusOptions: Array
});
const emit = defineEmits(['close', 'apply-filters', 'reset-filters']);

const localFilters = ref({});

const FeedbackStatus = {
  PENDING: 'PENDING',
  IN_REVIEW: 'IN_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  IMPLEMENTED: 'IMPLEMENTED'
};

const statusOptions = [
  { label: 'Pending', value: 'PENDING' },
  { label: 'In Review', value: 'IN_REVIEW' },
  { label: 'Approved', value: 'APPROVED' },
  { label: 'Rejected', value: 'REJECTED' },
  { label: 'Implemented', value: 'IMPLEMENTED' }
];

watch(() => props.filters, (newFilters) => {
  localFilters.value = JSON.parse(JSON.stringify(newFilters));
}, { immediate: true, deep: true });

function apply() {
  emit('apply-filters', localFilters.value);
  emit('close');
}

function reset() {
  emit('reset-filters');
  emit('close');
}
</script>

<template>
  <Drawer :visible="visible" @update:visible="emit('close')" position="right" style="width: 350px">
    <template #header>
      <h3 class="font-bold">Filters</h3>
    </template>
    <div class="p-4">
      <div class="form-group mb-4">
        <label class="font-semibold">Search Term</label>
        <InputText v-model="localFilters.searchTerm" class="w-full mt-1" placeholder="Search by title, description, user..." />
      </div>
      <div class="form-group mb-4">
        <label class="font-semibold">Type</label>
        <MultiSelect v-model="localFilters.selectedTypeFilter" :options="feedbackTypeOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select types" />
      </div>
      <div class="form-group mb-4">
        <label class="font-semibold">Status</label>
        <MultiSelect v-model="localFilters.selectedStatusFilter" :options="statusOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select status" />
      </div>
      <div class="form-group mb-4">
        <label class="font-semibold">Date Range</label>
        <Calendar v-model="localFilters.dateRange" selectionMode="range" :manualInput="false" class="w-full mt-1" />
      </div>
    </div>
    <template #footer>
      <Button label="Reset" severity="secondary" @click="reset" />
      <Button label="Apply Filters" @click="apply" class="ml-2" />
    </template>
  </Drawer>
</template> 