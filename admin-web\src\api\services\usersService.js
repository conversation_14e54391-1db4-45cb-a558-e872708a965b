import httpClient from '../httpClient';
import { ENDPOINTS } from '../endpoints';

export const usersService = {
    async getAllUsers(page = 1, limit = 200, params = {}) {
    try {
      const response = await httpClient.get(ENDPOINTS.USERS.GET_ALL(page, limit, params))
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch users')
    }
  },
  async createUser(userData) {
    try {
      const response = await httpClient.post(ENDPOINTS.USERS.CREATE, userData)
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to create user')
    }
  },
  async updateUser(id, userData) {
    try {
      const response = await httpClient.put(ENDPOINTS.USERS.UPDATE(id), userData)
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to update user')
    }
  },
  async updateUserProfile(userId, profileData) {
    try {
      const response = await httpClient.put(ENDPOINTS.USERS.UPDATE_PROFILE(userId), profileData)
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to update user profile')
    }
  },
  async deleteUser(id) {
    try {
      const response = await httpClient.delete(ENDPOINTS.USERS.DELETE(id))
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to delete user')
    }
  },
  async blockUser(id) {
    try {
      const response = await httpClient.post(ENDPOINTS.USERS.BLOCK(id))
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to block user')
    }
  },
  async unblockUser(id) {
    try {
      const response = await httpClient.post(ENDPOINTS.USERS.UNBLOCK(id))
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to unblock user')
    }
  },
}