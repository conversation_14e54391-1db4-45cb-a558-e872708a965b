import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  private cookieMap = {
    ADMIN: 'ac.t',
    EMPLOYER: 'eac.t',
    JOB_SEEKER: 'jsac.t',
  };

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Get the request object
    const request = context.switchToHttp().getRequest();

    // Handle case sensitivity in headers
    let authHeader = request.headers.authorization || request.headers.Authorization;

    if (!authHeader) {
      const appType = request.headers.jdu;

      authHeader = request.cookies[this.cookieMap[appType]];

      authHeader = authHeader ? 'Bearer ' + authHeader : null;
    }

    // Check if authorization header exists
    if (!authHeader) {
      throw new UnauthorizedException('No authorization token provided');
    }

    // Normalize header for consistency
    request.headers.authorization = authHeader;

    // Check if the authorization header has the correct format
    const [type, token] = authHeader.split(' ');
    if (type !== 'Bearer' || !token) {
      throw new UnauthorizedException(
        'Invalid authorization header format. Expected: Bearer <token>',
      );
    }

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any) {
    // Handle different types of errors
    if (err || !user) {
      if (info?.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Token has expired');
      }
      if (info?.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid token');
      }
      if (info?.name === 'NotBeforeError') {
        throw new UnauthorizedException('Token not active');
      }
      if (info?.message === 'No auth token') {
        throw new UnauthorizedException('No authorization token provided');
      }
      throw new UnauthorizedException('Authentication failed');
    }

    // Check if user is blocked
    if (user.isBlocked) {
      throw new UnauthorizedException('User account is blocked');
    }

    return user;
  }
}
