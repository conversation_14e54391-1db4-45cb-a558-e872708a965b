<template>
  <Applicants />
</template>

<script setup>
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import Applicants from '@/components/applications/Applicants.vue'

const appStore = useAppStore();

onMounted(() => {
  appStore.pageInfo = {
    title: 'Applicants',
    subTitle: ''
  }
})
</script>

<style scoped>
.applicants-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style> 