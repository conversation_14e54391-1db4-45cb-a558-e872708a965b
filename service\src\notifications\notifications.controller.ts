import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { NotificationType } from './entities/notification.entity';
import { ApiResponseDto } from '@/welcome/dto/api-response.dto';
@Controller('notifications')
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  async getUserNotifications(
    @Query('userId') userId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('isRead') isRead?: boolean,
    @Query('type') type?: NotificationType,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.notificationsService.findAll(userId, {
      page: Number(page),
      limit: Number(limit),
      isRead,
      type,
    });
    return ApiResponseDto.success(result, HttpStatus.OK);
  }

  @Post('read/:id')
  async markAsRead(@Param('id') id: string, @Body('userId') userId: string) {
    return this.notificationsService.markAsRead(id, userId);
  }

  @Post('read-all')
  async markAllAsRead(@Body('userId') userId: string) {
    return this.notificationsService.markAllAsRead(userId);
  }

  @Delete(':id')
  async deleteNotification(@Param('id') id: string, @Body('userId') userId: string) {
    return this.notificationsService.delete(id, userId);
  }

  // Endpoint to create a notification for a user
  @Post('create')
  async createNotification(@Body() body: { userId: string; type: NotificationType; data: any }) {
    return this.notificationsService.create(body);
  }

  // Endpoint to trigger job created notifications
  @Post('notify-job-created')
  async notifyJobCreated(@Body() body: { jobId: string; employerId: string }, @Request() req) {
    return this.notificationsService.notifyJobCreated(body, req.user);
  }

  // Endpoint to trigger job updated notifications
  @Post('notify-job-updated')
  async notifyJobUpdated(@Body() body: { jobId: string; employerId: string }, @Request() req) {
    return this.notificationsService.notifyJobUpdated(body, req.user);
  }

  // Endpoint to trigger application status notifications
  @Post('notify-application-status')
  async notifyApplicationStatus(
    @Body() body: { applicationId: string; status: string },
    @Request() req,
  ) {
    return this.notificationsService.notifyApplicationStatus(body, req.user);
  }
}
