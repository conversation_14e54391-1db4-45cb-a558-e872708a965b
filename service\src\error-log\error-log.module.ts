import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ErrorLogController } from './controllers/error-log.controller';
import { ErrorLogService } from './services/error-log.service';
import { ErrorLogEntity } from './entities/error-log.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ErrorLogEntity])],
  controllers: [ErrorLogController],
  providers: [ErrorLogService],
  exports: [ErrorLogService],
})
export class ErrorLogModule {}
