import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsObject } from 'class-validator';
import { FeedbackType } from '../entities/feedback.entity';

export class CreateFeedbackDto {
  @ApiProperty({
    description: 'Type of feedback',
    enum: FeedbackType,
    example: FeedbackType.SUGGESTION,
  })
  @IsEnum(FeedbackType)
  type: FeedbackType;

  @ApiProperty({
    description: 'Title or subject of the feedback',
    example: 'Add dark mode support',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Detailed description of the feedback',
    example:
      'It would be great to have a dark mode option for better visibility in low-light conditions.',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Additional metadata or attachments',
    example: { screenshots: ['url1', 'url2'] },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
