import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl } from 'class-validator';

export class CreateJobApplicationDto {
  @ApiProperty({
    description: 'Cover letter for the job application',
    required: false,
    example: 'I am excited to apply for this position...',
  })
  @IsString()
  @IsOptional()
  coverLetter?: string;

  @ApiProperty({
    description: "URL to the applicant's resume",
    required: false,
    example: 'https://example.com/resume.pdf',
  })
  @IsUrl()
  @IsOptional()
  resumeUrl?: string;

  @ApiProperty({
    description: 'Interview date (dd/mm/yyyy)',
    required: false,
    example: '25/06/2024',
  })
  @IsString()
  @IsOptional()
  interviewDate?: string;

  @ApiProperty({ description: 'Interview time', required: false, example: '10:00 AM' })
  @IsString()
  @IsOptional()
  interviewTime?: string;

  @ApiProperty({ description: 'Interview location', required: false, example: 'Office HQ' })
  @IsString()
  @IsOptional()
  interviewLocation?: string;

  @ApiProperty({ description: 'Interview notes', required: false })
  @IsString()
  @IsOptional()
  interviewNotes?: string;
}
