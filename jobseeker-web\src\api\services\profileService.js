import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const profileService = {
  // Get user profile
  async getProfile() {
    try {
      const response = await httpClient.get(ENDPOINTS.PROFILE.GET)
      const data = response.profile || response.data
      // Map backend fields to frontend fields
      const mappedData = {
        ...data,
        address: data.addressLandmark || '',
        city: data.addressCity || '',
        postalCode: data.pinCode || '',
        phone: data.phoneNumber || data.phone || '',
        dateOfBirth: data.dateOfBirth || '',
        jobTitle: data.jobTitle || '',
        experienceYears: data.experienceYears || 0,
        experienceMonths: data.experienceMonths || 0,
        industry: data.industry || '',
        desiredSalary: data.desiredSalary || 0,
        bio: data.bio || '',
        skills: data.skills || [],
        workExperience: data.workExperience || [],
        education: data.education || [],
      }
      return {
        success: true,
        data: mappedData,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch profile')
    }
  },

  // Update user profile
  async updateProfile(profileData) {
    try {
      // Map frontend fields to backend fields
      const mappedData = {
        ...profileData,
        addressLandmark: profileData.address || '',
        addressCity: profileData.city || '',
        pinCode: profileData.postalCode || '',
        phoneNumber: profileData.phone ? profileData.phone : null,
        dateOfBirth: profileData.dateOfBirth ? profileData.dateOfBirth : null,
        jobTitle: profileData.jobTitle || '',
        experienceYears: profileData.experienceYears || 0,
        experienceMonths: profileData.experienceMonths || 0,
        industry: profileData.industry || '',
        desiredSalary: profileData.desiredSalary || 0,
        bio: profileData.bio || '',
        skills: profileData.skills || [],
        workExperience: profileData.workExperience || [],
        education: profileData.education || [],
      }
      const response = await httpClient.put(ENDPOINTS.PROFILE.UPDATE, mappedData)
      return {
        success: true,
        data: response.profile || response.data,
        message: response.message || 'Profile updated successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to update profile')
    }
  },

  // Upload avatar
  async uploadAvatar(file) {
    try {
      const formData = new FormData()
      formData.append('avatar', file)
      
      const response = await httpClient.post(ENDPOINTS.PROFILE.UPLOAD_AVATAR, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return {
        success: true,
        data: response.avatar || response.data,
        message: response.message || 'Avatar uploaded successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to upload avatar')
    }
  },

  // Delete avatar
  async deleteAvatar() {
    try {
      const response = await httpClient.delete(ENDPOINTS.PROFILE.DELETE_AVATAR)
      return {
        success: true,
        message: response.message || 'Avatar deleted successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to delete avatar')
    }
  },

  // Update privacy settings
  async updatePrivacySettings(settings) {
    try {
      const response = await httpClient.put(ENDPOINTS.PROFILE.PRIVACY_SETTINGS, settings)
      return {
        success: true,
        data: response.settings || response.data,
        message: response.message || 'Privacy settings updated successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to update privacy settings')
    }
  },

  // Get public profile
  async getPublicProfile(slug) {
    try {
      const response = await httpClient.get(ENDPOINTS.PROFILE.PUBLIC + '/' + slug)
      const data = response.profile || response.data
      // Map backend fields to frontend fields
      const mappedData = {
        ...data,
        jobTitle: data.jobTitle || '',
        experienceYears: data.experienceYears || 0,
        experienceMonths: data.experienceMonths || 0,
        industry: data.industry || '',
        bio: data.bio || '',
        skills: data.skills || [],
        workExperience: data.workExperience || [],
        education: data.education || [],
      }
      return {
        success: true,
        data: mappedData,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch public profile')
    }
  }
}