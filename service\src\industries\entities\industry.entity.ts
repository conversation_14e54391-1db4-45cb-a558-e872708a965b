import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { JobEntity } from '../../jobs/entities/job.entity';
import { SubIndustryEntity } from './sub-industry.entity';

@Entity('industries')
export class IndustryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => SubIndustryEntity, (subIndustry) => subIndustry.industry)
  subIndustries: SubIndustryEntity[];

  @OneToMany(() => JobEntity, (job) => job.industry)
  jobs: JobEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
