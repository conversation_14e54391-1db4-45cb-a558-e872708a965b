import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompanyEntity } from '../entities/company.entity';
import { CreateCompanyDto } from '../dto/create-company.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { UserRole } from '../../users/enums/user.enum';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(CompanyEntity)
    private readonly companyRepository: Repository<CompanyEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  async create(createCompanyDto: CreateCompanyDto, userId: string): Promise<CompanyEntity> {
    // Verify user exists and is an employer
    const user = await this.userRepository.findOne({
      where: { id: userId, role: UserRole.EMPLOYER },
    });

    if (!user) {
      throw new NotFoundException('User not found or is not an employer');
    }

    // Check if company already exists for this user
    const existingCompany = await this.companyRepository.findOne({
      where: { userId },
    });

    if (existingCompany) {
      throw new Error('Company already exists for this user');
    }

    // Create new company
    const company = this.companyRepository.create({
      ...createCompanyDto,
      userId,
    });

    return this.companyRepository.save(company);
  }

  async findByUserId(userId: string): Promise<CompanyEntity> {
    const company = await this.companyRepository.findOne({
      where: { userId },
      relations: ['user'],
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    return company;
  }

  async update(
    userId: string,
    updateCompanyDto: Partial<CreateCompanyDto>,
  ): Promise<CompanyEntity> {
    // First check if company exists
    let company = await this.companyRepository.findOne({
      where: { userId },
    });

    if (!company) {
      // If no company exists, create one instead
      const user = await this.userRepository.findOne({
        where: { id: userId, role: UserRole.EMPLOYER },
      });

      if (!user) {
        throw new NotFoundException('User not found or is not an employer');
      }

      // Create new company with the update data
      company = this.companyRepository.create({
        ...updateCompanyDto,
        userId,
      });
    } else {
      // Update existing company
      Object.assign(company, updateCompanyDto);
    }

    return this.companyRepository.save(company);
  }
}
