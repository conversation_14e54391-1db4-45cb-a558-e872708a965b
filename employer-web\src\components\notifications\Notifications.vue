<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import VirtualScroller from 'primevue/virtualscroller';
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import Dialog from 'primevue/dialog'
import ToggleButton from 'primevue/togglebutton'
import Select from 'primevue/select'
import alertManager from '@/utils/alertManager'
import { api } from '@/api'
import { useAuthStore } from '@/stores/auth'
import DataView from 'primevue/dataview';
import Card from 'primevue/card';
import Tooltip from 'primevue/tooltip'

const router = useRouter()
const { t } = useI18n()

const isLoading = ref(false)
const authStore = useAuthStore ? useAuthStore() : null
const backendNotifications = ref([])

const markAllAsRead = async () => {
  const confirmed = await alertManager.showConfirm(
    'Mark All as Read',
    'Are you sure you want to mark all notifications as read?',
    {
      confirmText: 'Mark All',
      cancelText: 'Cancel'
    }
  )

  if (confirmed) {
    notifications.value.forEach(n => n.isRead = true)
    alertManager.showSuccess('Success', 'All notifications marked as read')
  }
}

const markAsRead = (notification) => {
  notification.isRead = true
}

const handleNotificationClick = (notification) => {
  if (!notification.isRead) {
    markAsRead(notification)
  }

  router.push(`/employer/applicant-details/${notification.data.applicantId}`)
}

const handleDeleteNotification = async (notification) => {
  const confirmed = await alertManager.showConfirm(
    'Delete Notification',
    'Are you sure you want to delete this notification?',
    {
      confirmText: 'Delete',
      cancelText: 'Cancel',
    }
  )
  if (!confirmed) return;
  try {
    const userId = authStore?.user?.id;
    await api.notifications.deleteNotification({ notificationId: notification.id, userId });
    backendNotifications.value = backendNotifications.value.filter(n => n.id !== notification.id);
    alertManager.showSuccess('Deleted', 'Notification deleted successfully.');
  } catch (err) {
    alertManager.showError('Error', 'Failed to delete notification.');
  }
}

const fetchNotifications = async () => {
  isLoading.value = true
  try {
    const userId = authStore?.user?.id
    if (!userId) return
    const response = await api.notifications.getNotifications({ userId, limit: 50 })
    const items = response?.data?.items;

    if (items) {
      items.forEach((item) => {
        item.data = JSON.parse(item.data)
      })
      backendNotifications.value = items
    }
  } catch (err) {
    // handle error
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchNotifications()
  // Load notifications data
  // Request browser notification permission if needed
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }
})
</script>

<template>
  <div class="notifications-page-wrapper flex flex-col flex-1 overflow-hidden">
    <div class="notifications-page flex-1 flex  overflow-hidden">
      <DataView :value="backendNotifications" paginator :rows="20"
        class="notifications-dataview flex-col flex flex-1 overflow-hidden">
        <template #list="slotProps">
          <div class="notifications-list-scroll overflow-x-hidden overflow-y-auto p-2">
            <div v-for="(item, index) in slotProps.items" :key="index">
              <Card class="m-1" :class="item.isRead ? 'read' : 'not-read'">
                <template #title>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center gap-2">
                      {{ item.title }}
                      <Tag v-if="!item.isRead" severity="danger">New</Tag>
                    </div>
                    <div class="flex items-center gap-1">
                      <Button
                        icon="pi pi-external-link"
                        @click="handleNotificationClick(item)"
                        variant="text"
                        rounded
                        aria-label="Open"
                        v-tooltip.top="'View Profile'"
                      />
                      <Button
                        icon="pi pi-trash"
                        @click="handleDeleteNotification(item)"
                        variant="text"
                        rounded
                        aria-label="Delete"
                        v-tooltip.top="'Delete Notification'"
                      />
                    </div>
                  </div>
                </template>
                <template #content>
                  <p class="m-0">
                    Job Title: {{ item.data?.jobTitle }}
                  </p>
                  <p class="m-0">
                    Applicant Name: {{ item.data?.applicantName }}
                  </p>
                </template>
              </Card>
            </div>
          </div>
        </template>
      </DataView>
    </div>
  </div>
</template>

<style scoped>
.notifications-page {
  padding: 1rem;
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

:deep(.p-dataview-content) {
  flex: 1;
  overflow: hidden;
}

.notifications-list-scroll {
  height: 100%;
}

:deep(.p-paginator) {
  position: sticky;
  bottom: 0;
  background: var(--surface-card);
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    gap: 0.5rem;
  }

  .p-tag {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    margin-bottom: 0.25rem !important;
  }
}
</style>