<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import Card from 'primevue/card'
import DataView from 'primevue/dataview'
import Tooltip from 'primevue/tooltip'
import NotificationsSkelton from './NotificationsSkelton.vue'
import alertManager from '@/utils/alertManager'
import { api } from '@/api'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const { t } = useI18n()

const isLoading = ref(false)
const authStore = useAuthStore ? useAuthStore() : null
const backendNotifications = ref([])

const markAllAsRead = async () => {
  const confirmed = await alertManager.showConfirm(
    'Mark All as Read',
    'Are you sure you want to mark all notifications as read?',
    {
      confirmText: 'Mark All',
      cancelText: 'Cancel'
    }
  )

  if (confirmed) {
    backendNotifications.value.forEach(n => n.isRead = true)
    alertManager.showSuccess('Success', 'All notifications marked as read')
  }
}

// mark as read - no popups/alerts
const markAsRead = async (notification) => {
  const userId = authStore?.user?.id;
  try {
    await api.notifications.markAsRead({ notificationId: notification.id, userId });
    notification.isRead = true;
  } catch (err) {
    console.error('Failed to mark notification as read:', err);
  }
}

// mark as read when clicking to view profile
const handleNotificationClick = async (notification) => {
  if (!notification.isRead) {
    await markAsRead(notification)
  }

  router.push('/employer/applications')
}

const handleDeleteNotification = async (notification) => {
  const confirmed = await alertManager.showConfirm(
    'Delete Notification',
    'Are you sure you want to delete this notification?',
    {
      confirmText: 'Delete',
      cancelText: 'Cancel',
    }
  )
  if (!confirmed) return;

  try {
    const userId = authStore?.user?.id;
    await api.notifications.deleteNotification({ notificationId: notification.id, userId });
    backendNotifications.value = backendNotifications.value.filter(n => n.id !== notification.id);
    alertManager.showSuccess('Deleted', 'Notification deleted successfully.');
  } catch (err) {
    alertManager.showError('Error', 'Failed to delete notification.');
  }
}

const fetchNotifications = async () => {
  isLoading.value = true
  try {
    const userId = authStore?.user?.id
    if (!userId) return

    const response = await api.notifications.getNotifications({ userId, limit: 50 })
    const items = response?.data?.items

    if (items) {
      items.forEach(item => {
        item.data = JSON.parse(item.data)
        if (!item.isRead) {
          setTimeout(() => {
            item.hideTag = true
          }, 5000)
        }
      })
      backendNotifications.value = items
    }
  } catch (err) {
    // handle error
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchNotifications()
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }
})
</script>

<template>
  <div class="notifications-page-wrapper flex flex-col flex-1 overflow-hidden">
    <div class="notifications-page flex-1 flex flex-col gap-2 overflow-hidden p-4">


      <NotificationsSkelton v-if="isLoading" />

      <DataView
        v-else
        :value="backendNotifications"
        paginator
        :rows="10"
        class="notifications-dataview flex-col flex flex-1 overflow-hidden"
      >
        <template #list="slotProps">
          <div class="notifications-list-scroll overflow-y-auto p-2 space-y-3">
            <Card
              v-for="(item, index) in slotProps.items"
              :key="index"
              class="rounded-xl shadow-sm border border-gray-200 transition hover:shadow-md"
              :class="item.isRead ? 'bg-white' : 'bg-red-50 border-red-200'"
            >
              <template #title>
                <div class="flex justify-between items-center">
                  <div class="flex items-center gap-2 text-lg font-semibold text-gray-800">
                    <i class="pi pi-bell text-primary-500" />
                    {{ item.title }}
                    <Tag v-if="!item.isRead && !item.hideTag" severity="danger" class="text-xs font-medium">New</Tag>
                  </div>

                  <div class="flex items-center gap-1">
                    <Button
                      icon="pi pi-external-link"
                      @click="handleNotificationClick(item)"
                      variant="text"
                      rounded
                      aria-label="Open"
                      v-tooltip.top="'View Details'"
                      class="text-green-500 hover:text-green-700 transition"
                    />
                    <Button
                      icon="pi pi-trash"
                      @click="handleDeleteNotification(item)"
                      variant="text"
                      rounded
                      aria-label="Delete"
                      v-tooltip.top="'Delete Notification'"
                      class="text-red-400 hover:text-red-600 transition"
                    />
                  </div>
                </div>
              </template>

              <template #content>
                <div class="text-sm text-gray-600 space-y-1">
                  <p>
                    <span class="font-medium text-gray-500">Job Title:</span>
                    {{ item.data?.jobTitle || '—' }}
                  </p>
                  <p>
                    <span class="font-medium text-gray-500">Applicant Name:</span>
                    {{ item.data?.applicantName || '—' }}
                  </p>
                </div>
              </template>
            </Card>
          </div>
        </template>
      </DataView>
    </div>
  </div>
</template>

<style scoped>
.notifications-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

:deep(.p-dataview-content) {
  flex: 1;
  overflow: hidden;
  background-color: transparent;
}

.notifications-list-scroll {
  height: 100%;
}

:deep(.p-paginator) {
  position: sticky;
  bottom: 0;
  background: var(--surface-card);
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
}

@media (max-width: 480px) {
  .p-tag {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    margin-bottom: 0.25rem !important;
  }
}
</style>
