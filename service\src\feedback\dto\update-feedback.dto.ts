import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsObject } from 'class-validator';
import { FeedbackStatus } from '../entities/feedback.entity';

export class UpdateFeedbackDto {
  @ApiProperty({
    description: 'New status of the feedback',
    enum: FeedbackStatus,
    example: FeedbackStatus.IN_REVIEW,
    required: false,
  })
  @IsOptional()
  @IsEnum(FeedbackStatus)
  status?: FeedbackStatus;

  @ApiProperty({
    description: 'Admin response or notes about the feedback',
    example: 'This feature is planned for the next release.',
    required: false,
  })
  @IsOptional()
  @IsString()
  adminResponse?: string;

  @ApiProperty({
    description: 'Additional metadata or attachments',
    example: { screenshots: ['url1', 'url2'] },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
