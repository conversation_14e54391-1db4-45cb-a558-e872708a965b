import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ErrorLogEntity, ErrorStatus } from '../entities/error-log.entity';
import { CreateErrorLogDto } from '../dto/create-error-log.dto';
import { UpdateErrorLogDto } from '../dto/update-error-log.dto';

@Injectable()
export class ErrorLogService {
  constructor(
    @InjectRepository(ErrorLogEntity)
    private readonly errorLogRepository: Repository<ErrorLogEntity>,
  ) {}

  async create(createErrorLogDto: CreateErrorLogDto, userId?: string): Promise<ErrorLogEntity> {
    const errorLog = this.errorLogRepository.create({
      ...createErrorLogDto,
      userId,
      status: ErrorStatus.OPEN,
    });
    return this.errorLogRepository.save(errorLog);
  }

  async findAll(
    page = 1,
    limit = 10,
    severity?: string,
    status?: ErrorStatus,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{ data: ErrorLogEntity[]; total: number }> {
    const queryBuilder = this.errorLogRepository.createQueryBuilder('errorLog');

    if (severity) {
      queryBuilder.andWhere('errorLog.severity = :severity', { severity });
    }

    if (status) {
      queryBuilder.andWhere('errorLog.status = :status', { status });
    }

    if (startDate) {
      queryBuilder.andWhere('errorLog.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('errorLog.createdAt <= :endDate', { endDate });
    }

    const [data, total] = await queryBuilder
      .orderBy('errorLog.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return { data, total };
  }

  async findOne(id: string): Promise<ErrorLogEntity> {
    const errorLog = await this.errorLogRepository.findOne({ where: { id } });
    if (!errorLog) {
      throw new NotFoundException(`Error log with ID ${id} not found`);
    }
    return errorLog;
  }

  async update(
    id: string,
    updateErrorLogDto: UpdateErrorLogDto,
    userId: string,
  ): Promise<ErrorLogEntity> {
    const errorLog = await this.findOne(id);

    Object.assign(errorLog, {
      ...updateErrorLogDto,
      updatedBy: userId,
    });

    return this.errorLogRepository.save(errorLog);
  }

  async delete(id: string): Promise<void> {
    const result = await this.errorLogRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Error log with ID ${id} not found`);
    }
  }

  async getErrorStats(): Promise<{
    total: number;
    bySeverity: Record<string, number>;
    byScreen: Record<string, number>;
    byStatus: Record<string, number>;
  }> {
    const total = await this.errorLogRepository.count();

    const severityStats = await this.errorLogRepository
      .createQueryBuilder('errorLog')
      .select('errorLog.severity', 'severity')
      .addSelect('COUNT(*)', 'count')
      .groupBy('errorLog.severity')
      .getRawMany();

    const screenStats = await this.errorLogRepository
      .createQueryBuilder('errorLog')
      .select('errorLog.screen', 'screen')
      .addSelect('COUNT(*)', 'count')
      .groupBy('errorLog.screen')
      .getRawMany();

    const statusStats = await this.errorLogRepository
      .createQueryBuilder('errorLog')
      .select('errorLog.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('errorLog.status')
      .getRawMany();

    return {
      total,
      bySeverity: severityStats.reduce((acc, curr) => {
        acc[curr.severity] = parseInt(curr.count);
        return acc;
      }, {}),
      byScreen: screenStats.reduce((acc, curr) => {
        acc[curr.screen] = parseInt(curr.count);
        return acc;
      }, {}),
      byStatus: statusStats.reduce((acc, curr) => {
        acc[curr.status] = parseInt(curr.count);
        return acc;
      }, {}),
    };
  }
}
