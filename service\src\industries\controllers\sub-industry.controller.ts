import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseUUI<PERSON>ipe,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { SubIndustryService } from '../services/sub-industry.service';
import { CreateSubIndustryDto } from '../dto/create-sub-industry.dto';
import { UpdateSubIndustryDto } from '../dto/update-sub-industry.dto';
import { SubIndustryEntity } from '../entities/sub-industry.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';

@ApiTags('Sub-Industries')
@ApiBearerAuth('JWT-auth')
@Controller('sub-industries')
export class SubIndustryController {
  constructor(private readonly subIndustryService: SubIndustryService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new sub-industry (Admin/Super Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'The sub-industry has been successfully created',
    type: () => ApiResponseDto<SubIndustryEntity>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Sub-industry name already exists in this industry',
  })
  @ApiResponse({
    status: 404,
    description: 'Parent industry not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async create(@Body() createSubIndustryDto: CreateSubIndustryDto) {
    try {
      const result = await this.subIndustryService.create(createSubIndustryDto);
      return ApiResponseDto.success(result, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error('CREATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all sub-industries' })
  @ApiResponse({
    status: 200,
    description: 'Return all active sub-industries',
    type: () => ApiResponseDto<SubIndustryEntity[]>,
  })
  async findAll() {
    try {
      const result = await this.subIndustryService.findAll();
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('GET_ALL_ERROR', error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('industry/:industryId')
  @ApiOperation({ summary: 'Get all sub-industries for a specific industry' })
  @ApiParam({
    name: 'industryId',
    description: 'ID of the parent industry',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Return all active sub-industries for the specified industry',
    type: () => ApiResponseDto<SubIndustryEntity[]>,
  })
  @ApiResponse({
    status: 404,
    description: 'Industry not found',
  })
  async findByIndustry(@Param('industryId', ParseUUIDPipe) industryId: string) {
    try {
      const result = await this.subIndustryService.findByIndustry(industryId);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('GET_BY_INDUSTRY_ERROR', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a sub-industry by ID' })
  @ApiParam({
    name: 'id',
    description: 'ID of the sub-industry',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Return the sub-industry',
    type: () => ApiResponseDto<SubIndustryEntity>,
  })
  @ApiResponse({
    status: 404,
    description: 'Sub-industry not found',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    try {
      const result = await this.subIndustryService.findOne(id);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('GET_ERROR', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update a sub-industry (Admin/Super Admin only)' })
  @ApiParam({
    name: 'id',
    description: 'ID of the sub-industry to update',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'The sub-industry has been successfully updated',
    type: () => ApiResponseDto<SubIndustryEntity>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Sub-industry name already exists in this industry',
  })
  @ApiResponse({
    status: 404,
    description: 'Sub-industry or parent industry not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateSubIndustryDto: UpdateSubIndustryDto,
  ) {
    try {
      const result = await this.subIndustryService.update(id, updateSubIndustryDto);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('UPDATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete a sub-industry (Admin/Super Admin only)' })
  @ApiParam({
    name: 'id',
    description: 'ID of the sub-industry to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'The sub-industry has been successfully deleted',
    type: () => ApiResponseDto<void>,
  })
  @ApiResponse({
    status: 404,
    description: 'Sub-industry not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    try {
      await this.subIndustryService.remove(id);
      return ApiResponseDto.success(null, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('DELETE_ERROR', error.message, HttpStatus.NOT_FOUND);
    }
  }
}
