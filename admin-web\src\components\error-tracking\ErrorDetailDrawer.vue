<script setup>
import { defineProps } from 'vue';
import Drawer from 'primevue/drawer';
import Tag from 'primevue/tag';
import Button from 'primevue/button';

const props = defineProps({
  error: {
    type: Object,
    default: null,
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close', 'toggle-resolution']);

const getSeverityColor = (severity) => {
  const colors = {
    LOW: 'info',
    MEDIUM: 'warning',
    HIGH: 'danger',
    CRITICAL: 'danger',
  };
  return colors[severity] || 'info';
};
</script>

<template>
  <Drawer
    :visible="visible"
    @update:visible="emit('close')"
    position="right"
    class="error-detail-drawer"
    style="width: 50vw"
  >
    <template #header>
      <h3 class="font-bold">Error Details</h3>
    </template>

    <div v-if="error" class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <label class="font-semibold">Severity</label>
          <div class="mt-1">
            <Tag :value="error.severity" :severity="getSeverityColor(error.severity)" />
          </div>
        </div>
        <div>
          <label class="font-semibold">Status</label>
           <div class="mt-1">
            <Tag :value="error.resolved ? 'Resolved' : 'Active'" :severity="error.resolved ? 'success' : 'danger'" />
           </div>
        </div>
        <div>
          <label class="font-semibold">Occurrences</label>
          <div class="mt-1">{{ error.occurrences }}</div>
        </div>
      </div>

      <div class="mb-4">
        <label class="font-semibold">Error Message</label>
        <p class="mt-1 bg-gray-100 p-2 rounded">{{ error.errorMessage }}</p>
      </div>

       <div class="mb-4">
        <label class="font-semibold">Screen</label>
        <p class="mt-1">{{ error.screen }}</p>
      </div>
      
       <div class="mb-4">
        <label class="font-semibold">URL</label>
        <p class="mt-1">{{ error.url }}</p>
      </div>

      <div class="mb-4">
        <label class="font-semibold">User Agent</label>
        <p class="mt-1 text-sm">{{ error.userAgent }}</p>
      </div>

      <div class="mb-4">
        <label class="font-semibold">Stack Trace</label>
        <pre class="bg-gray-800 text-white p-4 rounded mt-1 overflow-auto"><code>{{ error.metadata?.stackTrace || 'Not available' }}</code></pre>
      </div>

      <div class="mb-4">
        <label class="font-semibold">Metadata</label>
        <pre class="bg-gray-100 p-2 rounded mt-1 overflow-auto"><code>{{ JSON.stringify(error.metadata, null, 2) }}</code></pre>
      </div>

    </div>
    <div v-else class="p-4">
      <p>No error selected.</p>
    </div>
    
    <template #footer>
        <Button 
            :label="error?.resolved ? 'Mark as Unresolved' : 'Mark as Resolved'" 
            :icon="error?.resolved ? 'pi pi-undo' : 'pi pi-check'"
            @click="emit('toggle-resolution', error)"
        />
        <Button label="Close" severity="secondary" @click="emit('close')" class="ml-2" />
    </template>
  </Drawer>
</template> 