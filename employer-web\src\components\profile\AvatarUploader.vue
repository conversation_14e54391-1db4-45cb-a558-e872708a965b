<template>
  <div class="avatar-uploader">
    <Dialog 
      v-model:visible="showUploadDialog" 
      header="Change Profile Photo"
      :modal="true"
      :closable="true"
      class="avatar-dialog"
      :style="{ width: '500px' }"
    >
      <div class="upload-content">
        <div class="current-avatar">
          <h4>Current Photo</h4>
          <div class="avatar-preview">
            <Avatar 
              :label="getInitials()"
              size="xlarge"
              class="profile-avatar"
            />
          </div>
        </div>
        
        <div class="upload-section">
          <h4>Upload New Photo</h4>
          <FileUpload
            mode="basic"
            :customUpload="true"
            @uploader="handleFileUpload"
            accept="image/*"
            :maxFileSize="1000000"
            chooseLabel="Select Image"
            class="upload-button"
          />
          <small class="upload-help">Maximum file size: 1MB. Supported formats: JPG, PNG, GIF</small>
        </div>
        
        <div class="avatar-options">
          <h4>Or Choose an Avatar</h4>
          <div class="avatar-colors">
            <div 
              v-for="color in avatarColors" 
              :key="color"
              :style="{ backgroundColor: color }"
              class="color-option"
              @click="selectAvatarColor(color)"
            ></div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <Button 
            @click="showUploadDialog = false"
            label="Cancel"
            outlined
            :disabled="isUploading"
          />
          <Button 
            @click="saveAvatar"
            label="Save Changes"
            :loading="isUploading"
            class="save-btn"
          />
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Dialog from 'primevue/dialog'
import Avatar from 'primevue/avatar'
import FileUpload from 'primevue/fileupload'
import Button from 'primevue/button'
import alertManager from '@/utils/alertManager'

const props = defineProps({
  profileData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update'])

const showUploadDialog = ref(false)
const isUploading = ref(false)
const selectedFile = ref(null)
const selectedColor = ref(null)

const avatarColors = [
  '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
  '#8b5cf6', '#06b6d4', '#ec4899', '#84cc16'
]

const getInitials = () => {
  const first = props.profileData.firstName?.charAt(0) || ''
  const last = props.profileData.lastName?.charAt(0) || ''
  return (first + last).toUpperCase() || 'U'
}

const handleFileUpload = (event) => {
  selectedFile.value = event.files[0]
  selectedColor.value = null
}

const selectAvatarColor = (color) => {
  selectedColor.value = color
  selectedFile.value = null
}

const saveAvatar = async () => {
  if (!selectedFile.value && !selectedColor.value) {
    alertManager.showInfo('No Changes', 'Please select an image or avatar color')
    return
  }
  
  isUploading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Update profile with new avatar info
    const avatarUpdate = {
      avatarColor: selectedColor.value,
      // In a real app, we would upload the file and get a URL back
      avatarUrl: selectedFile.value ? URL.createObjectURL(selectedFile.value) : null
    }
    
    emit('update', avatarUpdate)
    showUploadDialog.value = false
    
    alertManager.showSuccess('Success', 'Profile photo updated successfully!')
  } catch (error) {
    alertManager.showError('Error', 'Failed to update profile photo. Please try again.')
  } finally {
    isUploading.value = false
  }
}

const openUploadDialog = () => {
  showUploadDialog.value = true
}

// Expose method to parent component
defineExpose({
  openUploadDialog
})
</script>

<style scoped>
.avatar-dialog {
  border-radius: 12px !important;
}

.upload-content {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.current-avatar,
.upload-section,
.avatar-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.current-avatar h4,
.upload-section h4,
.avatar-options h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.avatar-preview {
  display: flex;
  justify-content: center;
}

.profile-avatar {
  width: 120px !important;
  height: 120px !important;
  font-size: 3rem !important;
  background: var(--primary-color) !important;
}

.upload-button {
  align-self: center;
}

.upload-help {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  text-align: center;
}

.avatar-colors {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 2px solid var(--surface-border);
}

.color-option:hover {
  transform: scale(1.1);
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}
/* 
@media (max-width: 768px) {
  .dialog-footer {
    flex-direction: column;
  }
} */
</style>