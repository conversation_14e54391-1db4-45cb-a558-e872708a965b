import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const dashboardService = {
  // Get dashboard statistics
  async getStats() {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.STATS)
      console.log('Raw dashboard API response:', response)
      console.log('Dashboard statistics:', {
        totalActiveJobs: response.data.statistics?.totalActiveJobs || 0,
        jobsByIndustry: response.data.statistics?.jobsByIndustry || {},
        jobsByExperience: response.data.statistics?.jobsByExperience || {},
        totalApplications: response.data.statistics?.totalApplications || 0,
        profileViews: response.data.statistics?.profileViews || 0,
        savedJobs: response.data.statistics?.savedJobs || 0
      })
      return {
        success: true,
        data: {
          totalJobs: response.data.statistics?.totalActiveJobs || 0,
          activeJobs: response.data.statistics?.totalActiveJobs || 0,
          jobsByIndustry: response.data.statistics?.jobsByIndustry || {},
          jobsByExperience: response.data.statistics?.jobsByExperience || {},
          recentActivity: response.data.recentActivity && response.data.recentActivity.jobs || [],
          recommendedJobs: response.data.recommendedJobs || [],
          totalApplications: response.data.statistics?.totalApplications || 0,
          profileViews: response.data.statistics?.profileViews || 0,
          savedJobs: response.data.statistics?.savedJobs || 0
        },
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch dashboard statistics')
    }
  },

  // Get recent activity
  async getRecentActivity(limit = 10) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECENT_ACTIVITY, {
        params: { limit }
      })
      return {
        success: true,
        data: response.data.recentActivity && response.data.recentActivity.jobs || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recent activity')
    }
  },

  // Get job recommendations
  async getRecommendations(limit = 5) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECOMMENDATIONS, {
        params: { limit }
      })
      return {
        success: true,
        data: response.data.recommendedJobs || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recommendations')
    }
  }
}

// TODO: When real API is ready, replace with actual HTTP calls:
/*
import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const dashboardService = {
  async getStats() {
    try {
      const response = await httpClient.get('/jobs/jobseeker/dashboard')
      return {
        success: true,
        data: {
          totalJobs: response.data.statistics?.totalJobs || 0,
          activeJobs: response.data.statistics?.activeJobs || 0,
          jobsByIndustry: response.data.statistics?.jobsByIndustry || {},
          jobsByExperience: response.data.statistics?.jobsByExperience || {},
          recentActivity: response.data.recentActivity && response.data.recentActivity.jobs || [],
          recommendedJobs: response.data.recommendedJobs || [],
          totalApplications: response.data.statistics?.totalApplications || 0,
          profileViews: response.data.statistics?.profileViews || 0,
          savedJobs: response.data.statistics?.savedJobs || 0
        },
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch dashboard statistics')
    }
  },

  async getRecentActivity(limit = 10) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECENT_ACTIVITY, {
        params: { limit }
      })
      return {
        success: true,
        data: response.data.recentActivity && response.data.recentActivity.jobs || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recent activity')
    }
  },

  async getRecommendations(limit = 5) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECOMMENDATIONS, {
        params: { limit }
      })
      return {
        success: true,
        data: response.data.recommendedJobs || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recommendations')
    }
  }
}
*/