<template>
  <div>
    <h1>Company Profile</h1>
    <div v-if="company">
      <h2>{{ company.name }}</h2>
      <p>{{ company.description }}</p>
      <!-- Add more company details here as needed -->
    </div>
    <div v-else>
      <p>Loading company information...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { companiesService } from '@/api/services/companiesService'

const route = useRoute()
const company = ref(null)

onMounted(async () => {
  const companyId = route.params.id
  try {
    const response = await companiesService.getById(companyId)
    company.value = response.data
  } catch (e) {
    company.value = null
  }
})
</script> 