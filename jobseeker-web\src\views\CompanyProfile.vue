<template>
  <div class="company-profile">
    <div v-if="isLoading" class="loading-container">
      <ProgressSpinner />
      <p>Loading company information...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <Message severity="error" :closable="false">
        <p>{{ error }}</p>
        <Button label="Try Again" @click="loadCompany" class="mt-2" />
      </Message>
    </div>

    <div v-else-if="company" class="company-content">
      <!-- Company Header -->
      <div class="company-header">
        <div class="company-avatar">
          <Avatar
            :label="getCompanyInitial()"
            :style="{ backgroundColor: '#3b82f6' }"
            shape="circle"
            size="xlarge"
          />
        </div>
        <div class="company-info">
          <h1>{{ company.name }}</h1>
          <p v-if="company.industry" class="company-industry">{{ company.industry }}</p>
          <p v-if="company.location" class="company-location">
            <i class="pi pi-map-marker"></i>
            {{ company.location }}
          </p>
        </div>
      </div>

      <!-- Company Details -->
      <div class="company-details">
        <div class="detail-section" v-if="company.description">
          <h3>About Company</h3>
          <p>{{ company.description }}</p>
        </div>

        <div class="detail-section" v-if="company.website">
          <h3>Website</h3>
          <a :href="company.website" target="_blank" rel="noopener noreferrer">
            {{ company.website }}
          </a>
        </div>

        <div class="detail-section" v-if="company.foundedYear">
          <h3>Founded</h3>
          <p>{{ company.foundedYear }}</p>
        </div>

        <div class="detail-section" v-if="company.companySize">
          <h3>Company Size</h3>
          <p>{{ company.companySize }}</p>
        </div>
      </div>
    </div>

    <div v-else class="not-found-container">
      <Message severity="warn" :closable="false">
        <p>Company not found</p>
      </Message>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { companiesService } from '@/api/services/companiesService'
import ProgressSpinner from 'primevue/progressspinner'
import Message from 'primevue/message'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'

const route = useRoute()
const company = ref(null)
const isLoading = ref(false)
const error = ref(null)

const getCompanyInitial = () => {
  return company.value?.name ? company.value.name.charAt(0).toUpperCase() : 'C'
}

const loadCompany = async () => {
  const companyId = route.params.id
  isLoading.value = true
  error.value = null

  try {
    const response = await companiesService.getById(companyId)
    company.value = response.data
  } catch (e) {
    error.value = e.message || 'Failed to load company information'
    company.value = null
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadCompany()
})
</script>

<style scoped>
.company-profile {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.loading-container,
.error-container,
.not-found-container {
  text-align: center;
  padding: 2rem;
}

.company-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.company-avatar {
  flex-shrink: 0;
}

.company-info h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
}

.company-industry {
  color: var(--text-color-secondary);
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.company-location {
  color: var(--text-color-secondary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.company-details {
  display: grid;
  gap: 1.5rem;
}

.detail-section h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.1rem;
}

.detail-section p {
  margin: 0;
  color: var(--text-color-secondary);
  line-height: 1.6;
}

.detail-section a {
  color: var(--primary-color);
  text-decoration: none;
}

.detail-section a:hover {
  text-decoration: underline;
}
</style>