import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../entities/user.entity';
import { AdminActivityEntity } from '../entities/admin-activity.entity';
import { AdminActionType } from '../enums/admin.enum';
import { UserRole } from '../enums/user.enum';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(AdminActivityEntity)
    private readonly adminActivityRepository: Repository<AdminActivityEntity>,
  ) {}

  async logActivity(
    adminId: string,
    actionType: AdminActionType,
    targetId: string,
    details?: Record<string, any>,
  ): Promise<AdminActivityEntity> {
    const activity = this.adminActivityRepository.create({
      adminId,
      actionType,
      targetId,
      details,
    });
    return this.adminActivityRepository.save(activity);
  }

  async createAdmin(adminId: string, adminData: Partial<UserEntity>) {
    const admin = this.userRepository.create({
      ...adminData,
      role: UserRole.ADMIN,
    });

    await this.userRepository.save(admin);
    await this.logActivity(adminId, AdminActionType.USER_BLOCKED, admin.id, adminData);

    return admin;
  }

  async getUsers(
    page: number = 1,
    limit: number = 10,
    role?: UserRole,
    isBlocked?: boolean,
    searchTerm?: string,
  ) {
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (isBlocked !== undefined) {
      queryBuilder.andWhere('user.isBlocked = :isBlocked', { isBlocked });
    }

    if (searchTerm) {
      queryBuilder.andWhere(
        '(user.email ILIKE :searchTerm OR user.firstName ILIKE :searchTerm OR user.lastName ILIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` },
      );
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getUserById(userId: string) {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  async updateUser(adminId: string, userId: string, userData: Partial<UserEntity>) {
    const user = await this.getUserById(userId);

    Object.assign(user, userData);
    await this.userRepository.save(user);

    await this.logActivity(adminId, AdminActionType.PROFILE_UPDATE, userId, userData);

    return user;
  }

  async toggleUserBlock(adminId: string, userId: string, action: 'block' | 'unblock') {
    const user = await this.getUserById(userId);

    if (user.role === UserRole.SUPER_ADMIN) {
      throw new BadRequestException('Cannot block a super admin');
    }

    user.isBlocked = action === 'block';
    await this.userRepository.save(user);

    await this.logActivity(
      adminId,
      action === 'block' ? AdminActionType.USER_BLOCKED : AdminActionType.USER_UNBLOCKED,
      userId,
      { action },
    );

    return user;
  }

  async getAdminActivities(adminId: string) {
    return this.adminActivityRepository.find({
      where: { adminId },
      relations: ['user'],
      order: { createdAt: 'DESC' },
    });
  }

  async getActivityStats(startDate: Date, endDate: Date) {
    const queryBuilder = this.adminActivityRepository.createQueryBuilder('activity');

    // Get total activities
    const totalActivities = await queryBuilder
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getCount();

    // Get activities by type
    const activitiesByType = await queryBuilder
      .select('activity.actionType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('activity.actionType')
      .getRawMany();

    // Get activities by admin
    const activitiesByAdmin = await queryBuilder
      .select('activity.adminId', 'adminId')
      .addSelect('COUNT(*)', 'count')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('activity.adminId')
      .getRawMany();

    // Get activities by target user
    const activitiesByTargetUser = await queryBuilder
      .select('user.email', 'userEmail')
      .addSelect('COUNT(*)', 'count')
      .leftJoin('activity.user', 'user')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('user.email')
      .getRawMany();

    return {
      totalActivities,
      activitiesByType,
      activitiesByAdmin,
      activitiesByTargetUser,
    };
  }

  async manageUserAccess(adminId: string, userId: string, action: 'block' | 'unblock') {
    return this.toggleUserBlock(adminId, userId, action);
  }
}
