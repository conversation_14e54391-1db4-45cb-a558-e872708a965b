<script setup>
import { computed, ref, onMounted } from 'vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column'
import Tag from 'primevue/tag'
import ProgressBar from 'primevue/progressbar'
import But<PERSON> from 'primevue/button';
import { api } from '@/api'
import { useConfirm } from 'primevue/useconfirm';
import Drawer from 'primevue/drawer';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import MultiSelect from 'primevue/multiselect';
import Select from 'primevue/select';
import Checkbox from 'primevue/checkbox';
import Password from 'primevue/password';
import { defineExpose } from 'vue';
import UserCreateDrawer from './UserCreateDrawer.vue';
import UserEditDrawer from './UserEditDrawer.vue';
import UserStatusDrawer from './UserStatusDrawer.vue';
import UserProfileEditDrawer from './UserProfileEditDrawer.vue';
import EmployerOrganizationDrawer from './EmployerOrganizationDrawer.vue';


// Enums based on the User Entity
const UserRole = {
  JOB_SEEKER: 'JOB_SEEKER',
  EMPLOYER: 'EMPLOYER',
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN'
}

const SubscriptionType = {
  DEFAULT: 'DEFAULT',
  PREMIUM: 'PREMIUM',
  ENTERPRISE: 'ENTERPRISE'
}
// Search and filters
const searchTerm = ref('')
const selectedRoleFilter = ref(null)
const selectedSubscriptionFilter = ref([])
const selectedStatusFilter = ref(null)
const dateRange = ref([])
const verificationFilter = ref([])

// Sample users data based on the entity
const users = ref([])
const showUserDrawer = ref(false);
const isEditMode = ref(false);
const selectedUser = ref(null);
const userForm = ref({
  email: '',
  phone: '',
  password: '',
  firstName: '',
  middleName: '',
  lastName: '',
  isPhoneVerified: false,
  isEmailVerified: false,
  isProfileComplete: false,
  isAadharVerified: false,
  isBlocked: false,
  role: 'JOB_SEEKER',
  subscriptionType: 'DEFAULT',
  createdBy: ''
});
const confirm = useConfirm();
const showFilters = ref(false);
const showCreateDrawer = ref(false);
const showEditDrawer = ref(false);
const showStatusDrawer = ref(false);
const showProfileDrawer = ref(false);
const showOrganizationDrawer = ref(false);

// Computed properties
const filteredUsers = computed(() => {
  let filtered = users.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.fullName.toLowerCase().includes(search) ||
      user.email.toLowerCase().includes(search) ||
      user.phone?.toLowerCase().includes(search)
    )
  }

  if (selectedRoleFilter.value) {
    filtered = filtered.filter(user => 
      user.role === selectedRoleFilter.value
    )
  }

  if (selectedSubscriptionFilter.value.length > 0) {
    filtered = filtered.filter(user => 
      selectedSubscriptionFilter.value.includes(user.subscriptionType)
    )
  }

  if (selectedStatusFilter.value) {
    filtered = filtered.filter(user => {
      const isBlocked = user.isBlocked
      return selectedStatusFilter.value === (isBlocked ? 'blocked' : 'active')
    })
  }

  if (verificationFilter.value.length > 0) {
    filtered = filtered.filter(user => {
      return verificationFilter.value.every(filter => {
        switch (filter) {
          case 'email': return user.isEmailVerified
          case 'phone': return user.isPhoneVerified
          case 'aadhar': return user.isAadharVerified
          case 'profile': return user.isProfileComplete
          default: return true
        }
      })
    })
  }

  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(user => {
      const userDate = new Date(user.createdAt)
      return userDate >= startDate && userDate <= endDate
    })
  }

  return filtered
})

// Options for dropdowns
const roleOptions = Object.values(UserRole).map(role => ({
  label: role.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: role
}))

const subscriptionOptions = Object.values(SubscriptionType).map(type => ({
  label: type.toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

const statusOptions = [
  { label: 'Active', value: 'active' },
  { label: 'Blocked', value: 'blocked' }
]

const verificationOptions = [
  { label: 'Email Verified', value: 'email' },
  { label: 'Phone Verified', value: 'phone' },
  { label: 'Aadhar Verified', value: 'aadhar' },
  { label: 'Profile Complete', value: 'profile' }
]

// Methods
const getRoleColor = (role) => {
  const colors = {
    [UserRole.JOB_SEEKER]: 'info',
    [UserRole.EMPLOYER]: 'success',
    [UserRole.ADMIN]: 'warning',
    [UserRole.SUPER_ADMIN]: 'danger'
  }
  return colors[role] || 'info'
}

const getSubscriptionColor = (subscription) => {
  const colors = {
    [SubscriptionType.DEFAULT]: 'secondary',
    [SubscriptionType.PREMIUM]: 'info',
    [SubscriptionType.ENTERPRISE]: 'success'
  }
  return colors[subscription] || 'secondary'
}

const getVerificationColor = (score) => {
  if (score >= 75) return 'success'
  if (score >= 50) return 'warning'
  return 'danger'
}


const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

const openUserDrawer = (user = null) => {
  isEditMode.value = !!user;
  if (user) {
    selectedUser.value = user;
    userForm.value = { ...user, password: '' };
  } else {
    selectedUser.value = null;
    userForm.value = {
      email: '',
      phone: '',
      password: '',
      firstName: '',
      middleName: '',
      lastName: '',
      isPhoneVerified: false,
      isEmailVerified: false,
      isProfileComplete: false,
      isAadharVerified: false,
      isBlocked: false,
      role: 'JOB_SEEKER',
      subscriptionType: 'DEFAULT',
      createdBy: ''
    };
  }
  showUserDrawer.value = true;
};

const saveUser = async () => {
  if (!userForm.value.email.trim() || !userForm.value.firstName.trim() || !userForm.value.lastName.trim()) return;
  if (!isEditMode.value && !userForm.value.password.trim()) return;
  const userPayload = { ...userForm.value };
  if (isEditMode.value && !userPayload.password) delete userPayload.password;
  try {
    if (isEditMode.value && selectedUser.value) {
      await api.users.updateUser(selectedUser.value.id, userPayload);
    } else {
      await api.users.createUser(userPayload);
    }
    await loadUsers();
    showUserDrawer.value = false;
  } catch (err) {
    alert('Failed to save user: ' + (err.message || err));
  }
};

// const deleteUser = async (user) => {
//   confirm.require({
//     message: `Are you sure you want to delete user "${user.fullName}"?`,
//     header: 'Delete User',
//     icon: 'pi pi-exclamation-triangle',
//     rejectClass: 'p-button-secondary p-button-outlined',
//     rejectLabel: 'Cancel',
//     acceptLabel: 'Delete',
//     accept: async () => {
//       try {
//         await api.users.deleteUser(user.id);
//         await loadUsers();
//       } catch (err) {
//         alert('Failed to delete user: ' + (err.message || err));
//       }
//     }
//   });
// };

const loadUsers = async () => {
  const params = {
    role: selectedRoleFilter.value,
    isBlocked: selectedStatusFilter.value === 'blocked' ? true : selectedStatusFilter.value === 'active' ? false : undefined,
    searchTerm: searchTerm.value
  };
  const response = await api.users.getAllUsers(1, 200);
  if (response.success && response.data?.items) {
    users.value = response.data.items;
  }
};

function openFilterDrawer() {
  showFilters.value = true;
}

function openCreateUserDrawer() {
  selectedUser.value = null;
  showCreateDrawer.value = true;
}

function openEditUserDrawer(user) {
  selectedUser.value = user;
  showEditDrawer.value = true;
}

function openStatusDrawer(user) {
  selectedUser.value = user;
  showStatusDrawer.value = true;
}

function openProfileDrawer(user) {
  selectedUser.value = user;
  showProfileDrawer.value = true;
}

function openOrganizationDrawer(user) {
  selectedUser.value = user;
  showOrganizationDrawer.value = true;
}

function closeUserDrawer() {
  showUserDrawer.value = false;
}

function closeCreateDrawer() { showCreateDrawer.value = false; }
function closeEditDrawer() { showEditDrawer.value = false; }
function closeStatusDrawer() { showStatusDrawer.value = false; }
function closeProfileDrawer() { showProfileDrawer.value = false; }
function closeOrganizationDrawer() { showOrganizationDrawer.value = false; }

function applyFilters() {
  loadUsers(); // Reload users with current filters
  showFilters.value = false; // Close the drawer
}

function resetFilters() {
  selectedRoleFilter.value = null;
  selectedStatusFilter.value = null;
  searchTerm.value = '';
  loadUsers(); // Reload users with no filters
  showFilters.value = false; // Close the drawer
}

onMounted(loadUsers);

// Expose for parent
defineExpose({ openCreateUserDrawer, openFilterDrawer });
</script>

<template>
  <div>
    <DataTable 
      :value="users" 
      class="users-table"
      :paginator="true" 
      :rows="20"
      :rows-per-page-options="[10, 20, 50]"
      stripedRows
      :rowHover="true"
      sort-field="createdAt"
      :sort-order="-1"
      :scrollable="true"
      scroll-height="flex"
    >
      <Column field="email" header="Email" sortable />
      <Column field="firstName" header="First Name" sortable />
      <Column field="middleName" header="Middle Name" sortable />
      <Column field="lastName" header="Last Name" sortable />
      <Column field="role" header="Role" sortable>
        <template #body="slotProps">
          <Tag :value="slotProps.data.role.replace(/_/g, ' ')" :severity="getRoleColor(slotProps.data.role)" />
        </template>
      </Column>
      <Column field="subscriptionType" header="Subscription" sortable>
        <template #body="slotProps">
          <Tag :value="slotProps.data.subscriptionType" :severity="getSubscriptionColor(slotProps.data.subscriptionType)" />
        </template>
      </Column>
      <Column field="isBlocked" header="Status" sortable>
        <template #body="slotProps">
          <Tag :value="slotProps.data.isBlocked ? 'Blocked' : 'Active'" :severity="slotProps.data.isBlocked ? 'danger' : 'success'" />
        </template>
      </Column>
      <Column field="isEmailVerified" header="Email Verified" sortable>
        <template #body="slotProps">
          <i v-if="slotProps.data.isEmailVerified" class="pi pi-check-circle" style="color:green" />
          <i v-else class="pi pi-times-circle" style="color:red" />
        </template>
      </Column>
      <Column field="isPhoneVerified" header="Phone Verified" sortable>
        <template #body="slotProps">
          <i v-if="slotProps.data.isPhoneVerified" class="pi pi-check-circle" style="color:green" />
          <i v-else class="pi pi-times-circle" style="color:red" />
        </template>
      </Column>
      <Column field="isAadharVerified" header="Aadhar Verified" sortable>
        <template #body="slotProps">
          <i v-if="slotProps.data.isAadharVerified" class="pi pi-check-circle" style="color:green" />
          <i v-else class="pi pi-times-circle" style="color:red" />
        </template>
      </Column>
      <Column field="isProfileComplete" header="Profile Complete" sortable>
        <template #body="slotProps">
          <i v-if="slotProps.data.isProfileComplete" class="pi pi-check-circle" style="color:green" />
          <i v-else class="pi pi-times-circle" style="color:red" />
        </template>
      </Column>
      <Column field="createdAt" header="Created" sortable>
        <template #body="slotProps">
          <span class="created-date">{{ formatDate(slotProps.data.createdAt) }}</span>
        </template>
      </Column>
      <Column header="Actions" style="width: 260px" :class="'action-class'">
        <template #body="slotProps">
          <div class="action-buttons">
            <Button v-if="slotProps.data.role === 'employer'" icon="pi pi-building" text rounded size="small" @click="openOrganizationDrawer(slotProps.data)" v-tooltip.top="'Edit Organization'" />
            <Button icon="pi pi-id-card" text rounded size="small" @click="openProfileDrawer(slotProps.data)" v-tooltip.top="'Edit Profile'" />
            <Button icon="pi pi-pencil" text rounded size="small" @click="openEditUserDrawer(slotProps.data)" v-tooltip.top="'Edit User'" />
            <Button :icon="slotProps.data.isBlocked ? 'pi pi-unlock' : 'pi pi-lock'" text rounded size="small" :severity="slotProps.data.isBlocked ? 'success' : 'danger'" @click="openStatusDrawer(slotProps.data)" v-tooltip.top="slotProps.data.isBlocked ? 'Unblock User' : 'Block User'" />
            <!-- <Button icon="pi pi-trash" text rounded size="small" severity="danger" @click="deleteUser(slotProps.data)" v-tooltip.top="'Delete User'" /> -->
          </div>
        </template>
      </Column>
    </DataTable>

    <UserCreateDrawer :visible="showCreateDrawer" @close="closeCreateDrawer" @success="() => { closeCreateDrawer(); loadUsers(); }" />
    <UserEditDrawer :visible="showEditDrawer" :user="selectedUser" @close="closeEditDrawer" @success="() => { closeEditDrawer(); loadUsers(); }" />
    <UserStatusDrawer :visible="showStatusDrawer" :user="selectedUser" @close="closeStatusDrawer" @success="() => { closeStatusDrawer(); loadUsers(); }" />
    <UserProfileEditDrawer :visible="showProfileDrawer" :user="selectedUser" @close="closeProfileDrawer" @success="() => { closeProfileDrawer(); loadUsers(); }" />
    <EmployerOrganizationDrawer :visible="showOrganizationDrawer" :user="selectedUser" @close="closeOrganizationDrawer" @success="() => { closeOrganizationDrawer(); loadUsers(); }" />

    <!-- Filter Drawer (now from the right) -->
    <Drawer v-model:visible="showFilters" position="right" class="filter-drawer" :style="{ width: '320px' }">
      <template #header>
        <h3>Filters</h3>
      </template>
      <div class="p-4">
        <div class="form-group">
          <label for="search-term">Search Term</label>
          <InputText id="search-term" v-model="searchTerm" placeholder="Name, email, or phone" class="w-full" @keydown.enter="applyFilters" />
        </div>
        <div class="form-group">
          <label for="role-filter">Role</label>
          <Select id="role-filter" v-model="selectedRoleFilter" :options="roleOptions" option-label="label" option-value="id" placeholder="Select a role" class="w-full" :show-clear="true" />
        </div>
        <div class="form-group">
          <label for="status-filter">Status</label>
          <Select id="status-filter" v-model="selectedStatusFilter" :options="statusOptions" option-label="label" option-value="value" placeholder="Select a status" class="w-full" :show-clear="true" />
        </div>
      </div>
      <template #footer>
        <div class="drawer-footer">
          <Button label="Reset" severity="secondary" @click="resetFilters" />
          <Button label="Apply" @click="applyFilters" />
        </div>
      </template>
    </Drawer>
  </div>
</template>

<style scoped>
.user-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: var(--p-primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.user-name {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.user-email {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.user-phone {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.verification-cell {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.verification-score {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.score-bar {
  flex: 1;
  height: 6px;
}

.score-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
  min-width: 35px;
}

.verification-badges {
  display: flex;
  gap: 0.25rem;
}

.verification-badge {
  font-size: 0.75rem;
  padding: 0.125rem;
  border-radius: 3px;
}

.verification-badge.verified {
  color: var(--p-green-600);
  background: var(--p-green-100);
}

.created-date {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  gap: 1rem;
}
</style>