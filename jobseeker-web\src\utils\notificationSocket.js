import { ref, onUnmounted } from 'vue';
import { io } from 'socket.io-client';

// Auto-select backend URL based on environment
const getBackendUrl = () => {
  if (window.location.hostname === 'localhost') {
    return 'http://localhost:3000';
  }
  return 'https://jobdalal.com';
};

export function useNotificationSocket({ userId, token }) {
  const notifications = ref([]);
  
  const socket = io(getBackendUrl(), {
    auth: {
      userId,
      token,
    },
    transports: ['websocket'],
  });

  // Listen for new notifications
  socket.on('notification', (notification) => {
    notifications.value.unshift(notification);
    // Optionally, show a toast or badge here
  });

  // Listen for notification read events (optional)
  socket.on('notificationRead', (notificationId) => {
    const notif = notifications.value.find(n => n.id === notificationId);
    if (notif) notif.isRead = true;
  });

  // Clean up on component unmount
  onUnmounted(() => {
    socket.disconnect();
  });

  // Expose notifications and socket instance
  return {
    notifications,
    socket,
  };
} 