import { createApp } from 'vue'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'
import 'primeicons/primeicons.css'
import Tooltip from 'primevue/tooltip'
import ToastService from 'primevue/toastservice';
import i18n from './i18n'
import './style.css'
import App from './App.vue'
import router from './router'
import VueGtag from 'vue-gtag-next';

const app = createApp(App)
const pinia = createPinia()

app.use(VueGtag, {
  config: { id: 'G-LK50T133FR' },
  router // Pass the router instance for automatic page tracking
})
app.use(pinia)
app.use(router)
app.use(i18n)
app.use(ToastService);
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      prefix: 'p',
      darkModeSelector: '.dark-theme',
      cssLayer: false
    }
  },
  ripple: true
})

// Register global directives
app.directive('tooltip', Tooltip)

app.mount('#app')