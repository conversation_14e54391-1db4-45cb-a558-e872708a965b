@layer tailwind-base, primevue, tailwind-utilities;

@layer tailwind-base {
  @import 'tailwindcss/theme';
  @import 'tailwindcss/preflight';
}

@layer tailwind-utilities {
  @import 'tailwindcss/utilities';
}

:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  max-width: 100%;
}

body {
  margin: 0;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden; /* Prevent horizontal scroll on body */
}

#app {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden; /* Prevent horizontal scroll on app */
}

.action-class {
  position: sticky;
  right: 0;
  background: #fff;
}

/* Dark mode specific styles */
.dark {
  color-scheme: dark;
}

.dark body {
  background-color: #0f172a;
  color: #f8fafc;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}

/* Smooth transitions for all interactive elements */
button, .p-button, .p-inputtext, .p-card, .p-dropdown, .p-togglebutton {
  transition: all 0.3s ease;
}

/* Theme transition */
html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Focus styles */
.p-button:focus,
.p-inputtext:focus,
.p-dropdown:focus,
.p-togglebutton:focus {
  outline: 2px solid var(--p-primary-color);
  outline-offset: 2px;
}

/* Ensure tables don't cause horizontal scroll */
.p-datatable {
  overflow-x: auto;
}

/* Ensure content doesn't overflow */
.p-datatable-wrapper {
  overflow-x: auto;
}

/* Animation utilities */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.4s ease-out;
}

/* Dark mode component overrides */
.dark .p-card {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
}

.dark .p-inputtext {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
  color: var(--p-text-color);
}

.dark .p-inputtext:focus {
  border-color: var(--p-primary-color);
  box-shadow: 0 0 0 1px var(--p-primary-color);
}

.dark .p-dropdown {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
}

.dark .p-dropdown:focus {
  border-color: var(--p-primary-color);
  box-shadow: 0 0 0 1px var(--p-primary-color);
}

.dark .p-multiselect {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
}

.dark .p-multiselect:focus {
  border-color: var(--p-primary-color);
  box-shadow: 0 0 0 1px var(--p-primary-color);
}

.dark .p-calendar {
  background: var(--p-surface-card);
}

.dark .p-calendar .p-inputtext {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
}

.dark .p-datatable {
  background: var(--p-surface-card);
}

.dark .p-datatable .p-datatable-header {
  background: var(--p-surface-100);
  border-bottom: 1px solid var(--p-surface-border);
}

.dark .p-datatable .p-datatable-tbody > tr {
  background: var(--p-surface-card);
  border-bottom: 1px solid var(--p-surface-border);
}

.dark .p-datatable .p-datatable-tbody > tr:nth-child(even) {
  background: var(--p-surface-50);
}

.dark .p-datatable .p-datatable-tbody > tr:hover {
  background: var(--p-surface-100);
}

.dark .p-paginator {
  background: var(--p-surface-card);
  border-top: 1px solid var(--p-surface-border);
}

.dark .p-dialog {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
}

.dark .p-dialog .p-dialog-header {
  background: var(--p-surface-card);
  border-bottom: 1px solid var(--p-surface-border);
}

.dark .p-dialog .p-dialog-content {
  background: var(--p-surface-card);
}

.dark .p-dialog .p-dialog-footer {
  background: var(--p-surface-card);
  border-top: 1px solid var(--p-surface-border);
}

.dark .p-sidebar {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
}

.dark .p-sidebar .p-sidebar-header {
  background: var(--p-surface-card);
  border-bottom: 1px solid var(--p-surface-border);
}

.dark .p-menu {
  background: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
}

.dark .p-menu .p-menuitem-link {
  color: var(--p-text-color);
}

.dark .p-menu .p-menuitem-link:hover {
  background: var(--p-surface-hover);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}