import { Controller, Post, Body, Get, Put, UseGuards, Request, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CompaniesService } from '../services/companies.service';
import { CreateCompanyDto } from '../dto/create-company.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';

@ApiTags('Companies')
@Controller('companies')
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get company profile by ID (public)' })
  @ApiResponse({ status: 200, description: 'Returns company profile' })
  async getCompanyById(@Param('id') id: string) {
    return this.companiesService.findById(id);
  }

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Create company profile' })
  @ApiResponse({ status: 201, description: 'Company profile created successfully' })
  async create(@Request() req, @Body() createCompanyDto: CreateCompanyDto) {
    return this.companiesService.create(createCompanyDto, req.user.id);
  }

  @Get('my-company')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Get company profile' })
  @ApiResponse({ status: 200, description: 'Returns company profile' })
  async getMyCompany(@Request() req) {
    return this.companiesService.findByUserId(req.user.id);
  }

  @Get('by-user/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get company profile by user ID (for admins)' })
  @ApiResponse({ status: 200, description: 'Returns company profile' })
  async getCompanyByUserId(@Param('userId') userId: string) {
    return this.companiesService.findByUserId(userId);
  }

  @Put('by-user/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update company profile by user ID (for admins)' })
  @ApiResponse({ status: 200, description: 'Company profile updated successfully' })
  async updateByUserId(
    @Param('userId') userId: string,
    @Body() updateCompanyDto: Partial<CreateCompanyDto>,
  ) {
    // eslint-disable-next-line no-useless-catch
    try {
      const company = await this.companiesService.update(userId, updateCompanyDto);
      return {
        success: true,
        company,
        message: 'Company updated successfully',
      };
    } catch (error) {
      throw error;
    }
  }

  @Put()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Update company profile' })
  @ApiResponse({ status: 200, description: 'Company profile updated successfully' })
  async update(@Request() req, @Body() updateCompanyDto: Partial<CreateCompanyDto>) {
    // eslint-disable-next-line no-useless-catch
    try {
      const company = await this.companiesService.update(req.user.id, updateCompanyDto);
      return {
        success: true,
        company,
        message: 'Company updated successfully',
      };
    } catch (error) {
      throw error;
    }
  }
}
