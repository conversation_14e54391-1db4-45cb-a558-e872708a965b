<script setup>
import { ref, watch } from 'vue';
import Drawer from 'primevue/drawer';
import Button from 'primevue/button';
import Tag from 'primevue/tag';
import { usersService } from '@/api/services/usersService';

const props = defineProps({
  visible: <PERSON><PERSON><PERSON>,
  user: Object,
  onClose: Function,
  onSuccess: Function
});

const loading = ref(false);
const error = ref('');
const status = ref(false);

watch(() => props.user, (user) => {
  if (user) {
    status.value = !!user.isBlocked;
  }
}, { immediate: true });

async function handleToggleStatus() {
  error.value = '';
  loading.value = true;
  try {
    if (status.value) {
      await usersService.unblockUser(props.user.id);
    } else {
      await usersService.blockUser(props.user.id);
    }
    if (props.onSuccess) props.onSuccess();
    if (props.onClose) props.onClose();
  } catch (err) {
    error.value = err.message || 'Failed to change user status.';
  } finally {
    loading.value = false;
  }
}
</script>
<template>
  <Drawer :visible="visible" position="right" class="user-drawer" :style="{ width: '400px' }" @update:visible="onClose">
    <template #header>
      <h3>Change User Status</h3>
    </template>
    <div class="drawer-content">
      <div class="form-group">
        <label>Current Status:</label>
        <Tag :value="status ? 'Blocked' : 'Active'" :severity="status ? 'danger' : 'success'" />
      </div>
      <div class="form-group">
        <Button :label="status ? 'Unblock User' : 'Block User'" :severity="status ? 'success' : 'danger'" @click="handleToggleStatus" :loading="loading" />
      </div>
      <div v-if="error" class="error-message">{{ error }}</div>
      <div class="drawer-footer">
        <Button label="Close" severity="secondary" @click="onClose" :disabled="loading" />
      </div>
    </div>
  </Drawer>
</template>
<style scoped>
.form-group { margin-bottom: 1.5rem; }
.drawer-footer { display: flex; justify-content: flex-end; gap: 1rem; margin-top: 1.5rem; }
.error-message { color: red; margin-bottom: 1rem; }
</style> 