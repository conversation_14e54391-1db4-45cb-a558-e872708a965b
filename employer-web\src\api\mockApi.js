// Mock API implementation with realistic data and endpoints
import { ref } from 'vue'

// Simulate network delay
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// Mock data storage
const mockData = {
  users: [
    {
      id: 1,
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      avatar: null,
      createdAt: '2024-01-15T10:30:00Z',
      lastLogin: '2024-12-20T14:25:00Z'
    },
    {
      id: 2,
      email: '<EMAIL>',
      name: 'HR Manager',
      role: 'hr',
      avatar: null,
      createdAt: '2024-02-01T09:15:00Z',
      lastLogin: '2024-12-19T16:45:00Z'
    }
  ],
  
  jobs: [],
  
  applications: [],
  
  departments: [
    { id: 1, name: 'Engineering', code: 'engineering', color: '#3b82f6' },
    { id: 2, name: 'Marketing', code: 'marketing', color: '#10b981' },
    { id: 3, name: 'Sales', code: 'sales', color: '#f59e0b' },
    { id: 4, name: 'Human Resources', code: 'hr', color: '#ef4444' },
    { id: 5, name: 'Finance', code: 'finance', color: '#8b5cf6' },
    { id: 6, name: 'Operations', code: 'operations', color: '#06b6d4' }
  ],
  
  jobStatuses: [
    { id: 1, name: 'Active', code: 'active', color: '#10b981' },
    { id: 2, name: 'Draft', code: 'draft', color: '#f59e0b' },
    { id: 3, name: 'Closed', code: 'closed', color: '#ef4444' },
    { id: 4, name: 'On Hold', code: 'on-hold', color: '#6b7280' }
  ],
  
  jobTypes: [
    { id: 1, name: 'Full-time', code: 'full-time' },
    { id: 2, name: 'Part-time', code: 'part-time' },
    { id: 3, name: 'Contract', code: 'contract' },
    { id: 4, name: 'Freelance', code: 'freelance' },
    { id: 5, name: 'Internship', code: 'internship' }
  ],
  
  experienceLevels: [
    { id: 1, name: 'Entry Level', code: 'entry', years: '0-2' },
    { id: 2, name: 'Mid Level', code: 'mid', years: '3-5' },
    { id: 3, name: 'Senior Level', code: 'senior', years: '6-10' },
    { id: 4, name: 'Executive', code: 'executive', years: '10+' }
  ]
}

// Generate realistic job data
const generateJobData = (count = 1000) => {
  const companies = [
    'TechCorp Inc.', 'InnovateLabs', 'DataSystems Ltd.', 'CloudWorks', 'DevStudio Pro',
    'DigitalFlow Solutions', 'CodeCraft Technologies', 'TechVision Group', 'SmartSolutions Inc.', 'NextGen Systems',
    'ByteForge', 'PixelPerfect', 'CodeStream', 'TechNova', 'DataDriven Co.',
    'CloudFirst Technologies', 'AgileWorks', 'ScaleUp Solutions', 'TechHub', 'InnovateNow'
  ]
  
  const locations = [
    'New York, NY', 'San Francisco, CA', 'Austin, TX', 'Seattle, WA', 'Boston, MA',
    'Chicago, IL', 'Los Angeles, CA', 'Denver, CO', 'Atlanta, GA', 'Portland, OR',
    'Remote (US)', 'Remote (Global)', 'Miami, FL', 'Phoenix, AZ', 'Dallas, TX'
  ]
  
  const jobTitles = {
    engineering: [
      'Software Engineer', 'Frontend Developer', 'Backend Developer', 'Full Stack Developer',
      'DevOps Engineer', 'Data Engineer', 'Mobile Developer', 'QA Engineer',
      'Site Reliability Engineer', 'Security Engineer', 'Machine Learning Engineer',
      'Cloud Architect', 'Technical Lead', 'Principal Engineer'
    ],
    marketing: [
      'Marketing Manager', 'Content Strategist', 'Digital Marketing Specialist', 'Brand Manager',
      'SEO Specialist', 'Social Media Manager', 'Product Marketing Manager', 'Growth Hacker',
      'Marketing Analyst', 'Campaign Manager', 'Content Creator', 'Marketing Coordinator'
    ],
    sales: [
      'Sales Representative', 'Account Manager', 'Business Development Manager', 'Sales Manager',
      'Customer Success Manager', 'Inside Sales Representative', 'Enterprise Sales',
      'Sales Development Representative', 'Regional Sales Manager', 'Sales Engineer'
    ],
    hr: [
      'HR Manager', 'Recruiter', 'HR Business Partner', 'Talent Acquisition Specialist',
      'HR Coordinator', 'People Operations Manager', 'Compensation Analyst',
      'Learning & Development Specialist', 'HR Generalist', 'Employee Relations Specialist'
    ],
    finance: [
      'Financial Analyst', 'Accountant', 'Finance Manager', 'Controller', 'Investment Analyst',
      'Budget Analyst', 'Tax Specialist', 'Audit Manager', 'Treasury Analyst', 'CFO'
    ],
    operations: [
      'Operations Manager', 'Project Manager', 'Operations Analyst', 'Supply Chain Manager',
      'Process Improvement Specialist', 'Business Analyst', 'Program Manager',
      'Operations Coordinator', 'Logistics Manager', 'Quality Assurance Manager'
    ]
  }
  
  const benefits = [
    'Health Insurance', 'Dental Insurance', 'Vision Insurance', '401(k) Matching',
    'Flexible PTO', 'Remote Work Options', 'Professional Development Budget',
    'Stock Options', 'Gym Membership', 'Commuter Benefits', 'Life Insurance',
    'Parental Leave', 'Mental Health Support', 'Free Lunch', 'Learning Stipend'
  ]
  
  const skills = {
    engineering: [
      'JavaScript', 'Python', 'React', 'Node.js', 'AWS', 'Docker', 'Kubernetes',
      'TypeScript', 'Vue.js', 'Angular', 'Java', 'C++', 'Go', 'Rust', 'PostgreSQL',
      'MongoDB', 'Redis', 'GraphQL', 'REST APIs', 'Git', 'CI/CD', 'Microservices'
    ],
    marketing: [
      'Google Analytics', 'SEO', 'SEM', 'Content Marketing', 'Social Media Marketing',
      'Email Marketing', 'Marketing Automation', 'A/B Testing', 'CRM', 'Adobe Creative Suite',
      'Copywriting', 'Brand Strategy', 'Market Research', 'Lead Generation'
    ],
    sales: [
      'Salesforce', 'CRM Management', 'Lead Generation', 'Cold Calling', 'Negotiation',
      'Account Management', 'Pipeline Management', 'Sales Forecasting', 'Customer Relationship Management',
      'Presentation Skills', 'Business Development', 'Territory Management'
    ],
    hr: [
      'HRIS', 'Workday', 'BambooHR', 'Recruiting', 'Performance Management',
      'Employee Relations', 'Compensation Analysis', 'Benefits Administration',
      'Training & Development', 'Employment Law', 'Diversity & Inclusion'
    ],
    finance: [
      'Excel', 'Financial Modeling', 'QuickBooks', 'SAP', 'Financial Analysis',
      'Budgeting', 'Forecasting', 'Accounting', 'Tax Preparation', 'Audit',
      'Risk Management', 'Investment Analysis', 'Financial Reporting'
    ],
    operations: [
      'Project Management', 'Lean Six Sigma', 'Process Improvement', 'Supply Chain Management',
      'Data Analysis', 'Quality Management', 'Vendor Management', 'Risk Assessment',
      'Business Process Optimization', 'Logistics', 'Inventory Management'
    ]
  }
  
  const jobs = []
  
  for (let i = 1; i <= count; i++) {
    const department = mockData.departments[Math.floor(Math.random() * mockData.departments.length)]
    const departmentCode = department.code
    const titles = jobTitles[departmentCode] || jobTitles.engineering
    const title = titles[Math.floor(Math.random() * titles.length)]
    const company = companies[Math.floor(Math.random() * companies.length)]
    const location = locations[Math.floor(Math.random() * locations.length)]
    const status = mockData.jobStatuses[Math.floor(Math.random() * mockData.jobStatuses.length)]
    const jobType = mockData.jobTypes[Math.floor(Math.random() * mockData.jobTypes.length)]
    const experienceLevel = mockData.experienceLevels[Math.floor(Math.random() * mockData.experienceLevels.length)]
    
    const salaryMin = Math.floor(Math.random() * 80000) + 40000
    const salaryMax = salaryMin + Math.floor(Math.random() * 100000) + 20000
    
    const postedDate = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000)
    const applicationsCount = Math.floor(Math.random() * 200)
    
    const jobSkills = skills[departmentCode] || skills.engineering
    const requiredSkills = jobSkills
      .sort(() => 0.5 - Math.random())
      .slice(0, Math.floor(Math.random() * 8) + 3)
    
    const jobBenefits = benefits
      .sort(() => 0.5 - Math.random())
      .slice(0, Math.floor(Math.random() * 8) + 5)
    
    jobs.push({
      id: i,
      title,
      company,
      department: departmentCode,
      departmentName: department.name,
      description: `We are seeking a talented ${title} to join our dynamic ${department.name} team. This role offers excellent growth opportunities and the chance to work on cutting-edge projects that make a real impact. You'll collaborate with cross-functional teams and contribute to innovative solutions that drive our business forward.`,
      requirements: [
        `${experienceLevel.years} years of experience in ${departmentCode}`,
        `Strong proficiency in ${requiredSkills.slice(0, 3).join(', ')}`,
        `Experience with ${requiredSkills.slice(3, 6).join(', ')}`,
        'Excellent communication and teamwork skills',
        'Bachelor\'s degree in relevant field or equivalent experience'
      ],
      responsibilities: [
        `Develop and maintain ${departmentCode} solutions`,
        'Collaborate with cross-functional teams',
        'Participate in code reviews and technical discussions',
        'Contribute to architectural decisions',
        'Mentor junior team members'
      ],
      skills: requiredSkills,
      benefits: jobBenefits,
      status: status.code,
      statusName: status.name,
      jobType: jobType.code,
      jobTypeName: jobType.name,
      experienceLevel: experienceLevel.code,
      experienceLevelName: experienceLevel.name,
      location,
      salaryMin,
      salaryMax,
      currency: 'USD',
      applicationsCount,
      viewsCount: Math.floor(Math.random() * 1000) + applicationsCount,
      isRemote: location.includes('Remote'),
      postedDate: postedDate.toISOString(),
      updatedDate: new Date(postedDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      closingDate: new Date(postedDate.getTime() + (Math.random() * 60 + 30) * 24 * 60 * 60 * 1000).toISOString(),
      createdBy: mockData.users[Math.floor(Math.random() * mockData.users.length)].id,
      isUrgent: Math.random() < 0.1,
      isFeatured: Math.random() < 0.05,
      color: department.color
    })
  }
  
  return jobs
}

// Initialize mock data
mockData.jobs = generateJobData(1500)

// Mock API endpoints
export const mockApi = {
  // Authentication endpoints
  auth: {
    async login(credentials) {
      await delay(1000)
      
      // Simple email validation for demo
      if (!credentials.email || !credentials.email.includes('@')) {
        throw new Error('Invalid email address')
      }
      
      // Find or create user
      let user = mockData.users.find(u => u.email === credentials.email)
      if (!user) {
        user = {
          id: mockData.users.length + 1,
          email: credentials.email,
          name: credentials.email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          role: 'user',
          avatar: null,
          createdAt: new Date().toISOString(),
          lastLogin: new Date().toISOString()
        }
        mockData.users.push(user)
      } else {
        user.lastLogin = new Date().toISOString()
      }
      
      return {
        success: true,
        user,
        token: `mock_token_${user.id}_${Date.now()}`
      }
    },
    
    async sendOtp(email) {
      await delay(800)
      
      if (!email || !email.includes('@')) {
        throw new Error('Invalid email address')
      }
      
      // Simulate OTP sending with your exact response format
      console.log(`Mock OTP sent to ${email}: 123456`)
      
      return {
        data: {
          message: "OTP sent successfully"
        },
        success: true,
        status: 200
      }
    },
    
    async verifyOtp(email, otp) {
      await delay(600)
      
      if (!email || !email.includes('@')) {
        return {
          data: null,
          success: false,
          status: 401,
          errorCode: "OTP_VERIFY_ERROR",
          errorMessage: "Invalid email address"
        }
      }
      
      if (!otp || otp.length !== 6) {
        return {
          data: null,
          success: false,
          status: 401,
          errorCode: "OTP_VERIFY_ERROR",
          errorMessage: "Invalid OTP"
        }
      }
      
      // For demo purposes, simulate different scenarios based on OTP
      if (otp === '000000') {
        // Simulate expired OTP
        return {
          data: null,
          success: false,
          status: 401,
          errorCode: "OTP_VERIFY_ERROR",
          errorMessage: "OTP expired"
        }
      }
      
      if (otp === '111111') {
        // Simulate invalid OTP
        return {
          data: null,
          success: false,
          status: 401,
          errorCode: "OTP_VERIFY_ERROR",
          errorMessage: "Invalid OTP"
        }
      }
      
      // Success response with your exact format
      return {
        data: {
          access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.QBIyXEjtUq0xnGYy26znPzYM6y44LEy1G1zjQBkqWRQ",
          refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.tqFQSKpSc-ElgWb9Z7npD_G_U4lbIFCRTWLjpj_KJKU",
          user: {
            id: "fc6f26e6-f1a9-4b6b-8529-cf5552b54274",
            email: email,
            phone: null,
            role: "employer",
            profile: {
              id: "d2bb5119-2cc8-4fb7-9575-c75ef6606da0",
              userId: "fc6f26e6-f1a9-4b6b-8529-cf5552b54274",
              firstName: null,
              lastName: null,
              phoneNumber: null,
              address: null,
              city: null,
              state: null,
              country: null,
              postalCode: null,
              profileImage: null,
              profileImageThumbnail: null,
              imageMetadata: null,
              isComplete: false,
              updatedBy: null,
              createdAt: "2025-06-09T18:44:54.785Z",
              updatedAt: "2025-06-09T18:44:54.785Z"
            }
          }
        },
        success: true,
        status: 200
      }
    },
    
    async logout() {
      await delay(200)
      return { success: true }
    },
    
    async refreshToken(token) {
      await delay(300)
      return {
        success: true,
        token: `refreshed_${token}_${Date.now()}`
      }
    }
  },
  
  // Jobs endpoints
  jobs: {
    async getAll(params = {}) {
      await delay(400)
      
      let jobs = [...mockData.jobs]
      
      // Apply filters
      if (params.search) {
        const searchTerm = params.search.toLowerCase()
        jobs = jobs.filter(job => 
          job.title.toLowerCase().includes(searchTerm) ||
          job.company.toLowerCase().includes(searchTerm) ||
          job.description.toLowerCase().includes(searchTerm) ||
          job.location.toLowerCase().includes(searchTerm)
        )
      }
      
      if (params.department) {
        jobs = jobs.filter(job => job.department === params.department)
      }
      
      if (params.status && params.status.length > 0) {
        jobs = jobs.filter(job => params.status.includes(job.status))
      }
      
      if (params.jobType && params.jobType.length > 0) {
        jobs = jobs.filter(job => params.jobType.includes(job.jobType))
      }
      
      if (params.experienceLevel) {
        jobs = jobs.filter(job => job.experienceLevel === params.experienceLevel)
      }
      
      if (params.salaryMin !== null && params.salaryMin !== undefined) {
        jobs = jobs.filter(job => job.salaryMax >= params.salaryMin)
      }
      
      if (params.salaryMax !== null && params.salaryMax !== undefined) {
        jobs = jobs.filter(job => job.salaryMin <= params.salaryMax)
      }
      
      if (params.dateRange && params.dateRange.length === 2) {
        const [startDate, endDate] = params.dateRange
        jobs = jobs.filter(job => {
          const jobDate = new Date(job.postedDate)
          return jobDate >= startDate && jobDate <= endDate
        })
      }
      
      if (params.isRemote !== undefined) {
        jobs = jobs.filter(job => job.isRemote === params.isRemote)
      }
      
      // Apply sorting
      if (params.sortBy) {
        jobs.sort((a, b) => {
          const aVal = a[params.sortBy]
          const bVal = b[params.sortBy]
          
          if (params.sortOrder === 'desc') {
            return bVal > aVal ? 1 : -1
          }
          return aVal > bVal ? 1 : -1
        })
      } else {
        // Default sort by posted date (newest first)
        jobs.sort((a, b) => new Date(b.postedDate) - new Date(a.postedDate))
      }
      
      // Apply pagination
      const page = params.page || 1
      const limit = params.limit || 50
      const offset = (page - 1) * limit
      const paginatedJobs = jobs.slice(offset, offset + limit)
      
      return {
        success: true,
        data: paginatedJobs,
        pagination: {
          page,
          limit,
          total: jobs.length,
          totalPages: Math.ceil(jobs.length / limit),
          hasNext: offset + limit < jobs.length,
          hasPrev: page > 1
        }
      }
    },
    
    async getById(id) {
      await delay(300)
      
      const job = mockData.jobs.find(j => j.id === parseInt(id))
      if (!job) {
        throw new Error('Job not found')
      }
      
      return {
        success: true,
        data: job
      }
    },
    
    async create(jobData) {
      await delay(800)
      
      const newJob = {
        id: Math.max(...mockData.jobs.map(j => j.id)) + 1,
        ...jobData,
        applicationsCount: 0,
        viewsCount: 0,
        postedDate: new Date().toISOString(),
        updatedDate: new Date().toISOString(),
        createdBy: 1 // Mock current user ID
      }
      
      mockData.jobs.unshift(newJob)
      
      return {
        success: true,
        data: newJob,
        message: 'Job created successfully'
      }
    },
    
    async update(id, jobData) {
      await delay(600)
      
      const jobIndex = mockData.jobs.findIndex(j => j.id === parseInt(id))
      if (jobIndex === -1) {
        throw new Error('Job not found')
      }
      
      mockData.jobs[jobIndex] = {
        ...mockData.jobs[jobIndex],
        ...jobData,
        updatedDate: new Date().toISOString()
      }
      
      return {
        success: true,
        data: mockData.jobs[jobIndex],
        message: 'Job updated successfully'
      }
    },
    
    async delete(id) {
      await delay(400)
      
      const jobIndex = mockData.jobs.findIndex(j => j.id === parseInt(id))
      if (jobIndex === -1) {
        throw new Error('Job not found')
      }
      
      mockData.jobs.splice(jobIndex, 1)
      
      return {
        success: true,
        message: 'Job deleted successfully'
      }
    },
    
    async getStats() {
      await delay(300)
      
      const stats = {
        total: mockData.jobs.length,
        active: mockData.jobs.filter(j => j.status === 'active').length,
        draft: mockData.jobs.filter(j => j.status === 'draft').length,
        closed: mockData.jobs.filter(j => j.status === 'closed').length,
        totalApplications: mockData.jobs.reduce((sum, job) => sum + job.applicationsCount, 0),
        avgApplicationsPerJob: Math.round(mockData.jobs.reduce((sum, job) => sum + job.applicationsCount, 0) / mockData.jobs.length),
        departmentBreakdown: mockData.departments.map(dept => ({
          department: dept.name,
          count: mockData.jobs.filter(j => j.department === dept.code).length
        }))
      }
      
      return {
        success: true,
        data: stats
      }
    }
  },
  
  // Reference data endpoints
  reference: {
    async getDepartments() {
      await delay(200)
      return {
        success: true,
        data: mockData.departments
      }
    },
    
    async getJobStatuses() {
      await delay(200)
      return {
        success: true,
        data: mockData.jobStatuses
      }
    },
    
    async getJobTypes() {
      await delay(200)
      return {
        success: true,
        data: mockData.jobTypes
      }
    },
    
    async getExperienceLevels() {
      await delay(200)
      return {
        success: true,
        data: mockData.experienceLevels
      }
    }
  },
  
  // Dashboard endpoints
  dashboard: {
    async getStats() {
      await delay(400)
      
      const now = new Date()
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
      
      const recentJobs = mockData.jobs.filter(job => new Date(job.postedDate) >= lastMonth)
      const activeJobs = mockData.jobs.filter(job => job.status === 'active')
      
      return {
        success: true,
        data: {
          totalJobs: mockData.jobs.length,
          activeJobs: activeJobs.length,
          recentJobs: recentJobs.length,
          totalApplications: mockData.jobs.reduce((sum, job) => sum + job.applicationsCount, 0),
          avgSalary: Math.round(mockData.jobs.reduce((sum, job) => sum + (job.salaryMin + job.salaryMax) / 2, 0) / mockData.jobs.length),
          topDepartments: mockData.departments
            .map(dept => ({
              name: dept.name,
              count: mockData.jobs.filter(j => j.department === dept.code).length
            }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5),
          recentActivity: mockData.jobs
            .sort((a, b) => new Date(b.updatedDate) - new Date(a.updatedDate))
            .slice(0, 10)
            .map(job => ({
              id: job.id,
              title: job.title,
              company: job.company,
              action: 'updated',
              timestamp: job.updatedDate
            }))
        }
      }
    }
  }
}

// Export individual API modules for easier imports
export const authApi = mockApi.auth
export const jobsApi = mockApi.jobs
export const referenceApi = mockApi.reference
export const dashboardApi = mockApi.dashboard

// Export mock data for direct access if needed
export { mockData }