import httpClient from '../httpClient'

export const industriesService = {
  // Get all industries from backend
  async getIndustries() {
    try {
      const response = await httpClient.get('/industries')
      return {
        success: true,
        data: response.data,
        status: response.status || 200,
        message: response.message || 'Industries fetched successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch industries')
    }
  }
}