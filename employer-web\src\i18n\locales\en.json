{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "clear": "Clear", "apply": "Apply", "close": "Close", "view": "View", "download": "Download", "share": "Share", "copy": "Copy", "email": "Email", "phone": "Phone", "location": "Location", "date": "Date", "time": "Time", "name": "Name", "description": "Description", "status": "Status", "type": "Type", "category": "Category", "refresh": "Refresh", "logout": "Logout", "login": "<PERSON><PERSON>", "signup": "Sign Up", "profile": "Profile", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "privacy": "Privacy", "terms": "Terms", "language": "Language", "theme": "Theme", "switchToLight": "Switch to light theme", "switchToDark": "Switch to dark theme", "notifications": "Notifications", "unAuthError": "Unauthorized Error"}, "navigation": {"main": "Main", "tools": "Tools", "preferences": "Preferences", "dashboard": "Dashboard", "postedJobs": "Posted Jobs", "jobs": "Jobs", "savedJobs": "Saved Jobs", "applicants": "Applicants", "applications": "Applications", "profile": "Profile", "settings": "Settings", "home": "Home", "findJobs": "Look for Work", "postJobs": "Post a Job Opening", "notifications": "Notifications"}, "auth": {"welcomeBack": "Welcome Back", "signInToAccount": "Please sign in to your account", "continueWithGoogle": "Continue with Google", "orLoginWithEmail": "or login with your email", "email": "Email", "enterEmail": "Enter your email", "sendOTP": "Send OTP", "sendingOTP": "Sending OTP...", "enterOTP": "Enter 6-digit OTP", "otpSentTo": "We've sent a verification code to {email}", "resendOTP": "Resend OTP", "resendIn": "Resend in {seconds}s", "verifying": "Verifying...", "backToEmail": "Back to email", "forgotPassword": "Forgot your password?", "noAccount": "Don't have an account?", "invalidEmail": "Invalid email address", "invalidOTP": "Invalid OTP format", "otpVerified": "OTP verified successfully", "loginFailed": "<PERSON><PERSON> failed", "otpSent": "OTP sent successfully", "tryAgain": "Try Again"}, "dashboard": {"welcomeBack": "Welcome back, {name}!", "readyToExplore": "Ready to explore new opportunities", "availableJobs": "Available Jobs", "applicationsSent": "Applications Sent", "profileViews": "Profile Views", "savedJobs": "Saved Jobs", "newThisMonth": "+{percent}% new this month", "pendingReview": "{count} pending review", "thisWeek": "This week", "readyToApply": "Ready to apply", "quickActions": "Quick Actions", "searchJobs": "Search Jobs", "myApplications": "My Applications", "updateProfile": "Update Profile", "recommendedForYou": "Recommended for You", "viewAll": "View All", "recentActivity": "Recent Activity", "updated": "Updated {time}", "applied": "Applied to {job}", "saved": "Saved {job}", "viewed": "Profile viewed by {company}", "saveJob": "Save Job", "applyNow": "Apply Now", "apply": "Apply"}, "jobs": {"yourPostedJobs": "Find Your Posted Jobs", "discoverOpportunities": "Discover thousands of blue collar employee", "filters": "Filters", "clearAll": "Clear All", "industry": "Industry", "selectIndustry": "Select Industry", "jobType": "Job Type", "selectJobType": "Select Job Type", "experienceLevel": "Experience Level", "selectExperience": "Select Experience", "salaryRange": "Salary Range", "workType": "Work Type", "remoteWork": "Remote Work", "jobsFound": "{count} jobs found", "sortBy": "Sort by", "mostRecent": "Most Recent", "salaryHighToLow": "Salary: High to Low", "salaryLowToHigh": "Salary: Low to High", "mostApplications": "Most Applications", "companyName": "Company Name", "featured": "Featured", "urgent": "<PERSON><PERSON>", "postedAgo": "Posted {time}", "applicants": "{count} applicants", "views": "{count} views", "applied": "Applied", "loadMore": "Load More Jobs", "noJobsFound": "No more jobs found", "adjustFilters": "Try adjusting your filters or search terms to find more opportunities.", "clearFilters": "Clear Filters", "findingOpportunities": "Finding great opportunities for you...", "backToJobs": "Back to Jobs", "jobDetails": "Job Details", "keyResponsibilities": "Key Responsibilities", "requirements": "Requirements", "requiredSkills": "Required Skills", "benefitsPerks": "Benefits & Perks", "quickApply": "Quick Apply", "readyForNextStep": "Ready to take the next step in your career?", "aboutCompany": "About {company}", "viewCompanyProfile": "View Company Profile", "department": "Department", "postedDate": "Posted Date", "closingDate": "Closing Date", "shareJob": "Share this Job", "copyLink": "Copy Link", "jobNotFound": "Job Not Found", "jobNotFoundDesc": "The job you're looking for doesn't exist or has been removed.", "min": "Min", "max": "Max", "anyTime": "Any time", "pastMonth": "Past month", "pastWeek": "Past week", "past24Hours": "Past 24 hours", "filterByTime": "", "filter": "Filter <PERSON>"}, "applications": {"myApplications": "My Applications", "trackApplications": "Track your job applications and their status", "totalApplications": "Total Applications", "pendingReview": "Pending Review", "interviews": "Interviews", "jobOffers": "Job Offers", "allApplications": "All Applications ({count})", "pending": "Pending ({count})", "interview": "Interviews ({count})", "offers": "Offers ({count})", "rejected": "Rejected ({count})", "appliedAgo": "Applied {time}", "applicationSubmitted": "Application Submitted", "applicationSubmittedDesc": "Your application has been successfully submitted", "underReview": "Under Review", "underReviewDesc": "Your application is being reviewed by the hiring team", "interviewScheduled": "Interview Scheduled", "interviewScheduledDesc": "Phone interview scheduled for tomorrow at 2:00 PM", "interviewCompleted": "Interview Completed", "interviewCompletedDesc": "Successfully completed phone and in-person interviews", "jobOfferReceived": "Job Offer Received", "jobOfferReceivedDesc": "Congratulations! You have received a job offer", "applicationNotSelected": "Application Not Selected", "applicationNotSelectedDesc": "Thank you for your interest. We have decided to move forward with other candidates.", "viewJob": "View Job", "withdraw": "Withdraw", "details": "Details", "noPendingApplications": "No Pending Applications", "noInterviewInvitations": "No Interview Invitations", "noJobOffersYet": "No Job Offers Yet", "noRejectedApplications": "No Rejected Applications", "noApplicationsYet": "No Applications Yet", "allReviewed": "All your applications have been reviewed.", "keepApplyingInterviews": "Keep applying to increase your chances of getting interviews.", "keepApplyingOffers": "Keep applying and interviewing to receive job offers.", "noRejections": "No applications have been rejected.", "startApplying": "Start applying to jobs to track your applications here.", "findJobsToApply": "Find Jobs to Apply", "loadingApplications": "Loading your applications...", "withdrawConfirm": "Are you sure you want to withdraw your application for {job}?"}, "applicants": {"selectJob": "Select Job", "selectJobDesc": "Choose a job to view and manage its applicants", "jobLabel": "Job", "filterLabel": "Filter", "totalApplicants": "Total Applicants", "pendingReview": "Pending Review", "interviews": "Interviews", "jobOffers": "Job Offers", "allApplicants": "All Applicants ({count})", "pending": "Pending ({count})", "interview": "Interviews ({count})", "offers": "Offers ({count})", "rejected": "Rejected ({count})", "appliedAgo": "Applied {time}", "applicationSubmitted": "Application Submitted", "applicationSubmittedDesc": "Application has been successfully submitted", "underReview": "Under Review", "underReviewDesc": "Application is being reviewed by the hiring team", "interviewScheduled": "Interview Scheduled", "interviewScheduledDesc": "Interview has been scheduled", "interviewCompleted": "Interview Completed", "interviewCompletedDesc": "Interview has been completed", "jobOfferReceived": "Job Offer Received", "jobOfferReceivedDesc": "Job offer has been sent to the applicant", "applicationNotSelected": "Application Not Selected", "applicationNotSelectedDesc": "Application has been rejected", "applicationWithdrawn": "Application Withdrawn", "applicationWithdrawnDesc": "Application has been withdrawn by the applicant", "viewProfile": "View Profile", "scheduleInterview": "Schedule Interview", "makeOffer": "Make Offer", "reject": "Reject", "details": "Details", "downloadResume": "Download Resume", "noResume": "No Resume", "noPendingApplicants": "No Pending Applicants", "noInterviewApplicants": "No Interview Applicants", "noOfferApplicants": "No Offer Applicants", "noRejectedApplicants": "No Rejected Applicants", "noWithdrawnApplicants": "No Withdrawn Applicants", "noApplicantsYet": "No Applicants Yet", "allApplicantsReviewed": "All applicants have been reviewed.", "noInterviewsScheduled": "No interviews have been scheduled.", "noOffersMade": "No offers have been made.", "noRejections": "No applications have been rejected.", "noWithdrawals": "No applications have been withdrawn.", "noApplicantsForJob": "No applicants for this job yet.", "selectJobFirst": "Select a Job First", "selectJobFirstDesc": "Please select a job to view its applicants", "noJobsAvailable": "No Jobs Available", "noJobsAvailableDesc": "You haven't posted any jobs yet. Create your first job posting to start receiving applications.", "createJob": "Create Job", "loadingApplicants": "Loading applicants..."}, "profile": {"myProfile": "My Profile", "manageProfile": "Manage your profile and preferences", "changePhoto": "Change Photo", "employer": "Employer", "locationNotSpecified": "Location not specified", "yearsExperience": "Years Experience", "profileViews": "Profile Views", "personalInformation": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "dateOfBirth": "Date of Birth", "notSpecified": "Not specified", "professionalInformation": "Professional Information", "currentJobTitle": "Current Job Title", "yearsOfExperience": "Years of Experience", "years": "years", "desiredSalary": "<PERSON>d <PERSON>", "bio": "Bio", "noBioAdded": "No bio added yet", "skills": "Skills", "addSkills": "Add Skills", "workExperience": "Work Experience", "addWorkExperience": "Add Work Experience", "present": "Present", "education": "Education", "addEducation": "Add Education", "profileCompletion": "Profile Completion", "quickActions": "Quick Actions", "downloadResume": "Download Resume", "viewPublicProfile": "View Public Profile", "shareProfile": "Share Profile", "privacySettings": "Privacy Settings", "profileVisibility": "Profile Visibility", "profileVisibilityDesc": "Make your profile visible to employers", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive job alerts and updates", "addPhoneNumber": "Add your phone number", "writeProfessionalBio": "Write a professional bio", "addYourSkills": "Add your skills"}, "notifications": {"title": "Notifications", "subtitle": "Stay updated with your job search activities", "markAllAsRead": "<PERSON> as <PERSON>", "settings": "Notification Settings", "total": "Total Notifications", "unread": "Unread", "today": "Today", "important": "Important", "all": "All", "jobAlerts": "<PERSON>", "applications": "Applications", "system": "System", "noNotifications": "No notifications yet", "noUnread": "No unread notifications", "noJobAlerts": "No job alerts", "noApplications": "No application notifications", "noSystem": "No system notifications", "enableNotifications": "Enable Notifications", "emailSettings": "Email Notifications", "pushSettings": "Push Notifications", "frequency": "Frequency", "jobMatch": "New Job Match", "applicationUpdate": "Application Update", "interviewScheduled": "Interview Scheduled", "profileViewed": "Profile Viewed", "systemUpdate": "System Update"}, "landing": {"title": "<PERSON>", "heroTitle": "Bridge the Gap.", "heroHighlight": "JOB DALAL", "heroTitleEnd": "helps you to Build Your Future.", "heroSubtitle": "From kitchens to construction sites — connect directly with employers and get hired fast.", "startJobSearch": "Start Job Search", "learnMore": "Learn More", "activeJobs": "Active Jobs", "companies": "Organisations", "jobSeekers": "Job Seekers", "whyChoose": "Trusted by Workers. Preferred by Employers.", "easyJobSearch": "Effortless Job Search", "easyJobSearchDesc": "Easily discover jobs tailored to your skills, location, and industry preferences using our powerful and intuitive search filters.", "directEmployerContact": "Direct Employer Access", "directEmployerContactDesc": "Skip the middlemen, communicate directly with employers for quicker feedback and faster hiring decisions.", "mobileFriendly": "Mobile-Friendly Experience", "mobileFriendlyDesc": "Search and apply for jobs anytime, anywhere with our easy-to-use mobile platform.", "verifiedEmployers": "Localized Opportunities", "verifiedEmployersDesc": "Discover job openings near your area, making it easier to work close to home.", "popularJobCategories": "Top Job Sectors for Skilled Workers", "restaurents": "Restaurents", "hotels": "Hotels", "mart": "Mart", "construction": "Construction", "manufacturing": "Manufacturing", "logistics": "Logistics", "maintenance": "Maintenance", "security": "Security", "hospitality": "Hospitality", "readyToFind": "Ready to Find Your Next Job?", "readyToFindDesc": "Join thousands of job seekers using JOB DALAL to connect with employers and secure meaningful opportunities near you.", "getStartedNow": "Get Started Now", "forJobSeekers": "For Job Seekers", "browseJobs": "Browse Jobs", "createProfile": "Create Profile", "careerResources": "Career Resources", "forEmployers": "For Employers", "postJob": "Post a Job", "findCandidates": "Find Candidates", "pricing": "Pricing", "support": "Support", "helpCenter": "Help Center", "contactUs": "Contact Us", "privacyPolicy": "Privacy Policy", "connectingWorkers": "The Smart Way to Hire and <PERSON> Hired.", "footerPrefix": "©", "footerSuffix": "<PERSON>. All rights reserved.", "allRightsReserved": "© 2025 Job Dalal. All rights reserved.", "employerPortalSoon": "Employer portal coming soon! This would navigate to abc.com/employer"}, "time": {"justNow": "Just now", "minutesAgo": "{count}m ago", "hoursAgo": "{count}h ago", "daysAgo": "{count}d ago", "dayAgo": "1 day ago", "daysAgoPlural": "{count} days ago", "weeksAgo": "{count} weeks ago", "monthsAgo": "{count} months ago"}, "numbers": {"thousand": "K", "million": "M", "billion": "B"}, "apiMessage": {"invalidEmail": "Enter valid Email"}, "tooltipViewProfile": "View Profile", "tooltipScheduleInterview": "Schedule Interview", "tooltipViewInterviewDetails": "View Interview Details", "tooltipRescheduleInterview": "Reschedule Interview"}