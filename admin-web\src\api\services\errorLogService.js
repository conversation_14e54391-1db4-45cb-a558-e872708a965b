import httpClient from '../httpClient';
import { ENDPOINTS } from '../endpoints';

export const errorLogService = {
  async getErrorLogs({ page = 1, limit = 20, ...filters } = {}) {
    const url = ENDPOINTS.ERROR_LOGS.LIST(page, limit, filters);
    const response = await httpClient.get(url);
    return response;
  },

  async createErrorLog(payload) {
    const response = await httpClient.post(ENDPOINTS.ERROR_LOGS.CREATE, payload);
    return response;
  },

  async getErrorLogById(id) {
    const response = await httpClient.get(ENDPOINTS.ERROR_LOGS.DETAIL(id));
    return response;
  },

  async updateErrorLog(id, payload) {
    const response = await httpClient.patch(ENDPOINTS.ERROR_LOGS.UPDATE(id), payload);
    return response;
  },

  async deleteErrorLog(id) {
    const response = await httpClient.delete(ENDPOINTS.ERROR_LOGS.DELETE(id));
    return response;
  },
};