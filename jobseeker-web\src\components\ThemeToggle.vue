<template>
  <div class="theme-language-controls">
    <LanguageSelector />
    <Button
      @click="toggleTheme"
      :icon="isDark ? 'pi pi-sun' : 'pi pi-moon'"
      severity="secondary"
      outlined
      rounded
      :aria-label="isDark ? $t('common.switchToLight') : $t('common.switchToDark')"
      class="theme-toggle"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Button from 'primevue/button'
import LanguageSelector from './LanguageSelector.vue'

const isDark = ref(false)

const toggleTheme = () => {
  isDark.value = !isDark.value
  updateTheme()
}

const updateTheme = () => {
  const html = document.documentElement
  
  if (isDark.value) {
    html.classList.add('dark-theme')
    localStorage.setItem('theme', 'dark')
  } else {
    html.classList.remove('dark-theme')
    localStorage.setItem('theme', 'light')
  }
}

const initializeTheme = () => {
  const savedTheme = localStorage.getItem('theme')
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  
  isDark.value = savedTheme === 'dark' || (!savedTheme && prefersDark)
  updateTheme()
}

onMounted(() => {
  initializeTheme()
})
</script>

<style scoped>
.theme-language-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.theme-toggle {
  width: 40px !important;
  height: 40px !important;
  transition: all 0.2s ease !important;
}

.theme-toggle:hover {
  transform: scale(1.05) !important;
}

:deep(.p-button-icon) {
  font-size: 1.1rem !important;
}

@media (max-width: 768px) {
  .theme-language-controls {
    gap: 0.5rem;
  }
  
  :deep(.language-selector) {
    min-width: 100px;
  }
}
</style>