<script setup>
import { ref, reactive, watch, computed } from 'vue';
import Drawer from 'primevue/drawer';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
import Textarea from 'primevue/textarea';

const props = defineProps({
  visible: <PERSON><PERSON><PERSON>,
  feedback: Object // null for create, object for edit
});
const emit = defineEmits(['close', 'success', 'update']);

const isEditMode = computed(() => !!props.feedback && !!props.feedback.id);

const FeedbackType = {
  SUGGESTION: 'SUGGESTION',
  BUG_REPORT: 'BUG_REPORT',
  FEATURE_REQUEST: 'FEATURE_REQUEST',
  COMPLAINT: 'COMPLAINT',
  COMPLIMENT: 'COMPLIMENT',
  GENERAL: 'GENERAL'
};

const FeedbackStatus = {
  PENDING: 'PENDING',
  IN_REVIEW: 'IN_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  IMPLEMENTED: 'IMPLEMENTED'
};

const form = reactive({
  type: FeedbackType.GENERAL,
  title: '',
  description: '',
  metadata: '', // JSON string
  status: FeedbackStatus.PENDING
});

watch(() => props.feedback, (val) => {
  if (val) {
    form.type = val.type || FeedbackType.GENERAL;
    form.title = val.title || '';
    form.description = val.description || '';
    form.metadata = val.metadata ? JSON.stringify(val.metadata, null, 2) : '';
    form.status = val.status || FeedbackStatus.PENDING;
  } else {
    form.type = FeedbackType.GENERAL;
    form.title = '';
    form.description = '';
    form.metadata = '';
    form.status = FeedbackStatus.PENDING;
  }
}, { immediate: true });

const typeOptions = Object.values(FeedbackType).map(type => ({
  label: type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}));

const statusOptions = [
  { label: 'Pending', value: 'PENDING' },
  { label: 'In Review', value: 'IN_REVIEW' },
  { label: 'Approved', value: 'APPROVED' },
  { label: 'Rejected', value: 'REJECTED' },
  { label: 'Implemented', value: 'IMPLEMENTED' }
];

function handleClose() {
  emit('close');
}

function handleSubmit() {
  if (!form.title.trim() || !form.description.trim()) return;
  let parsedMetadata = undefined;
  if (form.metadata.trim()) {
    try {
      parsedMetadata = JSON.parse(form.metadata);
    } catch (e) {
      alert('Metadata must be valid JSON');
      return;
    }
  }
  const payload = {
    type: form.type,
    title: form.title.trim(),
    description: form.description.trim(),
    metadata: parsedMetadata,
    status: form.status
  };
  if (isEditMode.value) {
    emit('update', { id: props.feedback.id, ...payload });
  } else {
    emit('success', payload);
  }
  handleClose();
}
</script>

<template>
  <Drawer :visible="visible" @update:visible="handleClose" position="right" style="width: 400px">
    <template #header>
      <h3>{{ isEditMode ? 'Edit Feedback' : 'Submit Feedback' }}</h3>
    </template>
    <form class="p-4 flex flex-col gap-4" @submit.prevent="handleSubmit">
      <div>
        <label class="font-semibold">Type</label>
        <Select v-model="form.type" :options="typeOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select type" />
      </div>
      <div>
        <label class="font-semibold">Title *</label>
        <InputText v-model="form.title" class="w-full mt-1" required placeholder="Enter feedback title..." />
      </div>
      <div>
        <label class="font-semibold">Description *</label>
        <Textarea v-model="form.description" class="w-full mt-1" rows="4" required placeholder="Describe your feedback..." />
      </div>
      <div>
        <label class="font-semibold">Status *</label>
        <Select v-model="form.status" :options="statusOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select status" required />
      </div>
      <div>
        <label class="font-semibold">Metadata (JSON)</label>
        <Textarea v-model="form.metadata" class="w-full mt-1" rows="3" placeholder='{"key": "value"}' />
      </div>
      <div class="flex justify-end gap-2 mt-4">
        <Button label="Cancel" severity="secondary" @click="handleClose" type="button" />
        <Button :label="isEditMode ? 'Update' : 'Submit'" type="submit" :disabled="!form.title.trim() || !form.description.trim() || !form.status" />
      </div>
    </form>
  </Drawer>
</template> 