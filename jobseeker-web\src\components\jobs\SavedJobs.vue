<script setup>
import { ref, computed, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'
import alertManager from '@/utils/alertManager'
import { useJobsStore } from '@/stores/jobs'
import { useApplicationsStore } from '@/stores/applications'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const { t } = useI18n()
const jobsStore = useJobsStore()
const applicationsStore = useApplicationsStore()
const authStore = useAuthStore()
// State

const isLoading = ref(false)
const searchQuery = ref('')
const selectedIndustry = ref(null)
const selectedJobType = ref(null)
const sortBy = ref('savedDate')
const industryOptions = ref([])

// Flattened saved jobs from applications
const savedJobsFlattened = computed(() =>
  (applicationsStore.applications || []).map(app => ({
    ...app.job,
    savedDate: app.savedDate || app.createdAt,
    applicationId: app.id,
    status: app.status,
    // add more fields from app if needed
  }))
)

// Filter options
const jobTypeOptions = [
    { label: 'Full-time', value: 'Full-time' },
    { label: 'Part-time', value: 'Part-time' },
    { label: 'Contract', value: 'Contract' },
    { label: 'Temporary', value: 'Temporary' }
]

const sortOptions = [
    { label: 'Recently Saved', value: 'savedDate' },
    { label: 'Recently Posted', value: 'postedDate' },
    { label: 'Salary: High to Low', value: 'salaryMax' },
    { label: 'Salary: Low to High', value: 'salaryMin' },
    { label: 'Closing Soon', value: 'closingDate' },
    { label: 'Most Applicants', value: 'applicationsCount' }
]

// Computed properties
const recentlySavedJobs = computed(() => {
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    return savedJobsFlattened.value.filter(job => new Date(job.savedDate) >= oneWeekAgo)
})

const urgentSavedJobs = computed(() =>
    savedJobsFlattened.value.filter(job => job.isUrgent || job.urgency === 'URGENT')
)

const appliedFromSaved = computed(() =>
    savedJobsFlattened.value.filter(job => applicationsStore.applications.some(app => app.job?.id === job.id))
)

const filteredSavedJobs = computed(() => {
    let filtered = savedJobsFlattened.value || [];

    // Apply search filter
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(job =>
            (job.title && typeof job.title === 'string' && job.title.toLowerCase().includes(query)) ||
            (job.company && typeof job.company === 'string' && job.company.toLowerCase().includes(query)) ||
            (job.location && typeof job.location === 'string' && job.location.toLowerCase().includes(query)) ||
            (Array.isArray(job.skills) && job.skills.some(skill => typeof skill === 'string' && skill.toLowerCase().includes(query)))
        );
    }

    // Apply industry filter
    if (selectedIndustry.value) {
        const selectedIndustryLower = selectedIndustry.value.toLowerCase();
        filtered = filtered.filter(job => {
            if (typeof job.industry === 'string') {
                return job.industry.toLowerCase() === selectedIndustryLower;
            } else if (job.industry && typeof job.industry.name === 'string') {
                return job.industry.name.toLowerCase() === selectedIndustryLower;
            }
            return false;
        });
    }

    // Apply job type filter
    if (selectedJobType.value) {
        const selectedJobTypeLower = selectedJobType.value.toLowerCase();
        filtered = filtered.filter(job =>
            (job.jobTypeName && typeof job.jobTypeName === 'string' && job.jobTypeName.toLowerCase() === selectedJobTypeLower)
        );
    }

    // Apply sorting
    filtered.sort((a, b) => {
        switch (sortBy.value) {
            case 'savedDate':
                return new Date(b.savedDate) - new Date(a.savedDate);
            case 'postedDate':
                return new Date(b.postedDate) - new Date(a.postedDate);
            case 'salaryMax':
                return b.salaryMax - a.salaryMax;
            case 'salaryMin':
                return a.salaryMin - b.salaryMin;
            case 'closingDate':
                return new Date(a.closingDate) - new Date(b.closingDate);
            case 'applicationsCount':
                return b.applicationsCount - a.applicationsCount;
            default:
                return 0;
        }
    });

    return filtered;
});

// Helper functions
const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now - date
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHr = Math.floor(diffMin / 60)
    const diffDays = Math.floor(diffHr / 24)

    if (diffSec < 60) return 'just now'
    if (diffMin < 60) return `${diffMin} min ago`
    if (diffHr < 24) return `${diffHr} hour${diffHr === 1 ? '' : 's'} ago`
    if (diffDays === 1) return '1 day ago'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} week${Math.floor(diffDays / 7) === 1 ? '' : 's'} ago`
    return `${Math.floor(diffDays / 30)} month${Math.floor(diffDays / 30) === 1 ? '' : 's'} ago`
}

// Truncate description to one line with ellipsis
const truncateDescription = (desc) => {
  if (!desc) return '';
  // Remove HTML tags if any
  const text = desc.replace(/<[^>]*>/g, '').replace(/\n/g, ' ');
  // Limit to ~100 chars or first sentence
  if (text.length <= 100) return text;
  return text.slice(0, 100).trim() + '...';
}

const canApply = (jobId) => {
  // Check if already applied (saved applications)
  const savedApps = applicationsStore.applications.filter(app => app && app.jobId === jobId)
  const hasSavedApp = savedApps.length > 0

  // Check if already in pending applications
  const hasPendingApp = applicationsStore.pendingApplications.some(app => app.jobId === jobId)

  // Can't apply if already has saved application (unless rejected) or pending application
  if (hasPendingApp) return false

  if (hasSavedApp) {
    // Find the latest application by appliedDate
    const latestApp = savedApps.reduce((a, b) =>
      new Date(a.appliedDate) > new Date(b.appliedDate) ? a : b
    )
    return latestApp.status === 'REJECTED'
  }

  return true // can apply if no application exists
}

const getEmptyStateTitle = () => {
    if (searchQuery.value) {
        return 'No matching saved jobs'
    }
    return 'No saved jobs yet'
}

const getEmptyStateMessage = () => {
    if (searchQuery.value) {
        return 'Try adjusting your search terms or filters to find saved jobs.'
    }
    return 'Start saving jobs you\'re interested in to keep track of them here.'
}

// Actions
const refreshSavedJobs = () => {
    isLoading.value = true
    // Simulate API call
    setTimeout(() => {
        isLoading.value = false
        alertManager.showSuccess('Refreshed', 'Saved jobs have been refreshed')
    }, 1000)
}

const clearAllSaved = async () => {
    const confirmed = await alertManager.showConfirm(
        'Clear All Saved Jobs',
        'Are you sure you want to remove all saved jobs? This action cannot be undone.',
        {
            confirmText: 'Clear All',
            cancelText: 'Cancel'
        }
    )

    if (confirmed) {
        jobsStore.clearSavedJobs()
        alertManager.showSuccess('Cleared', 'All saved jobs have been removed')
    }
}

const removeSavedJob = async (job) => {
    const confirmed = await alertManager.showConfirm(
        'Remove Saved Job',
        `Remove "${job.title}" from your saved jobs?`,
        {
            confirmText: 'Remove',
            cancelText: 'Cancel'
        }
    )

    if (confirmed) {
        jobsStore.saveJob(job.id)
        alertManager.showSuccess('Removed', 'Job removed from saved list')
    }
}

const viewJobDetails = (job) => {
    router.push(`/jobseeker/jobs/${job?.id}`)
}

const applyToJob = async (job) => {
  if (!canApply(job.id)) return;
  try {
    // Create a pending application instead of saving immediately
    const pendingApp = applicationsStore.createPendingApplication(job);
    alertManager.showSuccess('Added to Applications', `"${job.title}" has been added to your applications. Click "Save" to submit it.`);
  } catch (error) {
    alertManager.showError('Application Failed', error.message || 'Failed to add application. Please try again.');
  }
}

const shareJob = (job) => {
    const jobUrl = `${window.location.origin}/jobseeker/jobs/${job.id}`

    if (navigator.share) {
        navigator.share({
            title: job.title,
            text: `Check out this job: ${job.title} at ${job.company}`,
            url: jobUrl
        })
    } else {
        navigator.clipboard.writeText(jobUrl).then(() => {
            alertManager.showSuccess('Link Copied', 'Job URL copied to clipboard')
        }).catch(() => {
            alertManager.showError('Copy Failed', 'Failed to copy link to clipboard')
        })
    }
}

const searchJobs = () => {
    router.push('/jobseeker/jobs')
}

const clearSearch = () => {
    searchQuery.value = ''
    selectedIndustry.value = null
    selectedJobType.value = null
}

const getStatusSeverity = (status) => {
    switch (status) {
        case 'ACTIVE': return 'success'
        case 'DRAFT': return 'warning'
        case 'CLOSED': return 'danger'
        case 'PAUSED': return 'secondary'
        default: return 'info'
    }
}

onMounted(async () => {
    // Load saved jobs data
    applicationsStore.fetchApplications();
    // Fetch industries from authStore if not already loaded
    if (!authStore.industries || !Array.isArray(authStore.industries) || authStore.industries.length === 0) {
        await authStore.getIndustries();
    }
    // Map industries to label/value for Select
    if (Array.isArray(authStore.industries)) {
        industryOptions.value = authStore.industries.map(ind => ({
            label: ind.name,
            value: ind.name // or ind.id if you want to filter by id
        }));
    } else {
        industryOptions.value = [];
    }
    console.log('Applications:', applicationsStore.applications);
    console.log('Jobs:', jobsStore.jobs);
    jobsStore.jobs.forEach(job => console.log(job.id));
});

// If using <keep-alive> for your jobs page, also use:
onActivated(() => {
    applicationsStore.fetchApplications();
});
</script>


<template>
    <div class="saved-jobs-page flex-1 overflow-x-hidden overflow-y-auto">
        <div class="saved-jobs-content">

            <!-- Filter and Sort Controls -->
            <div class="controls-section">
                <div class="search-controls">
                    <div class="search-input-wrapper">
                        <i class="pi pi-search search-icon"></i>
                        <InputText v-model="searchQuery" placeholder="Search saved jobs..." class="search-input" />
                    </div>
                </div>

                <div class="filter-controls">
                    <Select v-model="selectedIndustry" :options="industryOptions" optionLabel="label"
                        optionValue="value" placeholder="All Industries" class="filter-dropdown" showClear />

                    <Select v-model="selectedJobType" :options="jobTypeOptions" optionLabel="label" optionValue="value"
                        placeholder="All Job Types" class="filter-dropdown" showClear />

                    <Select v-model="sortBy" :options="sortOptions" optionLabel="label" optionValue="value"
                        placeholder="Sort by" class="sort-dropdown" />
                </div>
            </div>

            <!-- Saved Jobs List -->
            <div class="saved-jobs-list" v-if="filteredSavedJobs.length > 0">
                <div v-for="job in filteredSavedJobs" :key="job.applicationId"
                    :class="['saved-job-card', { 'urgent': job.isUrgent, 'applied': canApply(job.id) }]">
                    <div class="job-header">
                        <div class="job-avatar">
                            <Avatar
                                :label="(typeof job.title === 'string' && job.title.length > 0 ? job.title.charAt(0).toUpperCase() : '?')"
                                shape="circle"
                                size="large"
                                style="background-color: var(--primary-color); color: #fff; font-weight: bold; font-size: 1.2rem;"
                            />
                        </div>
                        <div class="job-basic-info">
                            <h3 class="job-title" @click="viewJobDetails(job)">{{ job.title }}</h3>
                            <p class="job-company">{{ job.company }}</p>
                            <div class="job-badges">
                                <Tag v-if="job.isFeatured" :value="t('jobs.featured')" severity="success"
                                    class="featured-badge" />
                                <Tag v-if="job.urgency === 'URGENT'" :value="t('jobs.urgent')" severity="danger" />
                                <Tag :value="job.status" :severity="getStatusSeverity(job.status)" />
                                <Tag :value="job.paymentType" severity="info" />
                            </div>
                        </div>
                        <div class="job-actions">
                            <Button icon="pi pi-heart-fill" text size="small" @click="removeSavedJob(job)"
                                v-tooltip.bottom="'Remove from Saved'" class="remove-saved-btn" />
                            <Button icon="pi pi-external-link" text size="small" @click="viewJobDetails(job)"
                                v-tooltip.bottom="'View Details'" />
                        </div>
                    </div>

                    <div class="job-content">
                        <div class="job-description">{{ truncateDescription(job.description) }}</div>

                        <div class="job-details">
                            <div class="job-meta">
                                <span class="job-location">
                                    <i class="pi pi-map-marker"></i>
                                    {{ job.location }}
                                </span>
                                <span class="job-vacancies">
                                    <i class="pi pi-users"></i>
                                    {{ job.vacancies }} {{ job.vacancies === 1 ? 'position' : 'positions' }}
                                </span>
                                <span class="job-salary">
                                    <i class="pi pi-indian-rupee"></i>
                                    {{ (job.salary || job.salaryMin || 0).toLocaleString() }} {{ job.paymentType ?
                                        job.paymentType.toLowerCase() : '' }}
                                </span>
                                <span class="job-posted">
                                    <i class="pi pi-calendar"></i>
                                    {{ t('jobs.postedAgo', { time: formatDate(job.postedDate || job.createdAt) }) }}
                                </span>
                                <span class="job-saved">
                                    <i class="pi pi-heart"></i>
                                    Saved {{ formatDate(job.savedDate) }}
                                </span>
                            </div>

                            <div class="job-additional-info"
                                v-if="job.accommodation || job.transportation || job.foodProvided">
                                <div class="info-items">
                                    <span v-if="job.accommodation" class="info-item">
                                        <i class="pi pi-home"></i>
                                        Accommodation
                                    </span>
                                    <span v-if="job.transportation" class="info-item">
                                        <i class="pi pi-car"></i>
                                        Transportation
                                    </span>
                                    <span v-if="job.foodProvided" class="info-item">
                                        <i class="pi pi-shopping-cart"></i>
                                        Food Provided
                                    </span>
                                </div>
                            </div>

                            <div class="job-skills" v-if="job.skills && job.skills.length > 0">
                                <Tag v-for="skill in job.skills.slice(0, 4)" :key="skill" :value="skill"
                                    class="skill-tag" />
                                <span v-if="job.skills.length > 4" class="more-skills">
                                    +{{ job.skills.length - 4 }} more
                                </span>
                            </div>
                            <div class="job-responsibilities" v-if="job.responsibilities && job.responsibilities.length > 0">
                                <Tag v-for="item in job.responsibilities" :key="item" :value="item" class="skill-tag" />
                            </div>
                            <div class="job-requirements" v-if="job.requirements && job.requirements.length > 0">
                                <Tag v-for="item in job.requirements" :key="item" :value="item" class="skill-tag" />
                            </div>
                            <div class="job-benefits" v-if="job.benefits && job.benefits.length > 0">
                                <Tag v-for="item in job.benefits" :key="item" :value="item" class="skill-tag" />
                            </div>
                        </div>
                    </div>

                    <div class="job-footer">
                        <div class="job-stats">
                            <span class="applicants-count">
                                <i class="pi pi-users"></i>
                                {{ job.applicationsCount }} applicants
                            </span>
                            <span class="views-count">
                                <i class="pi pi-eye"></i>
                                {{ job.viewsCount }} views
                            </span>
                            <span class="closing-date" v-if="job.closingDate">
                                <i class="pi pi-clock"></i>
                                Closes {{ formatDate(job.closingDate) }}
                            </span>
                        </div>
                        <div class="job-footer-actions">
                            <Button @click="shareJob(job)" icon="pi pi-share-alt" text size="small"
                                v-tooltip.bottom="'Share Job'" />
                            <Button @click="applyToJob(job)" :label="canApply(job.id) ? t('dashboard.applyNow') : t('jobs.applied')"
                                :disabled="!canApply(job.id)" class="apply-button" size="large">
                                <template v-if="canApply(job.id)">
                                    <i class="pi pi-check"></i>
                                    Apply
                                </template>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-else-if="!isLoading" class="empty-state">
                <div class="empty-content">
                    <i class="pi pi-heart empty-icon"></i>
                    <h3>{{ getEmptyStateTitle() }}</h3>
                    <p>{{ getEmptyStateMessage() }}</p>
                    <div class="empty-actions">
                        <Button icon="pi pi-search" :label="searchQuery ? 'Clear Search' : 'Browse Jobs'"
                            @click="searchQuery ? clearSearch() : searchJobs()" />
                        <Button v-if="searchQuery" @click="clearSearch" icon="pi pi-times" label="Clear Search"
                            outlined />
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="isLoading" class="loading-state">
                <i class="pi pi-spin pi-spinner loading-icon"></i>
                <p>Loading your saved jobs...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.saved-jobs-page {
    background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.saved-jobs-content {
    margin: 0 auto;
    padding: 1rem;
}

.page-header {
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    font-size: 2rem;
    color: var(--red-500);
}

.header-text p {
    color: var(--text-color-secondary);
    margin: 0;
    font-size: 1.1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.controls-section {
    background: var(--surface-card);
    border: 1px solid var(--surface-border);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-controls {
    flex: 1;
    min-width: 300px;
}

.search-input-wrapper {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-secondary);
}

.search-input {
    width: 100% !important;
    padding-left: 3rem !important;
    height: 48px !important;
    border-radius: 8px !important;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-dropdown,
.sort-dropdown {
    min-width: 150px;
}

.saved-jobs-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.saved-job-card {
    background: var(--surface-card);
    border: 1px solid var(--surface-border);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all var(--transition-duration) ease;
    position: relative;
}

.saved-job-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.saved-job-card.urgent {
    border-left: 4px solid var(--red-500);
}

.saved-job-card.applied {
    border-left: 4px solid var(--green-500);
}

.job-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.job-basic-info {
    flex: 1;
    min-width: 0;
}

.job-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.job-title:hover {
    color: var(--primary-color);
}

.job-company {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.job-badges {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.featured-badge,
.urgent-badge,
.job-type-badge,
.applied-badge {
    font-size: 0.75rem !important;
}

.job-actions {
    display: flex;
    gap: 0.25rem;
}

.remove-saved-btn {
    color: var(--red-500) !important;
}

.job-content {
    margin-bottom: 1rem;
}

.job-description {
    color: var(--text-color-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.job-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.job-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.job-location,
.job-vacancies,
.job-salary,
.job-posted,
.job-saved {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-color-secondary);
    font-size: 0.85rem;
}

.job-location i,
.job-posted i,
.job-saved i {
    color: var(--primary-color);
    font-size: 0.75rem;
}

.job-vacancies i {
    color: var(--primary-color);
    font-size: 0.75rem;
}

.job-salary {
    font-weight: 600;
    color: var(--primary-color);
}

.job-salary i {
    color: var(--primary-color);
    font-size: 0.75rem;
}

.job-saved i {
    color: var(--red-500);
}

.job-skills {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.skill-tag {
    font-size: 0.75rem !important;
    background: var(--surface-100) !important;
    color: var(--text-color-secondary) !important;
}

.more-skills {
    font-size: 0.8rem;
    color: var(--text-color-secondary);
    font-style: italic;
}

.job-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
}

.job-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.applicants-count,
.views-count,
.closing-date {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-color-secondary);
    font-size: 0.8rem;
}

.applicants-count i,
.views-count i,
.closing-date i {
    color: var(--primary-color);
    font-size: 0.75rem;
}

.job-footer-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.apply-button {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    font-weight: 600 !important;
}

.apply-button:disabled {
    background: var(--green-500) !important;
    border-color: var(--green-500) !important;
    opacity: 1 !important;
}

.empty-state,
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}

.empty-content {
    max-width: 400px;
}

.empty-icon,
.loading-icon {
    font-size: 4rem;
    color: var(--text-color-secondary);
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.empty-state p {
    color: var(--text-color-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.job-additional-info {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.info-items {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-color-secondary);
    font-size: 0.85rem;
}

.info-item i {
    color: var(--primary-color);
    font-size: 0.75rem;
}

.section-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .saved-jobs-content {
        padding: 0.2rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .header-actions {
        align-self: flex-end;
    }

    .header-text h1 {
        font-size: 2rem;
    }

    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .controls-section {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .search-controls {
        min-width: auto;
    }

    .filter-controls {
        justify-content: stretch;
    }

    .filter-dropdown,
    .sort-dropdown {
        flex: 1;
        min-width: auto;
    }

    .job-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .job-actions {
        align-self: flex-end;
    }

    .job-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .job-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .job-footer-actions {
        width: 100%;
        justify-content: space-between;
    }

    .apply-button {
        flex: 1;
    }

    .empty-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .stats-overview {
        grid-template-columns: 1fr;
    }

    .filter-controls {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
  .p-tag, .skill-tag, .job-badges .p-tag {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    margin-bottom: 0.25rem !important;
  }
}
</style>