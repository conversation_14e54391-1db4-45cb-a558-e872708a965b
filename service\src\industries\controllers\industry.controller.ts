import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IndustryService } from '../services/industry.service';
import { CreateIndustryDto } from '../dto/create-industry.dto';
import { UpdateIndustryDto } from '../dto/update-industry.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';
import { IndustryEntity } from '../entities/industry.entity';

@ApiTags('Industries')
@Controller('industries')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class IndustryController {
  constructor(private readonly industryService: IndustryService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new industry (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'Industry created successfully',
    type: () => ApiResponseDto<IndustryEntity>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async create(@Body() createIndustryDto: CreateIndustryDto) {
    try {
      const result = await this.industryService.create(createIndustryDto);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('CREATE_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.JOB_SEEKER, UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Get all active industries' })
  @ApiResponse({
    status: 200,
    description: 'List of active industries retrieved successfully',
    type: () => ApiResponseDto<IndustryEntity[]>,
  })
  async findAll() {
    try {
      const result = await this.industryService.findAll();
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('GET_ALL_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific active industry by ID' })
  @ApiResponse({
    status: 200,
    description: 'Active industry retrieved successfully',
    type: () => ApiResponseDto<IndustryEntity>,
  })
  @ApiResponse({ status: 404, description: 'Active industry not found' })
  async findOne(@Param('id') id: string) {
    try {
      const result = await this.industryService.findOne(id);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('GET_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update an industry (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Industry updated successfully',
    type: () => ApiResponseDto<IndustryEntity>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Industry not found' })
  async update(@Param('id') id: string, @Body() updateIndustryDto: UpdateIndustryDto) {
    try {
      const result = await this.industryService.update(id, updateIndustryDto);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('UPDATE_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete an industry (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Industry deleted successfully',
    type: () => ApiResponseDto<void>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Industry not found' })
  async remove(@Param('id') id: string) {
    try {
      const result = await this.industryService.remove(id);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('DELETE_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }
}
