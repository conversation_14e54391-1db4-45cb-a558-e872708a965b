import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { AudienceMessageService } from '../services/audience-message.service';
import { CreateAudienceMessageDto, UpdateAudienceMessageDto } from '../dto/audience-message.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { ApiTags, ApiOperation, ApiResponse as SwaggerResponse } from '@nestjs/swagger';
import { ApiResponseDto } from '../dto/api-response.dto';
import { AudienceMessageEntity } from '../entities/audience-message.entity';

@ApiTags('Audience Messages')
@Controller('welcome/:welcomeId/audience-messages')
export class AudienceMessageController {
  constructor(private readonly audienceMessageService: AudienceMessageService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new audience message' })
  @SwaggerResponse({
    status: HttpStatus.CREATED,
    description: 'Audience message created successfully',
    type: ApiResponseDto<AudienceMessageEntity>,
  })
  async create(
    @Param('welcomeId') welcomeId: string,
    @Body() createDto: CreateAudienceMessageDto,
  ): Promise<ApiResponseDto<AudienceMessageEntity>> {
    try {
      const audienceMessage = await this.audienceMessageService.create(welcomeId, createDto);
      return ApiResponseDto.success(audienceMessage, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error(
        'AUDIENCE_MESSAGE_CREATE_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all audience messages for a welcome' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Audience messages retrieved successfully',
    type: ApiResponseDto<[AudienceMessageEntity]>,
  })
  async findAll(
    @Param('welcomeId') welcomeId: string,
  ): Promise<ApiResponseDto<AudienceMessageEntity[]>> {
    try {
      const audienceMessages = await this.audienceMessageService.findAll(welcomeId);
      return ApiResponseDto.success(audienceMessages, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'AUDIENCE_MESSAGES_FETCH_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an audience message by ID' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Audience message retrieved successfully',
    type: ApiResponseDto<AudienceMessageEntity>,
  })
  async findOne(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<AudienceMessageEntity>> {
    try {
      const audienceMessage = await this.audienceMessageService.findOne(id);
      return ApiResponseDto.success(audienceMessage, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'AUDIENCE_MESSAGE_NOT_FOUND',
        error.message,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update an audience message' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Audience message updated successfully',
    type: ApiResponseDto<AudienceMessageEntity>,
  })
  async update(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
    @Body() updateDto: UpdateAudienceMessageDto,
  ): Promise<ApiResponseDto<AudienceMessageEntity>> {
    try {
      const audienceMessage = await this.audienceMessageService.update(id, updateDto);
      return ApiResponseDto.success(audienceMessage, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'AUDIENCE_MESSAGE_UPDATE_ERROR',
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete an audience message' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Audience message deleted successfully',
    type: ApiResponseDto<void>,
  })
  async delete(
    @Param('welcomeId') welcomeId: string,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<void>> {
    try {
      await this.audienceMessageService.delete(id);
      return ApiResponseDto.success(null, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'AUDIENCE_MESSAGE_DELETE_ERROR',
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
