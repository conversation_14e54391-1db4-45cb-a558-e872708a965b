import { Controller, Get, Post, Body, Param, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { JobService } from '../services/job.service';
import { JobScheduleDto } from '../dto/job-schedule.dto';
import { JobEntity } from '../entities/job.entity';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';

@ApiTags('Job Schedule')
@Controller('job-schedule')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JobScheduleController {
  constructor(private readonly jobService: JobService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get all pending jobs' })
  @ApiResponse({
    status: 200,
    description: 'Returns all pending jobs',
    type: PaginatedResponseDto<JobEntity>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async findAllPendingJobs(
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobEntity>> {
    return this.jobService.findAllPendingJobs(paginationDto);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get a specific pending job' })
  @ApiResponse({
    status: 200,
    description: 'Returns the pending job',
    type: JobEntity,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  async findPendingJob(@Param('id') id: string): Promise<JobEntity> {
    return this.jobService.findPendingJobById(id);
  }

  @Post(':id/schedule')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Schedule a pending job' })
  @ApiResponse({
    status: 200,
    description: 'Job has been scheduled successfully',
    type: JobEntity,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid status or job is not pending' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  async scheduleJob(
    @Param('id') id: string,
    @Body() scheduleDto: JobScheduleDto,
    @Request() req,
  ): Promise<JobEntity> {
    return this.jobService.scheduleJob(id, req.user.role, scheduleDto);
  }

  @Get(':id/history')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get job schedule history' })
  @ApiResponse({
    status: 200,
    description: 'Returns the job schedule history',
    type: JobEntity,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  async getJobScheduleHistory(@Param('id') id: string): Promise<JobEntity> {
    return this.jobService.getJobScheduleHistory(id);
  }
}
