import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'


export const jobsService = {

  async getJobList(page, limit) {
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.LIST(page, limit))
      return response;
    } catch (error) {
      return error
    }
  },

  async findOrgUser(searchParam) {
    try {
      return await httpClient.post(ENDPOINTS.JOBS.GET_ORG_USER, searchParam)
    } catch (error) {
      return error
    }
  },
  async createJob(payload) {
    try {
      return await httpClient.post(ENDPOINTS.JOBS.CREATE, payload)
    } catch (error) {
      return error
    }
  },

  updateJob(jobId, jobData) {
    return httpClient.patch(`/jobs/${jobId}`, jobData);
  },

  async updateJobStatus(jobId, status) {
    try {
      const response = await httpClient.patch(ENDPOINTS.JOBS.UPDATE_STATUS(jobId), { status })
      return response;
    } catch (error) {
      return error
    }
  },
}
