@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* PrimeVue Theme CSS Custom Properties */
:root {
  /* Light theme colors */
  --primary-color: #14afdd;
  --primary-action-color: #0052cc;
  --primary-color-text: #ffffff;
  --surface-0: #ffffff;
  --surface-50: #fafafa;
  --surface-100: #f5f5f5;
  --surface-200: #eeeeee;
  --surface-300: #e0e0e0;
  --surface-400: #bdbdbd;
  --surface-500: #9e9e9e;
  --surface-600: #757575;
  --surface-700: #616161;
  --surface-800: #424242;
  --surface-900: #212121;

  --text-color: #212121;
  --text-color-secondary: #757575;
  --border-color: #e0e0e0;
  --surface-color: #ffffff;
  --surface-card: #ffffff;
  --surface-overlay: #ffffff;
  --surface-border: #e0e0e0;
  --surface-hover: #f5f5f5;
  --focus-ring: 0 0 0 0.2rem rgba(0, 82, 204, 0.2);

  /* Component specific colors */
  --highlight-bg: rgba(0, 82, 204, 0.3);
  --highlight-text-color: #14afdd;
  --mask-bg: rgba(0, 0, 0, 0.4);

  /* Status colors */
  --green-500: #22c55e;
  --blue-500: #3b82f6;
  --yellow-500: #eab308;
  --cyan-500: #06b6d4;
  --pink-500: #ec4899;
  --indigo-500: #6366f1;
  --teal-500: #14b8a6;
  --orange-500: #f97316;
  --bluegray-500: #64748b;
  --purple-500: #a855f7;
  --red-500: #ef4444;

  /* Gradients */
  --login-bg-start: #14afdd;
  --login-bg-end: #5e3f9c;
  --dashboard-bg-start: #f8fafc;
  --dashboard-bg-end: #e2e8f0;
  --jobs-bg-start: #f8fafc;
  --jobs-bg-end: #e2e8f0;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-family-mono: monospace;
  --line-height: 1.6;
  --font-weight: 400;
  --color-scheme: light;
  --font-size-1-2: 1.2rem;
  --font-size-1-4: 1.4rem;
  --font-size-1-6: 1.6rem;
  --font-size-1-8: 1.8rem;

  /* Transitions */
  --transition-duration: 150ms;

  /* Header height for sticky positioning */
  --header-height: 80px;
}

/* Dark theme colors */
.dark-theme {
  --primary-color: #14afdd;
  --primary-color-text: #ffffff;
  --surface-0: #0f172a;
  --surface-50: #1e293b;
  --surface-100: #334155;
  --surface-200: #475569;
  --surface-300: #64748b;
  --surface-400: #94a3b8;
  --surface-500: #cbd5e1;
  --surface-600: #e2e8f0;
  --surface-700: #f1f5f9;
  --surface-800: #f8fafc;
  --surface-900: #ffffff;

  --text-color: #f8fafc;
  --text-color-secondary: #cbd5e1;
  --border-color: #475569;
  --surface-color: #1e293b;
  --surface-card: #1e293b;
  --surface-overlay: #1e293b;
  --surface-border: #475569;
  --surface-hover: #334155;
  --focus-ring: 0 0 0 0.2rem rgba(66, 153, 225, 0.2);

  /* Component specific colors */
  --highlight-bg: rgba(66, 153, 225, 0.3);
  --highlight-text-color: #5e3f9c;
  --mask-bg: rgba(0, 0, 0, 0.6);

  /* Gradients for dark theme */
  --login-bg-start: #1a202c;
  --login-bg-end: #2d3748;
  --dashboard-bg-start: #1a202c;
  --dashboard-bg-end: #2d3748;
  --jobs-bg-start: #1a202c;
  --jobs-bg-end: #2d3748;

  color-scheme: dark;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--surface-0);
  color: var(--text-color);
  font-family: var(--font-family);
  transition: background-color var(--transition-duration) ease, color var(--transition-duration) ease;
}

#app {
  width: 100%;
  min-height: 100vh;
  background-color: var(--surface-0);
  color: var(--text-color);
}

/* CRITICAL: Sticky Header Styles - Apply to ALL header types */
.page-header,
.landing-header,
.dashboard-header {
  /* position: sticky !important; */
  /* top: 0 !important; */
  /* z-index: 1000 !important; */
  background: var(--surface-card) !important;
  border-bottom: 1px solid var(--surface-border) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  transition: all var(--transition-duration) ease !important;
}

.p-breadcrumb {
  padding: 0 !important;
}

.page-header,
.dashboard-header {
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 0.5rem 1.5rem;
}

/* Enhanced shadow on scroll for all headers */
.page-header.scrolled,
.landing-header.scrolled,
.dashboard-header.scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Dark theme header adjustments for all headers */
.dark-theme .page-header,
.dark-theme .landing-header,
.dark-theme .dashboard-header {
  background: rgba(11, 17, 30, 0.9) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.dark-theme .page-header.scrolled,
.dark-theme .landing-header.scrolled,
.dark-theme .dashboard-header.scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;
}

/* Landing page header specific adjustments */
.landing-header {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

.dark-theme .landing-header {
  background: rgba(11, 17, 30, 0.9) !important;
}

/* Dashboard header specific adjustments */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

.dark-theme .dashboard-header {
  background: rgba(30, 41, 59, 0.95) !important;
}

/* Ensure content doesn't hide behind sticky header */
.page-content,
.jobs-content,
.profile-content {
  padding-top: 0 !important;
}

/* Adjust main content areas to account for sticky header */
.dashboard-container,
.profile-page,
.job-details-page {
  padding-top: 0 !important;
}

/* Force sticky positioning on all page containers */
/* .dashboard-container .dashboard-header,
.jobs-page .page-header,
.profile-page .page-header,
.applications-page .page-header,
.job-details-page .page-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
} */

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-color);
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  color: var(--text-color-secondary);
  line-height: 1.6;
  margin: 0;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-duration) ease;
}

a:hover {
  color: var(--primary-color);
  opacity: 0.8;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--surface-100);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--surface-400);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--surface-500);
}

/* Focus styles */
*:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* Selection styles */
::selection {
  background-color: var(--highlight-bg);
  color: var(--highlight-text-color);
}

/* Utility classes */
.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--text-color-secondary) !important;
}

.bg-surface {
  background-color: var(--surface-color) !important;
}

.border-surface {
  border-color: var(--border-color) !important;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Alert Dialog Styles */
.alert-dialog-root {
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid var(--surface-border) !important;
}

.alert-dialog-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem !important;
  border-bottom: 1px solid var(--surface-border) !important;
  background: var(--surface-card) !important;
  border-radius: 12px 12px 0 0 !important;
}

.alert-dialog-content {
  padding: 0 !important;
  background: var(--surface-card) !important;
}

.alert-dialog-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem !important;
  background: var(--surface-card) !important;
  border-radius: 0 0 12px 12px !important;
  border-top: 1px solid var(--surface-border) !important;
}

.alert-dialog-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  gap: 1rem;
}

.alert-dialog-icon {
  margin-bottom: 0.5rem;
}

.alert-dialog-message {
  color: var(--text-color);
  font-size: 1rem;
  line-height: 1.5;
}

.alert-dialog-message p {
  margin: 0;
  color: var(--text-color);
}

.alert-dialog-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  flex-wrap: wrap;
}

.alert-dialog-confirm,
.alert-dialog-cancel {
  min-width: 100px !important;
  height: 40px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
}

/* Alert Dialog Type Specific Styles */
.alert-dialog--error .alert-dialog-header {
  border-bottom-color: rgba(239, 68, 68, 0.2) !important;
}

.alert-dialog--success .alert-dialog-header {
  border-bottom-color: rgba(16, 185, 129, 0.2) !important;
}

.alert-dialog--warning .alert-dialog-header {
  border-bottom-color: rgba(245, 158, 11, 0.2) !important;
}

.alert-dialog--info .alert-dialog-header {
  border-bottom-color: rgba(59, 130, 246, 0.2) !important;
}

.header-content h1 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

/* Dark theme alert dialog adjustments */
.dark-theme .alert-dialog-root {
  background: var(--surface-card) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4) !important;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  html {
    font-size: 13px;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  /* Adjust header height for mobile */
  :root {
    --header-height: 70px;
  }

  .alert-dialog-root {
    margin: 1rem !important;
    width: calc(100% - 2rem) !important;
    max-width: none !important;
  }

  .alert-dialog-actions {
    flex-direction: column;
  }

  .alert-dialog-confirm,
  .alert-dialog-cancel {
    width: 100% !important;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  /* Further adjust header height for small mobile */
  :root {
    --header-height: 65px;
  }

  .alert-dialog-body {
    padding: 1rem;
  }

  .alert-dialog-header {
    padding: 1rem 1rem 0.75rem 1rem !important;
  }

  .alert-dialog-footer {
    padding: 0.75rem 1rem 1rem 1rem !important;
  }
}

/* Job description HTML content styling */
.job-description {
  line-height: 1.6;
  word-break: break-word;
}

.job-description strong,
.job-description b {
  font-weight: 700;
  color: var(--text-color);
}

.job-description em,
.job-description i {
  font-style: italic;
  color: var(--text-color);
}

.job-description u {
  text-decoration: underline;
  color: var(--text-color);
}

.job-description p {
  margin: 0 0 1rem 0;
}

.job-description p:last-child {
  margin-bottom: 0;
}

/* Ensure proper spacing for HTML content */
.job-description * {
  color: inherit;
}

/* Dark theme adjustments for job description */
.dark-theme .job-description strong,
.dark-theme .job-description b,
.dark-theme .job-description em,
.dark-theme .job-description i,
.dark-theme .job-description u {
  color: var(--text-color);
}