// API configuration and main exports
import { authService } from './services/authService'
import { industriesService } from './services/industriesService'
import { jobsService } from './services/jobsService'
import { usersService } from './services/usersService'
// import { applicationsService } from './services/applicationsService'
// import { profileService } from './services/profileService'
// import { referenceService } from './services/referenceService'
// import { dashboardService } from './services/dashboardService'
// import { companiesService } from './services/companiesService'
// import { uploadService } from './services/uploadService'
import { errorLogService } from './services/errorLogService'
import { jobEnumService } from './services/jobEnumService'
import httpClient, { API_CONFIG } from './httpClient'
import { ENDPOINTS } from './endpoints'

// Error handling utility
export class ApiError extends Error {
  constructor(message, status = 500, code = 'API_ERROR') {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.code = code
  }
}

// Response wrapper utility
export const createApiResponse = (data, success = true, message = '') => ({
  success,
  data,
  message,
  timestamp: new Date().toISOString()
})

// Request wrapper with error handling
export const apiRequest = async (apiCall, ...args) => {
  try {
    const response = await apiCall(...args)
    return response
  } catch (error) {
    console.error('API Request Error:', error)
    
    if (error instanceof ApiError) {
      throw error
    }
    
    // Convert generic errors to ApiError
    throw new ApiError(
      error.message || 'An unexpected error occurred',
      error.status || 500,
      error.code || 'UNKNOWN_ERROR'
    )
  }
}

// Retry utility for failed requests
export const withRetry = async (fn, retries = API_CONFIG.retries, delay = API_CONFIG.retryDelay) => {
  try {
    return await fn()
  } catch (error) {
    if (retries > 0 && error.status >= 500) {
      await new Promise(resolve => setTimeout(resolve, delay))
      return withRetry(fn, retries - 1, delay * 1.5)
    }
    throw error
  }
}

// Main API object with all endpoints
export const api = {
  auth: authService,
  industries: industriesService,
  jobs: jobsService,
  users: usersService,
  error: errorLogService,
  // applications: applicationsService,
  // profile: profileService,
  // reference: referenceService,
  // dashboard: dashboardService,
  // companies: companiesService,
  // upload: uploadService,
  jobEnums: jobEnumService // New job enums service
}

// Export utilities
export { httpClient, API_CONFIG, ENDPOINTS }

// Default export
export default api