import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, Matches } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateIndustryDto {
  @ApiProperty({
    description: 'Industry name',
    example: 'Technology',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @Transform(({ value }) => value?.trim())
  @Matches(/^[a-zA-Z0-9\s-]+$/, {
    message: 'Industry name can only contain letters, numbers, spaces, and hyphens',
  })
  name: string;

  @ApiProperty({
    description: 'Industry description',
    required: false,
    example: 'Technology sector including software, hardware, and IT services',
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.trim())
  description?: string;
}
