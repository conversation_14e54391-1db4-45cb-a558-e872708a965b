import {
  <PERSON>,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  Get,
  Query,
  Request,
  NotFoundException,
  Put,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { UsersService } from './users.service';
import {
  PhoneSignupDto,
  EmailSignupDto,
  VerifyPhoneOtpDto,
  VerifyEmailOtpDto,
} from './dto/auth.dto';
import { UpdateProfileDto } from './dto/profile.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole, SubscriptionType } from './enums/user.enum';
import { UpdateUserDto } from './dto/update-user.dto';
import { SubscriptionService } from './subscription/subscription.service';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  @Post('signup/phone')
  @ApiOperation({ summary: 'Sign up with phone number' })
  @ApiResponse({
    status: 200,
    description: 'Verification code sent successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Verification code sent successfully. Please check your phone.',
        },
      },
    },
  })
  @ApiResponse({ status: 409, description: 'Phone number already registered' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async initiatePhoneSignup(@Body() phoneSignupDto: PhoneSignupDto) {
    return this.usersService.initiatePhoneSignup(phoneSignupDto);
  }

  @Post('resend/phone')
  @ApiOperation({ summary: 'Resend phone verification code' })
  @ApiResponse({
    status: 200,
    description: 'Verification code resent successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Verification code resent successfully. Please check your phone.',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Phone number already verified' })
  async resendPhoneVerification(@Body('phone') phone: string) {
    return this.usersService.resendPhoneVerification(phone);
  }

  @Post('signup/email')
  @ApiOperation({ summary: 'Sign up with email' })
  @ApiResponse({
    status: 200,
    description: 'OTP sent successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'OTP sent successfully. Please check your email.',
        },
      },
    },
  })
  @ApiResponse({ status: 409, description: 'Email already registered' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async initiateEmailSignup(@Body() emailSignupDto: EmailSignupDto) {
    return this.usersService.initiateEmailSignup(emailSignupDto);
  }

  @Post('verify/phone')
  @ApiOperation({ summary: 'Verify phone verification code' })
  @ApiResponse({
    status: 200,
    description: 'Phone number verified successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Phone number verified successfully',
        },
        token: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid verification code' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async verifyPhoneOtp(@Body() verifyDto: VerifyPhoneOtpDto) {
    return this.usersService.verifyPhoneOtp(verifyDto);
  }

  @Post('verify/email')
  @ApiOperation({ summary: 'Verify email OTP' })
  @ApiResponse({
    status: 200,
    description: 'Email verified successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Email verified successfully',
        },
        token: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid OTP or expired OTP' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async verifyEmailOtp(@Body() verifyDto: VerifyEmailOtpDto) {
    return this.usersService.verifyEmailOtp(verifyDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('profile')
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Profile updated successfully',
        },
        profile: {
          type: 'object',
          properties: {
            addressLandmark: { type: 'string', example: 'Near City Mall' },
            addressCity: { type: 'string', example: 'Mumbai' },
            state: { type: 'string', example: 'Maharashtra' },
            country: { type: 'string', example: 'India' },
            profileImageSrc: { type: 'string', example: 'https://example.com/profile.jpg' },
          },
        },
        isProfileComplete: { type: 'boolean', example: true },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async updateProfile(@Body() updateProfileDto: UpdateProfileDto, @Param('userId') userId: string) {
    return this.usersService.updateProfile(userId, updateProfileDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('verify/aadhar')
  @ApiOperation({ summary: 'Verify Aadhar number' })
  @ApiResponse({
    status: 200,
    description: 'Aadhar verification successful',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Aadhar verification successful',
        },
        isAadharVerified: { type: 'boolean', example: true },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Invalid Aadhar number' })
  async verifyAadhar(@Body('aadharNumber') aadharNumber: string, @Param('userId') userId: string) {
    return this.usersService.verifyAadhar(userId, aadharNumber);
  }

  @Get('me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({
    status: 200,
    description: 'Current user details',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        phone: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        role: { type: 'string', enum: Object.values(UserRole) },
        subscriptionType: { type: 'string', enum: Object.values(SubscriptionType) },
        isProfileComplete: { type: 'boolean' },
        profile: {
          type: 'object',
          properties: {
            addressLandmark: { type: 'string' },
            addressCity: { type: 'string' },
            state: { type: 'string' },
            country: { type: 'string' },
            pinCode: { type: 'string' },
            profileImageSrc: { type: 'string' },
          },
        },
      },
    },
  })
  async getCurrentUser(@Request() req) {
    return this.usersService.getUser(req.user.id);
  }

  @Get()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all users (Admin only)' })
  @ApiQuery({ name: 'role', enum: UserRole, required: false })
  @ApiQuery({ name: 'page', type: Number, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  async getUsers(
    @Query('role') role?: UserRole,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ) {
    return this.usersService.getUsers(role, page, limit);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get user by ID (Admin only)' })
  async getUser(@Param('id') id: string) {
    return this.usersService.getUser(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update user (Admin only)' })
  async updateUser(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @Put('subscription/upgrade')
  @ApiOperation({ summary: 'Upgrade user subscription' })
  @ApiResponse({
    status: 200,
    description: 'Subscription upgraded successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        subscriptionType: { type: 'string', enum: Object.values(SubscriptionType) },
      },
    },
  })
  async upgradeSubscription(
    @Request() req,
    @Body('plan') plan: string,
    @Body('amount') amount: number,
  ) {
    const subscription = await this.subscriptionService.findByUserId(req.user.id);
    if (!subscription) {
      throw new NotFoundException('No subscription found for user');
    }
    return this.subscriptionService.upgradeSubscription(subscription.id, plan, amount, req.user.id);
  }

  @Get('subscription/features')
  @ApiOperation({ summary: 'Get subscription features' })
  @ApiResponse({
    status: 200,
    description: 'Subscription features',
    schema: {
      type: 'object',
      properties: {
        maxJobApplications: { type: 'number' },
        maxJobPostings: { type: 'number' },
        canViewContactInfo: { type: 'boolean' },
        canMessageEmployers: { type: 'boolean' },
        prioritySupport: { type: 'boolean' },
      },
    },
  })
  async getSubscriptionFeatures(@Request() req) {
    return this.subscriptionService.getSubscriptionFeatures(req.user.subscriptionType);
  }
}
