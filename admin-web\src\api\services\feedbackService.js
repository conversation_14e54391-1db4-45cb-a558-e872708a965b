import httpClient from '../httpClient';
import { ENDPOINTS } from '../endpoints';

export const feedbackService = {
  async getFeedbackList({ page = 1, limit = 20, ...filters } = {}) {
    const url = ENDPOINTS.FEEDBACK.LIST(page, limit, filters);
    const response = await httpClient.get(url);
    return response;
  },

  async createFeedback(payload) {
    const response = await httpClient.post(ENDPOINTS.FEEDBACK.CREATE, payload);
    return response;
  },

  async getFeedbackById(id) {
    const response = await httpClient.get(ENDPOINTS.FEEDBACK.DETAIL(id));
    return response;
  },

  async updateFeedback(id, payload) {
    const response = await httpClient.patch(ENDPOINTS.FEEDBACK.UPDATE(id), payload);
    return response;
  },

  async deleteFeedback(id) {
    const response = await httpClient.delete(ENDPOINTS.FEEDBACK.DELETE(id));
    return response;
  },
}; 