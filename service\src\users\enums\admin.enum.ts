export enum AdminActionType {
  USER_LOGIN = 'USER_LOGIN',
  USER_LOGOUT = 'USER_LOGOUT',
  PROFILE_UPDATE = 'PROFILE_UPDATE',
  SUBSCRIPTION_CHANGE = 'SUBSCRIPTION_CHANGE',
  VERIFICATION_COMPLETE = 'VERIFICATION_COMPLETE',
  USER_BLOCKED = 'USER_BLOCKED',
  USER_UNBLOCKED = 'USER_UNBLOCKED',
  TESTIMONIAL_APPROVED = 'TESTIMONIAL_APPROVED',
  TESTIMONIAL_REJECTED = 'TESTIMONIAL_REJECTED',
  USER_CREATE = 'USER_CREATE',
}
