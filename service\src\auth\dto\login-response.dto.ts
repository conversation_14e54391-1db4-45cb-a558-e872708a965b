import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../../users/enums/user.enum';
import { ProfileEntity } from '../../users/entities/profile.entity';
import { CompanyEntity } from '../../companies/entities/company.entity';

class UserResponseDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'User email' })
  email: string;

  @ApiProperty({ description: 'User phone number', required: false })
  phone?: string;

  @ApiProperty({ description: 'User role', enum: UserRole })
  role: UserRole;

  @ApiProperty({ description: 'Is user blocked' })
  isBlocked: boolean;

  @ApiProperty({ description: 'Is user profile complete' })
  isProfileComplete: boolean;

  @ApiProperty({ description: 'User profile', type: () => ProfileEntity })
  profile: ProfileEntity;

  @ApiProperty({ description: 'User first name' })
  firstName: string;

  @ApiProperty({ description: 'User last name' })
  lastName: string;

  @ApiProperty({ description: 'Is email verified' })
  isEmailVerified: boolean;

  @ApiProperty({ description: 'Is phone verified' })
  isPhoneVerified: boolean;

  @ApiProperty({ description: 'Is Aadhar verified' })
  isAadharVerified: boolean;

  @ApiProperty({ description: 'User company', type: () => CompanyEntity, required: false })
  company?: CompanyEntity;
}

export class LoginResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  access_token: string;

  @ApiProperty({ description: 'JWT refresh token' })
  refresh_token: string;

  @ApiProperty({ description: 'User information', type: () => UserResponseDto })
  user: UserResponseDto;
}
