import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IndustryEntity } from './entities/industry.entity';
import { SubIndustryEntity } from './entities/sub-industry.entity';
import { IndustryService } from './services/industry.service';
import { SubIndustryService } from './services/sub-industry.service';
import { IndustryController } from './controllers/industry.controller';
import { SubIndustryController } from './controllers/sub-industry.controller';

@Module({
  imports: [TypeOrmModule.forFeature([IndustryEntity, SubIndustryEntity])],
  controllers: [IndustryController, SubIndustryController],
  providers: [IndustryService, SubIndustryService],
  exports: [IndustryService, SubIndustryService],
})
export class IndustriesModule {}
