import '@/assets/style.css'

import { createApp } from 'vue'
// import { createPinia } from 'pinia'

import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'
import 'primeicons/primeicons.css'
import Tooltip from 'primevue/tooltip'
import ToastService from 'primevue/toastservice';
import i18n from './i18n'

import VueGtag from 'vue-gtag-next';
import App from './App.vue'
import router from './router'

const app = createApp(App)

// app.use(createPinia())

app.use(VueGtag, {
  config: { id: 'G-LK50T133FR' },
  router // Pass the router instance for automatic page tracking
})
app.use(router)
app.use(i18n)
app.use(ToastService);
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      prefix: 'p',
      darkModeSelector: '.dark-theme',
      cssLayer: false
    }
  },
  ripple: true
})

// Register global directives
app.directive('tooltip', Tooltip)

app.mount('#app')

