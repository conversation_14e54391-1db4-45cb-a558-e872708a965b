<template>
    <div :class="['filters-sidebar', 'desktop-only', { collapsed: filtersCollapsed }]">
        <div class="filters-header">
          <h3 v-if="!filtersCollapsed">
            <i class="pi pi-filter"></i>
            Filters
          </h3>
          <Button 
            :icon="filtersCollapsed ? 'pi pi-angle-right' : 'pi pi-angle-left'"
            text
            rounded
            size="small"
            @click="toggleFiltersCollapse"
            v-tooltip="filtersCollapsed ? 'Expand Filters' : 'Collapse Filters'"
            class="collapse-btn"
          />
        </div>

        <div v-if="!filtersCollapsed" class="filters-content">
          <!-- Search -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-search"></i>
              Search
            </label>
            <InputText 
              v-model="searchTerm" 
              placeholder="Search jobs..." 
              class="w-full"
            />
          </div>

          <!-- Industry Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-building"></i>
              Industry
            </label>
            <Select
              v-model="selectedIndustryFilter"
              :options="industryFilterOptions"
              option-label="label"
              option-value="value"
              placeholder="Select industry"
              :showClear="true"
              class="w-full"
            />
          </div>

          <!-- Job Type Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-briefcase"></i>
              Job Type
            </label>
            <Select
              v-model="selectedJobTypeFilter"
              :options="jobTypeOptions"
              option-label="label"
              option-value="value"
              placeholder="Select job type"
              :showClear="true"
              class="w-full"
            />
          </div>

          <!-- Urgency Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-clock"></i>
              Urgency
            </label>
            <Select
              v-model="selectedUrgencyFilter"
              :options="urgencyOptions"
              option-label="label"
              option-value="value"
              placeholder="Select urgency"
              :showClear="true"
              class="w-full"
            />
          </div>

          <!-- Statistics -->
          <div class="filter-stats">
            <h4>Statistics</h4>
            <div class="stats-list">
              <div class="stat-item">
                <span class="stat-label">Total Jobs</span>
                <span class="stat-value">{{ jobStats.total }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Active</span>
                <span class="stat-value success">{{ jobStats.active }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Full Time</span>
                <span class="stat-value">{{ jobStats.byType.FULL_TIME || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Urgent</span>
                <span class="stat-value warning">{{ jobStats.byUrgency.URGENT || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- Clear Filters -->
          <Button 
            label="Clear All Filters"
            icon="pi pi-filter-slash" 
            severity="secondary"
            @click="clearFilters"
            :disabled="!searchTerm && !selectedIndustryFilter && !selectedJobTypeFilter && !selectedUrgencyFilter"
            class="w-full"
          />
        </div>
      </div>
</template>