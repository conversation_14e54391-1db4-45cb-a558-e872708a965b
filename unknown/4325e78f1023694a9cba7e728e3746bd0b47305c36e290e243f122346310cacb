<script setup>
import { defineEmits, onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Drawer from 'primevue/drawer';
import Avatar from 'primevue/avatar';
import Button from 'primevue/button';
import Select from 'primevue/select'
import Divider from 'primevue/divider';

import { useAuthStore } from '@/stores/auth'
import { changeLanguage, getCurrentLanguage, getAvailableLanguages } from '@/i18n'

const props = defineProps({
    visible: Boolean
})

const router = useRouter()
const route = useRoute()
const { t, locale } = useI18n()
const authStore = useAuthStore();
const isDark = ref(false)
const selectedLanguage = ref('')
const languages = ref(getAvailableLanguages())

console.log(authStore.user)

const emit = defineEmits(['update:visible'])

function handleHide(val) {
    emit('update:visible', val)
}

const languageFlags = {
    en: '🇮🇳',
    hi: '🇮🇳'
}

const getFlag = (code) => {
    return languageFlags[code] || '🌐'
}

const getLanguageName = (code) => {
    const lang = languages.value.find(l => l.code === code)
    return lang ? lang.nativeName : code
}

const getUserInitials = () => {
    const user = authStore.user
    if (!user) return 'U'

    if (user.profile?.firstName || user.profile?.lastName) {
        const firstName = user.profile.firstName || ''
        const lastName = user.profile.lastName || ''
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || 'U'
    }

    if (user.email) {
        return user.email.charAt(0).toUpperCase()
    }

    return 'U'
}

const getUserName = () => {
    const user = authStore.user
    if (!user) return t('profile.jobSeeker')

    if (user.profile?.firstName || user.profile?.lastName) {
        const firstName = user.profile.firstName || ''
        const lastName = user.profile.lastName || ''
        return `${firstName} ${lastName}`.trim() || t('profile.jobSeeker')
    }

    if (user.email) {
        return user.email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    return t('profile.jobSeeker')
}

const onProfileClick = () => {
    handleHide(false)
    router.push('/jobseeker/profile')
}

const onLogoutClick = async () => {
    await authStore.logout()
    handleHide(false)
    router.push('/jobseeker')
}

const toggleTheme = () => {
    isDark.value = !isDark.value
    updateTheme()
}

const updateTheme = () => {
    const html = document.documentElement

    if (isDark.value) {
        html.classList.add('dark-theme')
        localStorage.setItem('theme', 'dark')
    } else {
        html.classList.remove('dark-theme')
        localStorage.setItem('theme', 'light')
    }
}

const initializeTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    isDark.value = savedTheme === 'dark' || (!savedTheme && prefersDark)
    updateTheme()
}

const handleLanguageChange = (event) => {
    const newLanguage = event.value
    changeLanguage(newLanguage)
    selectedLanguage.value = newLanguage
}

onMounted(() => {
    // Initialize theme
    initializeTheme()

    // Initialize language
    selectedLanguage.value = getCurrentLanguage()
})
</script>

<template>
    <Drawer :visible="visible" @update:visible="handleHide" position="right">
        <template #header>
            <div class="flex items-center gap-2">
                <Avatar :label="getUserInitials()" shape="circle" />
                <span class="font-bold">{{ getUserName() }}</span>
            </div>
        </template>
        <Divider />
        <div class="flex flex-col">
            <!-- <h5 class="nav-section-title">{{ t('navigation.preferences') }}</h5> -->
            
            <div class="flex py-3">
                <label class="flex-1">Profile</label>
                <Button @click="onProfileClick" icon="pi pi-user" text size="small" class="theme-toggle-btn" />
            </div>

            <div class="flex pb-3">
                <label class="flex-1">Theme</label>
                <Button @click="toggleTheme" :icon="isDark ? 'pi pi-sun' : 'pi pi-moon'" text size="small"
                    :aria-label="isDark ? t('common.switchToLight') : t('common.switchToDark')" class="theme-toggle-btn" />
            </div>
            
            <div class="flex">
                <label class="flex-1">Language</label>
            <Select v-model="selectedLanguage" @change="handleLanguageChange" :options="languages"
                optionLabel="nativeName" optionValue="code" class="language-dropdown"
                :placeholder="t('common.language')">
                <template #value="slotProps">
                    <div v-if="slotProps.value" class="language-option">
                        <!-- <span class="language-flag">{{ getFlag(slotProps.value) }}</span> -->
                        <span class="language-name">{{ getLanguageName(slotProps.value) }}</span>
                    </div>
                </template>
                <template #option="slotProps">
                    <div class="language-option">
                        <!-- <span class="language-flag">{{ getFlag(slotProps.option.code) }}</span> -->
                        <span class="language-name">{{ slotProps.option.nativeName }}</span>
                    </div>
                </template>
            </Select>
            </div>
        </div>
        
        <template #footer>
            <Divider />
            <div class="flex items-center gap-2">
                <Button label="Logout" icon="pi pi-sign-out" class="flex-auto" severity="danger" text
                    @click="onLogoutClick"></Button>
            </div>
        </template>
    </Drawer>
</template>
