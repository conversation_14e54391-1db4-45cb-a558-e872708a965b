<script setup>
import { onMounted, ref, watch } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const mapContainer = ref()
let map = null
let currentLocationMarker = null
let routeLayer = null
let customMarkers = null

const formData = ref({
  address: '',
  city: '',
  state: '',
  country: '',
  postalCode: '',
  latitude: null,
  longitude: null
})

// Watch for prop changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && !isEqual(formData.value, newValue)) {
      formData.value = { ...formData.value, ...newValue }
    }
  },
  { immediate: true, deep: true }
)

// Watch for form data changes
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// Helper function for shallow comparison
function isEqual(a, b) {
  return JSON.stringify(a) === JSON.stringify(b)
}

const locationError = ref(null)
const isGettingLocation = ref(false)

const getLocationDetails = async (lat, lng) => {
  const response = await fetch(
    `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
  )
  const data = await response.json()
  return {
    country: data.address?.country,
    address: data.display_name || '',
    postalCode: data.address?.postcode,
    state: data.address?.state || data.address?.region || '',
    city: data.address?.city || data.address?.town || data.address?.village || '',
    area: data.address?.suburb || data.address?.neighbourhood || data.address?.quarter || data.address?.hamlet || ''
  }
}

const getCurrentLocation = async () => {
  if (!navigator.geolocation) {
    locationError.value = 'Geolocation not supported'
    return
  }

  isGettingLocation.value = true
  locationError.value = null

  navigator.geolocation.getCurrentPosition(
    async (position) => {
      const lat = position.coords.latitude
      const lng = position.coords.longitude

      if (map) {
        if (currentLocationMarker) map.removeLayer(currentLocationMarker)

        currentLocationMarker = L.marker([lat, lng], {
          icon: createCurrentLocationIcon(),
        }).addTo(map)

        currentLocationMarker.bindPopup(
          `<b>📍 Your Location</b><br>Lat: ${lat.toFixed(6)}<br>Lng: ${lng.toFixed(6)}`,
        )

        map.setView([lat, lng], 15)
      }

      const locationDetails = await getLocationDetails(lat, lng)
      
      formData.value = {
        ...formData.value,
        ...locationDetails,
        latitude: lat,
        longitude: lng
      }

      isGettingLocation.value = false
    },
    (error) => {
      isGettingLocation.value = false
      locationError.value = error.message || 'Error retrieving location'
    },
  )
}

const createCurrentLocationIcon = () => L.divIcon({
  html: `
    <div style="
      width: 20px;
      height: 20px;
      background: #ff4444;
      border: 3px solid white;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      position: relative;
    ">
      <div style="
        position: absolute;
        top: -5px;
        left: -5px;
        width: 30px;
        height: 30px;
        background: rgba(255, 68, 68, 0.2);
        border-radius: 50%;
        animation: pulse 2s infinite;
      "></div>
    </div>
    <style>
      @keyframes pulse {
        0% { transform: scale(0.8); opacity: 1; }
        100% { transform: scale(2); opacity: 0; }
      }
    </style>
  `,
  className: 'current-location-marker',
  iconSize: [20, 20],
  iconAnchor: [10, 10],
})

onMounted(() => {
  if (!mapContainer.value) return

  map = L.map(mapContainer.value).setView([20.5937, 78.9629], 5) // Center on India
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
  }).addTo(map)

  routeLayer = L.layerGroup().addTo(map)
  customMarkers = L.layerGroup().addTo(map)
})
</script>

<template>
  <div class="location-component">
    <div ref="mapContainer" class="map-container"></div>
    
    <div class="location-controls">
      <div class="location-actions">
        <Button 
          icon="pi pi-map-marker" 
          @click="getCurrentLocation"
          :loading="isGettingLocation"
          :disabled="isGettingLocation"
          class="locate-me"
        >
          Get My Current Location
        </Button>
      </div>
    </div>

    <div v-if="locationError" class="location-error">
      {{ locationError }}
    </div>

    <div class="form-grid">
      <div class="form-group">
        <label for="address">Address Line 1</label>
        <InputText
          id="address"
          v-model="formData.address"
          placeholder="Enter your address"
        />
      </div>
      <div class="form-group">
        <label for="city">City</label>
        <InputText
          id="city"
          v-model="formData.city"
          placeholder="Enter your city"
        />
        <small v-if="props.errors.city" class="p-error">{{ props.errors.city }}</small>
      </div>
      <div class="form-group">
        <label for="state">State</label>
        <InputText
          id="state"
          v-model="formData.state"
          placeholder="Enter your state"
        />
        <small v-if="props.errors.state" class="p-error">{{ props.errors.state }}</small>
      </div>
      <div class="form-group">
        <label for="postalCode">PIN</label>
        <InputText
          id="postalCode"
          v-model="formData.postalCode"
          placeholder="Enter your PIN code"
        />
        <small v-if="props.errors.postalCode" class="p-error">{{ props.errors.postalCode }}</small>
      </div>
      <div class="form-group">
        <label for="country">Country</label>
        <InputText
          id="country"
          v-model="formData.country"
          placeholder="India"
        />
        <small v-if="props.errors.country" class="p-error">{{ props.errors.country }}</small>
      </div>
    </div>
  </div>
</template>

<style scoped>
.location-component {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  height: 100%;
  margin: 1rem auto;
}

.locate-me {
  width: 100%;
}

.map-container {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.location-error {
  color: #ef4444;
  padding: 0.5rem;
  background: #fee2e2;
  border-radius: 4px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-dropdown,
.form-group .p-inputnumber,
.form-group .p-inputtextarea {
  width: 100% !important;
}

:deep(.current-location-marker) {
  z-index: 1000;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}

.p-error {
  color: #e53935 !important;
  font-size: 0.85rem;
  margin-top: 2px;
}
</style> 