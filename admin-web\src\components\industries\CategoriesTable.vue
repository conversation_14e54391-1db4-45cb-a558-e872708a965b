<script setup>
import { defineProps, defineEmits } from 'vue'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Tag from 'primevue/tag'

defineProps({
  categories: Array,
  industryOptions: Array
})
const emit = defineEmits(['add-category', 'edit-category', 'toggle-category'])
</script>
<template>
  <div class="tab-content">
    <div class="section-header">
      <h3>All Categories</h3>
      <Button v-tooltip="'Add Category'" icon="pi pi-plus" size="small" @click="$emit('add-category')" raised rounded />
    </div>
    <div class="table-container">
      <DataTable :value="categories" class="categories-table" :paginator="true" :rows="10" stripedRows :rowHover="true" :scrollable="true" scroll-height="300px">
        <Column field="name" header="Category Name" sortable>
          <template #body="slotProps">
            <div class="category-name">
              <i class="pi pi-tag category-icon"></i>
              <span>{{ slotProps.data.name }}</span>
            </div>
          </template>
        </Column>
        <Column field="details" header="Details" sortable>
          <template #body="slotProps">
            <span class="category-details">{{ slotProps.data.description }}</span>
          </template>
        </Column>
        <Column field="industryName" header="Industry" sortable>
          <template #body="slotProps">
            <span class="industry-badge">{{ slotProps.data.industryName }}</span>
          </template>
        </Column>
        <Column field="isActive" header="Is Active" sortable>
          <template #body="slotProps">
            <Tag :value="slotProps.data.isActive ? 'Active' : 'Inactive'" :severity="slotProps.data.isActive ? 'success' : 'danger'" />
          </template>
        </Column>
        <Column header="Actions">
          <template #body="slotProps">
            <div class="action-buttons">
              <Button icon="pi pi-pencil" text rounded size="small" @click="$emit('edit-category', slotProps.data)" v-tooltip.left="'Edit Category'" />
              <Button icon="pi pi-eye-slash" text rounded size="small" :class="{active: slotProps.data.isActive}" @click="$emit('toggle-category', slotProps.data)" v-tooltip.left="slotProps.data.isActive ? 'De-activate' : 'Activate'" />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template>
<style scoped>
</style> 