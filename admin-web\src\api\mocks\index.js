// Mock API index - imports all mock endpoints
import { sendOtpMock } from './sendOtpMock'
import { verifyOtpMock } from './verifyOtpMock'
import { dashboardMock } from './dashboardMock'
import { jobEnumMock } from './jobEnumMock'
import { industriesMock } from './industriesMock'
import { jobsListMock } from './jobsListMock'

// Export all mock APIs
export {
  sendOtpMock,
  verifyOtpMock,
  dashboardMock,
  jobEnumMock,
  industriesMock,
  jobsListMock
}

// Combined mock API object for easy access
export const mockApis = {
  auth: {
    sendOtp: sendOtpMock.sendOtp,
    verifyOtp: verifyOtpMock.verifyOtp
  },
  dashboard: {
    getStats: dashboardMock.getStats,
    getRecentActivity: dashboardMock.getRecentActivity,
    getRecommendations: dashboardMock.getRecommendations
  },
  jobEnums: {
    getJobEnums: jobEnumMock.getJobEnums
  }
}