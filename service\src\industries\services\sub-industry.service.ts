import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubIndustryEntity } from '../entities/sub-industry.entity';
import { IndustryEntity } from '../entities/industry.entity';
import { CreateSubIndustryDto } from '../dto/create-sub-industry.dto';
import { UpdateSubIndustryDto } from '../dto/update-sub-industry.dto';

@Injectable()
export class SubIndustryService {
  constructor(
    @InjectRepository(SubIndustryEntity)
    private readonly subIndustryRepository: Repository<SubIndustryEntity>,
    @InjectRepository(IndustryEntity)
    private readonly industryRepository: Repository<IndustryEntity>,
  ) {}

  async create(createSubIndustryDto: CreateSubIndustryDto): Promise<SubIndustryEntity> {
    // Check for duplicate name within the same industry
    const existingSubIndustry = await this.subIndustryRepository
      .createQueryBuilder('subIndustry')
      .where('LOWER(subIndustry.name) = LOWER(:name)', { name: createSubIndustryDto.name })
      .andWhere('subIndustry.industryId = :industryId', {
        industryId: createSubIndustryDto.industryId,
      })
      .getOne();

    if (existingSubIndustry) {
      throw new BadRequestException(
        `Sub-industry with name "${createSubIndustryDto.name}" already exists in this industry`,
      );
    }

    // Verify parent industry exists
    const parentIndustry = await this.industryRepository.findOne({
      where: { id: createSubIndustryDto.industryId, isActive: true },
    });

    if (!parentIndustry) {
      throw new NotFoundException('Parent industry not found');
    }

    const subIndustry = this.subIndustryRepository.create(createSubIndustryDto);
    return this.subIndustryRepository.save(subIndustry);
  }

  async findAll(): Promise<SubIndustryEntity[]> {
    return this.subIndustryRepository.find({
      where: { isActive: true },
      relations: ['industry'],
      order: { name: 'ASC' },
    });
  }

  async findByIndustry(industryId: string): Promise<SubIndustryEntity[]> {
    return this.subIndustryRepository.find({
      where: { industryId, isActive: true },
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<SubIndustryEntity> {
    const subIndustry = await this.subIndustryRepository.findOne({
      where: { id, isActive: true },
      relations: ['industry'],
    });

    if (!subIndustry) {
      throw new NotFoundException('Sub-industry not found');
    }

    return subIndustry;
  }

  async update(id: string, updateSubIndustryDto: UpdateSubIndustryDto): Promise<SubIndustryEntity> {
    const subIndustry = await this.findOne(id);

    if (updateSubIndustryDto.name && updateSubIndustryDto.name !== subIndustry.name) {
      const existingSubIndustry = await this.subIndustryRepository
        .createQueryBuilder('subIndustry')
        .where('LOWER(subIndustry.name) = LOWER(:name)', { name: updateSubIndustryDto.name })
        .andWhere('subIndustry.industryId = :industryId', {
          industryId: updateSubIndustryDto.industryId || subIndustry.industryId,
        })
        .andWhere('subIndustry.id != :id', { id })
        .getOne();

      if (existingSubIndustry) {
        throw new BadRequestException(
          `Sub-industry with name "${updateSubIndustryDto.name}" already exists in this industry`,
        );
      }
    }

    if (
      updateSubIndustryDto.industryId &&
      updateSubIndustryDto.industryId !== subIndustry.industryId
    ) {
      const parentIndustry = await this.industryRepository.findOne({
        where: { id: updateSubIndustryDto.industryId, isActive: true },
      });

      if (!parentIndustry) {
        throw new NotFoundException('Parent industry not found');
      }
    }

    Object.assign(subIndustry, updateSubIndustryDto);
    return this.subIndustryRepository.save(subIndustry);
  }

  async remove(id: string): Promise<void> {
    const subIndustry = await this.findOne(id);

    // Check if there are any active jobs associated with this sub-industry
    const hasActiveJobs = await this.subIndustryRepository
      .createQueryBuilder('subIndustry')
      .leftJoin('subIndustry.jobs', 'job')
      .where('subIndustry.id = :id', { id })
      .andWhere('job.isActive = :isActive', { isActive: true })
      .getCount();

    if (hasActiveJobs > 0) {
      throw new BadRequestException(
        'Cannot delete sub-industry because it has active jobs associated with it',
      );
    }

    // Perform soft delete
    subIndustry.isActive = false;
    await this.subIndustryRepository.save(subIndustry);
  }
}
