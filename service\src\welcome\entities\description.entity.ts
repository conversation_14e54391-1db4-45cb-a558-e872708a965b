import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { WelcomeEntity } from './welcome.entity';

@Entity('descriptions')
export class DescriptionEntity extends BaseEntity {
  @Column('text')
  description: string;

  @ManyToOne(() => WelcomeEntity, (welcome) => welcome.descriptions, { onDelete: 'CASCADE' })
  welcome: WelcomeEntity;
}
