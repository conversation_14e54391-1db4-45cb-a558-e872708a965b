import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { WelcomeEntity } from './welcome.entity';

@Entity('audience_messages')
export class AudienceMessageEntity extends BaseEntity {
  @Column('text')
  message: string;

  @ManyToOne(() => WelcomeEntity, (welcome) => welcome.audience_messages, { onDelete: 'CASCADE' })
  welcome: WelcomeEntity;
}
