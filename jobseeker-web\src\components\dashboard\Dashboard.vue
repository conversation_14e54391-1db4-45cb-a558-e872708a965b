<template>
  <div class="dashboard-container flex-1 overflow-x-hidden overflow-y-auto">
    <div class="dashboard-content">

      <!-- Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card primary" @click="router.push('/jobseeker/jobs')">
          <div class="stat-icon"><i class="pi pi-briefcase"></i></div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.totalJobs || 0 }}</h3>
            <p>{{ t('dashboard.availableJobs') }}</p>
            <!-- <Button class="go-to-view-btn" icon="pi pi-arrow-right" severity="secondary" @click.prevent="goToJobsPage"></Button> -->
          </div>
        </div>
        <div class="stat-card success" @click.prevent="router.push('/jobseeker/applications')">
          <div class="stat-icon"><i class="pi pi-send"></i></div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.totalApplications || 0 }}</h3>
            <p>{{ t('dashboard.applicationsSent') }}</p>
            <!-- <Button class="go-to-view-btn" icon="pi pi-arrow-right" severity="secondary" @click.prevent="router.push('/jobseeker/applications')"></Button> -->
          </div>
        </div>
        <div class="stat-card info" @click.prevent="router.push('/jobseeker/jobs/saved')">
          <div class="stat-icon"><i class="pi pi-heart"></i></div>
          <div class="stat-content">
            <h3>{{ dashboardStore.stats.savedJobs || 0 }}</h3>
            <p>{{ t('dashboard.savedJobs') }}</p>
            <!-- <Button class="go-to-view-btn" icon="pi pi-arrow-right" severity="secondary" @click.prevent="router.push('/jobseeker/jobs/saved')"></Button> -->
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Recommended for You as Table -->
        <div class="recommended-card">
          <div class="card-header">
            <i class="pi pi-star card-icon"></i>
            <h3>Recommended for You</h3>
            <a class="view-all-link" @click.prevent="goToJobsPage">View All</a>
          </div>
          <div class="jobs-list">
            <DataView :value="recommendedJobs" layout="list">
              <template #list="slotProps">
                <div v-for="(item, index) in slotProps.items" :key="index" class="mb-4">
                  <Card class="shadow-md border border-gray-200 rounded-lg">
                    <template #title>
                      <div class="text-xl font-semibold text-primary-800">
                        {{ item.title }}
                      </div>
                    </template>

                    <template #content>
                      <div class="flex flex-col sm:flex-row sm:justify-between gap-4 text-sm text-gray-700 mt-2">

                        <!-- Location -->
                        <div class="flex items-center gap-2">
                          <i class="pi pi-map-marker text-green-500"></i>
                          <span>{{ item.location }}</span>
                        </div>

                        <!-- Salary -->
                        <div class="flex items-center gap-2">
                          <i class="pi pi-money-bill text-green-500"></i>
                          <span class="font-medium">{{ item.salary }}</span>
                          <span class="text-xs text-gray-500">/ {{ item.paymentType }}</span>
                        </div>
                      </div>

                      <!-- Apply Button -->
                      <div class="mt-4 text-right">
                        <Button label="Apply Now" size="large" class="p-button-sm p-button-primary"
                          @click.stop="applyToJob(item)" v-tooltip.top="t('dashboard.applyNow')" />
                      </div>
                    </template>
                  </Card>
                </div>
              </template>
            </DataView>
          </div>
        </div>
        <!-- Recent Activity -->
        <div class="activity-card">
          <div class="card-header">
            <i class="pi pi-clock card-icon"></i>
            <h3>{{ t('dashboard.recentActivity') }}</h3>
            <span class="last-updated" v-if="dashboardStore.lastUpdated">
              {{ t('dashboard.updated', { time: dashboardStore.getStatsAge() }) }}
            </span>
          </div>
          <div class="activity-list">
            <DataView :value="recentActivity" layout="grid" :paginator="false" :rows="6" class="recent-activity-view">
              <template #grid="slotProps">
                <div v-for="(item, index) in slotProps.items" :key="index" class="mb-4">
                  <Card class="shadow-md border border-gray-200 rounded-lg">
                    <template #title>
                      <div class="flex justify-between items-start">
                        <span class="text-lg font-medium text-gray-800">
                          {{ item.title }}
                        </span>
                        <span class="text-sm text-gray-500">
                          {{ formatDate(item.updatedAt || item.createdAt) }}
                        </span>
                      </div>
                    </template>

                    <template #content>
                      <div class="grid gap-2 text-sm text-gray-700">
                        <!-- <div v-if="item.salary_type" class="flex items-center gap-2 text-primary-700">
              <i class="pi pi-wallet"></i>
              {{ item.salary_type }}
            </div> -->

                        <!-- <div v-if="item.location" class="flex items-center gap-2 text-primary-700">
              <i class="pi pi-map-marker"></i>
              {{ item.location }}
            </div> -->
                        <div class="flex gap-2">
                          <div v-if="item.scheduledPostTime" class="flex items-center gap-2 text-primary-700">
                            <i class="pi pi-calendar"></i>
                            {{ formatRelativeTime(item.scheduledPostTime) }}
                          </div>

                          <div v-if="item.urgency">
                            <Tag :value="item.urgency" :severity="item.urgency" icon="pi pi-bolt" />
                          </div>

                        </div>


                        <!-- Apply Button -->
                        <div class="text-right py-3">
                          <Button label="View Details" size="large" class="p-button-sm p-button-primary"
                            @click.stop="openJobDetails(item)" v-tooltip.top="t('dashboard.details')" />
                        </div>
                      </div>
                    </template>
                  </Card>
                </div>

              </template>
            </DataView>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'
import { useJobsStore } from '@/stores/jobs'
import Button from 'primevue/button'
import { profileService } from '@/api/services/profileService'
import DataView from 'primevue/dataview';
import Card from 'primevue/card'
import Tag from 'primevue/tag'

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()
const dashboardStore = useDashboardStore()
const jobsStore = useJobsStore()

const userProfile = ref({})

// Computed properties for user display
const userFullName = computed(() => {
  const user = authStore.user
  if (!user) return ''

  // Try to get name from profile first
  if (user.profile?.firstName || user.profile?.lastName) {
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }

  // Fallback to user.name if available
  if (user.name) {
    return user.name
  }

  // Fallback to user.firstName and user.lastName if available
  if (user.firstName || user.lastName) {
    const firstName = user.firstName || ''
    const lastName = user.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }

  return ''
})

// Get data from dashboard store
const recommendedJobs = computed(() => {
  if (!userProfile.value || !jobsStore.jobs.length) return []

  const userSkills = (userProfile.value.skills || []).map(s => s.toLowerCase())
  const userLocation = normalizeCity(userProfile.value.city)
  const userTitle = (userProfile.value.jobTitle || '').toLowerCase()
  const userIndustry = (userProfile.value.industry || '').toLowerCase()

  return jobsStore.jobs.filter(job => {
    // Skills match
    const jobSkills = (job.skills || []).map(s => s.toLowerCase())
    const skillsMatch = userSkills.length && jobSkills.some(skill => userSkills.includes(skill))

    // Location match
    const jobLocation = normalizeCity(job.location)
    const locationMatch = userLocation && jobLocation && jobLocation.includes(userLocation)

    // Title match
    const jobTitle = (job.title || '').toLowerCase()
    let titleMatch = false
    if (userTitle && jobTitle) {
      titleMatch = jobTitle.includes(userTitle) || userTitle.includes(jobTitle)
    }

    // Industry match
    let jobIndustry = ''
    if (typeof job.industry === 'string') {
      jobIndustry = job.industry.toLowerCase()
    } else if (job.industry && job.industry.name) {
      jobIndustry = job.industry.name.toLowerCase()
    }
    let industryMatch = false
    if (userIndustry && jobIndustry) {
      industryMatch = jobIndustry.includes(userIndustry) || userIndustry.includes(jobIndustry)
    }

    // Recommend if any match
    return skillsMatch || locationMatch || titleMatch || industryMatch
  })
})

const recentActivity = computed(() =>
  dashboardStore.stats.recentActivity || []
)

const formatDate = (dateString) => {
  if (!dateString) return 'just now';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());

  const diffSeconds = Math.ceil(diffTime / 1000);
  const diffMinutes = Math.ceil(diffTime / (1000 * 60));
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffSeconds < 60) return 'just now';
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

function formatRelativeTime(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now - date
  const diffSec = Math.floor(diffMs / 1000)
  const diffMin = Math.floor(diffSec / 60)
  const diffHr = Math.floor(diffMin / 60)
  const diffDay = Math.floor(diffHr / 24)

  if (diffSec < 60) return 'just now'
  if (diffMin < 60) return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`
  if (diffHr < 24) return `${diffHr} hour${diffHr > 1 ? 's' : ''} ago`
  if (diffDay === 1) return '1 day ago'
  if (diffDay < 7) return `${diffDay} days ago`
  if (diffDay < 30) return `${Math.floor(diffDay / 7)} week${Math.floor(diffDay / 7) > 1 ? 's' : ''} ago`
  if (diffDay < 365) return `${Math.floor(diffDay / 30)} month${Math.floor(diffDay / 30) > 1 ? 's' : ''} ago`
  return `${Math.floor(diffDay / 365)} year${Math.floor(diffDay / 365) > 1 ? 's' : ''} ago`
}
const applyToJob = (job) => {
  router.push(`/jobseeker/jobs/${job.id}?action=apply`)
}

const goToJobsPage = () => {
  router.push('/jobseeker/jobs')
}

function getRecommendationReason(job) {
  const reasons = [];
  const userSkills = (userProfile.value.skills || []).map(s => s.toLowerCase());
  const jobSkills = (job.skills || []).map(s => s.toLowerCase());
  const matchedSkills = userSkills.filter(skill => jobSkills.includes(skill));
  if (matchedSkills.length) {
    reasons.push(` ${matchedSkills.join(', ')}`);
  }

  // Location
  const userLocation = normalizeCity(userProfile.value.city);
  const jobLocation = normalizeCity(job.location);
  let locationMatch = false;
  if (userLocation && jobLocation) {
    locationMatch = jobLocation.includes(userLocation) || userLocation.includes(jobLocation);
  }
  if (locationMatch) {
    reasons.push(`Matching location: ${job.location}`);
  }

  // Title
  const userTitle = (userProfile.value.jobTitle || '').toLowerCase();
  const jobTitle = (job.title || '').toLowerCase();
  let titleMatch = false;
  if (userTitle && jobTitle) {
    titleMatch = jobTitle.includes(userTitle) || userTitle.includes(jobTitle);
  }
  if (titleMatch) {
    reasons.push(`Matching title: ${job.title}`);
  }

  // Industry
  const userIndustry = (userProfile.value.industry || '').toLowerCase();
  let jobIndustry = '';
  if (typeof job.industry === 'string') {
    jobIndustry = job.industry.toLowerCase();
  } else if (job.industry && job.industry.name) {
    jobIndustry = job.industry.name.toLowerCase();
  }
  let industryMatch = false;
  if (userIndustry && jobIndustry) {
    industryMatch = jobIndustry.includes(userIndustry) || userIndustry.includes(jobIndustry);
  }
  if (industryMatch) {
    reasons.push(`Matching industry: ${job.industry.name || job.industry}`);
  }

  return reasons.length ? reasons.join(' | ') : 'General recommendation';
}

function normalizeCity(city) {
  if (!city) return '';
  const map = {
    bangalore: 'bangalore',
    bengaluru: 'bangalore',
    banglore: 'bangalore',
    // Add more as needed
  };
  const cleaned = city.trim().toLowerCase();
  return map[cleaned] || cleaned;
}

const openJobDetails = (activity) => {
  const jobId = activity.jobId || activity.id
  if (jobId) {
    router.push(`/jobseeker/jobs/${jobId}`)
  }
}

onMounted(async () => {
  dashboardStore.fetchStats()
  dashboardStore.startAutoRefresh()
  try {
    const response = await profileService.getProfile()
    userProfile.value = response.data
    if (!jobsStore.jobs.length) {
      await jobsStore.fetchJobs()
    }
  } catch (e) {
    console.error('Failed to fetch profile', e)
  }
})

onUnmounted(() => {
  dashboardStore.stopAutoRefresh()
})
</script>

<style scoped>
.dashboard-container {
  background: linear-gradient(135deg, var(--dashboard-bg-start) 0%, var(--dashboard-bg-end) 100%);
  transition: background var(--transition-duration) ease;
}

.dashboard-content {
  padding: 1.5rem 1rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}
.go-to-view-btn {
    position: absolute !important;
    right: 1rem;
    bottom: 1rem;
}

.stat-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-duration) ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.stat-card.success::before {
  background: var(--green-500);
}

.stat-card.info::before {
  background: var(--blue-500);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0 0 0.5rem 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.p-card, :deep(.p-dataview-content) {
  background: var(--surface-card);
  /* border: 1px solid var(--surface-border); */
}
.p-card-content span, .p-card-body span, .p-card-body div {
  color: var(--text-color);
}
.recommended-card,
.activity-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 1.5rem;
  min-width: 0;
  min-height: 350px;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.card-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.card-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
  flex: 1;
}

.last-updated {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 350px;
  overflow-y: auto;
}

.jobs-table {
  width: 100%;
  border-collapse: collapse;
}

.jobs-table th,
.jobs-table td {
  padding: 8px;
  text-align: left;
}

.jobs-table th {
  font-weight: 600;
  color: var(--text-color);
  background: var(--surface-100);
}

.jobs-table tr {
  border-bottom: 1px solid var(--surface-border);
}

.jobs-table tr:last-child {
  border-bottom: none;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  max-height: 350px;
  /* or another suitable value */
  overflow-y: auto;
}

.activity-item {
  background: var(--surface-card);
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  padding: 1rem 1.25rem;
  transition: box-shadow 0.2s;
}

.activity-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.07);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.activity-title {
  font-weight: 600;
  font-size: 1.08rem;
  color: var(--text-color);
}

.activity-time {
  font-size: 0.85em;
  color: var(--text-color-secondary);
}

.activity-meta {
  display: flex;
  gap: 1.25rem;
  font-size: 0.97em;
  color: var(--text-color-secondary);
  margin-bottom: 0.25rem;
}

.activity-meta i {
  margin-right: 0.25em;
  color: var(--primary-color);
}

.activity-description-wrapper {
  margin-top: 0.15rem;
}

.clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  color: var(--text-color-secondary);
  font-size: 0.97em;
}

.urgent-text {
  color: #e53935;
  font-weight: bold;
}

.view-all-link {
  float: right;
  margin-left: auto;
  color: var(--primary-color, #1976d2);
  font-size: 0.98em;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s;
}

.view-all-link:hover {
  color: #125ea2;
}

@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .p-card-body div,
  .last-updated,
  .view-all-link,
  .stat-content p {
    font-size: var(--font-size-1);
  }

  .text-right button {
    width: 100%
  }
  .stat-icon {
    width: 38px;
    height: 38px;
  }

  .stat-card{
    padding: 1rem;
  }

  .dashboard-content {
    padding: 0.2rem;
  }

  .stats-overview {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .activity-list, .jobs-list {
    max-height: inherit;
  }

  .recommended-card,
  .activity-card {
    min-height: inherit;
  }
}
</style>