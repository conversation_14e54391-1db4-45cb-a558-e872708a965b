<template>
  <div class="space-y-4 flex-1 overflow-y-auto overflow-x-hidden">
    <Card
      v-for="n in 10"
      :key="n"
      class="w-full shadow-sm rounded-xl border border-gray-200"
    >
      <template #content>
        <div class="flex items-start justify-between gap-2">
          <div class="space-y-2">
            <!-- Title + New badge -->
            <div class="flex items-center gap-2">
              <Skeleton width="12rem" height="1.2rem" class="rounded" />
              <Skeleton width="3rem" height="1rem" class="rounded bg-red-100" />
            </div>

            <!-- Job Title -->
            <Skeleton width="14rem" height="1rem" class="rounded" />

            <!-- Status -->
            <div class="flex gap-2 mt-1">
              <Skeleton width="6rem" height="1rem" class="rounded" />
              <Skeleton width="5rem" height="1rem" class="rounded" />
            </div>
          </div>

          <!-- Icon -->
          <Skeleton shape="circle" size="1.5rem" class="mt-1" />
        </div>
      </template>
    </Card>
  </div>
</template>

<script setup>
import Card from 'primevue/card';
import Skeleton from 'primevue/skeleton';
</script>

<style scoped>
</style>
