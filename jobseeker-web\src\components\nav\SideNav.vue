<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { navOptions } from './NavOptions'
import Button from 'primevue/button';

const route = useRoute()
const router = useRouter()
const visibleItem = ref(null)

const setActiveNav = (path) => {
  const sortedNav = [...navOptions].sort((a, b) => b.to.length - a.to.length)
  const activeNav = sortedNav.find(item => path.startsWith(item.to))
  visibleItem.value = activeNav?.text || null
}

const onNavBtnClick = (item) => {
  visibleItem.value = item.text
  router.push(item.to)
}

onMounted(() => {
  setActiveNav(route.path)
})

// 💡 Watch for route changes (e.g., via <router-link> from other components)
watch(() => route.path, (newPath) => {
  setActiveNav(newPath)
})
</script>
<template>
    <div class="flex flex-col side-nav hello-world">
        <Button
            v-for="(item, index) in navOptions"
            :key="index"
            v-tooltip.left="item.text"
            severity="secondary"
            :icon="`pi pi-${item.icon}`"
            iconPos="top"
            @click="onNavBtnClick(item)"
            class="nav-item p-2"
            :class="{ active: visibleItem === item.text }"
            />
    </div>
</template>

<style scoped>
.side-nav {
    width: 4rem;
    gap: 1rem;
    padding-top: 1rem;
}
:deep(button) {
    width: 100%;
    background-color: transparent;
    border-color: transparent;
    padding: 1rem;
}
:deep(.btn-icon) {
    font-size: 1.4rem;
}
.active {
    background: var(--p-button-link-color);
    color: var(--primary-color-text);
}
</style>