import { createI18n } from 'vue-i18n'
import en from './locales/en.json'
import hi from './locales/hi.json'
import kn from './locales/kn.json'

// Define supported locales
const supportedLocales = ['en', 'hi', 'kn']

// Get saved language from localStorage with validation
const getSavedLocale = () => {
  const savedLocale = localStorage.getItem('locale')
  
  // Validate that the saved locale is supported
  if (savedLocale && supportedLocales.includes(savedLocale)) {
    return savedLocale
  }
  
  // If invalid or not found, default to 'en' and update localStorage
  localStorage.setItem('locale', 'en')
  return 'en'
}

const validatedLocale = getSavedLocale()

const i18n = createI18n({
  legacy: false,
  locale: validatedLocale,
  fallbackLocale: 'en',
  messages: {
    en,
    hi,
    kn
  },
  globalInjection: true // Enable global injection for better reactivity
})

export default i18n

// Helper function to change language
export const changeLanguage = (locale) => {
  // Validate locale before setting
  if (supportedLocales.includes(locale)) {
    i18n.global.locale.value = locale
    localStorage.setItem('locale', locale)
    document.documentElement.setAttribute('lang', locale)
    
    // Force reactivity update
    document.dispatchEvent(new CustomEvent('locale-changed', { detail: locale }))
  }
}

// Helper function to get current language
export const getCurrentLanguage = () => {
  return i18n.global.locale.value
}

// Helper function to get available languages
export const getAvailableLanguages = () => {
  return [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'hi', name: 'Hindi', nativeName: 'हिंदी' },
    { code: 'kn', name: 'Kannada', nativeName: 'ಕನ್ನಡ' }
  ]
}