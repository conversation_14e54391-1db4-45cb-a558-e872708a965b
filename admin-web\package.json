{"name": "vite-vue-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@primeuix/themes": "^1.1.1", "axios": "^1.10.0", "leaflet": "^1.9.4", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.5", "quill": "^2.0.3", "vue": "^3.5.16", "vue-i18n": "^11.1.6", "vue-router": "^4.4.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@vitejs/plugin-vue": "^6.0.0-beta.2", "autoprefixer": "^10.4.21", "tailwindcss": "^4.1.10", "tailwindcss-primeui": "^0.6.1", "vite": "^7.0.0-beta.1"}}