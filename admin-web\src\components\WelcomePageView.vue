<script setup>
import { ref, reactive, computed } from 'vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Dialog from 'primevue/dialog'
import Checkbox from 'primevue/checkbox'
import Tag from 'primevue/tag'
import Chips from 'primevue/chips'
import ConfirmDialog from 'primevue/confirmdialog'
import { useConfirm } from 'primevue/useconfirm'

const confirm = useConfirm()

// Sample welcome page configurations
const welcomePages = ref([
  {
    id: '1',
    cmp_name: 'Blu-Collar',
    logo_path: '/assets/images/blu-collar-logo.png',
    show_signup: true,
    show_login: true,
    welcome_pop_msg: 'Welcome to Blu-Collar - Your Professional Services Platform',
    base_url: 'https://blu-collar.com',
    notification_url: 'https://api.blu-collar.com/notifications',
    user_url: 'https://api.blu-collar.com/users',
    message: 'Welcome to Blu-Collar - Your trusted platform for professional services',
    imageUrl: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=800',
    descriptions: [
      { description: 'Your trusted platform for professional services' },
      { description: 'Connect with skilled professionals in your area' },
      { description: 'Quality work guaranteed with every booking' }
    ],
    audience_messages: [
      { message: 'Join our community of professionals' },
      { message: 'Find reliable service providers near you' },
      { message: 'Build your career with flexible opportunities' }
    ],
    testimonials: [
      { 
        author: 'John Smith', 
        content: 'Great platform for finding professional services! Highly recommended.' 
      },
      { 
        author: 'Sarah Johnson', 
        content: 'As a service provider, this platform has helped me grow my business significantly.' 
      },
      { 
        author: 'Mike Davis', 
        content: 'Quick and reliable service bookings. The quality is always top-notch.' 
      }
    ],
    isActive: true,
    createdAt: new Date('2024-01-15T10:30:00Z'),
    updatedAt: new Date('2024-01-20T14:45:00Z')
  },
  {
    id: '2',
    cmp_name: 'TechCorp Solutions',
    logo_path: '/assets/images/techcorp-logo.png',
    show_signup: false,
    show_login: true,
    welcome_pop_msg: 'Welcome to TechCorp - Innovation at its Best',
    base_url: 'https://techcorp.com',
    notification_url: 'https://api.techcorp.com/notifications',
    user_url: 'https://api.techcorp.com/users',
    message: 'Welcome to TechCorp Solutions - Leading the way in technology innovation',
    imageUrl: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=800',
    descriptions: [
      { description: 'Cutting-edge technology solutions for modern businesses' },
      { description: 'Expert consultation and implementation services' }
    ],
    audience_messages: [
      { message: 'Transform your business with our technology solutions' },
      { message: 'Expert support every step of the way' }
    ],
    testimonials: [
      { 
        author: 'Emily Chen', 
        content: 'TechCorp transformed our business operations completely. Excellent service!' 
      }
    ],
    isActive: false,
    createdAt: new Date('2024-01-10T09:15:00Z'),
    updatedAt: new Date('2024-01-18T16:20:00Z')
  }
])

// Dialog states
const showWelcomeDialog = ref(false)
const isEditMode = ref(false)
const selectedWelcome = ref(null)

// Form data
const welcomeForm = reactive({
  cmp_name: '',
  logo_path: '',
  show_signup: false,
  show_login: false,
  welcome_pop_msg: '',
  base_url: '',
  notification_url: '',
  user_url: '',
  message: '',
  imageUrl: '',
  descriptions: [],
  audience_messages: [],
  testimonials: []
})

// Search and filters
const searchTerm = ref('')
const showActiveOnly = ref(null)

// Computed properties
const filteredWelcomePages = computed(() => {
  let filtered = welcomePages.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(page => 
      page.cmp_name.toLowerCase().includes(search) ||
      page.message.toLowerCase().includes(search) ||
      page.base_url?.toLowerCase().includes(search)
    )
  }

  if (showActiveOnly.value !== null) {
    filtered = filtered.filter(page => page.isActive === showActiveOnly.value)
  }

  return filtered
})

const welcomeStats = computed(() => {
  const total = welcomePages.value.length
  const active = welcomePages.value.filter(p => p.isActive).length
  const withSignup = welcomePages.value.filter(p => p.show_signup).length
  const withLogin = welcomePages.value.filter(p => p.show_login).length
  
  return {
    total,
    active,
    inactive: total - active,
    withSignup,
    withLogin
  }
})

// Methods
const openWelcomeDialog = (welcomePage = null) => {
  isEditMode.value = !!welcomePage
  if (welcomePage) {
    // Populate form with welcome page data
    Object.keys(welcomeForm).forEach(key => {
      if (welcomePage[key] !== undefined) {
        if (Array.isArray(welcomePage[key])) {
          welcomeForm[key] = [...welcomePage[key]]
        } else {
          welcomeForm[key] = welcomePage[key]
        }
      }
    })
    selectedWelcome.value = welcomePage
  } else {
    resetWelcomeForm()
  }
  showWelcomeDialog.value = true
}

const resetWelcomeForm = () => {
  Object.assign(welcomeForm, {
    cmp_name: '',
    logo_path: '',
    show_signup: false,
    show_login: false,
    welcome_pop_msg: '',
    base_url: '',
    notification_url: '',
    user_url: '',
    message: '',
    imageUrl: '',
    descriptions: [],
    audience_messages: [],
    testimonials: []
  })
  selectedWelcome.value = null
}

const saveWelcomePage = () => {
  if (!welcomeForm.cmp_name.trim() || !welcomeForm.message.trim()) return

  if (isEditMode.value && selectedWelcome.value) {
    // Update existing welcome page
    const index = welcomePages.value.findIndex(p => p.id === selectedWelcome.value.id)
    if (index !== -1) {
      welcomePages.value[index] = {
        ...welcomePages.value[index],
        ...welcomeForm,
        updatedAt: new Date()
      }
    }
  } else {
    // Add new welcome page
    const newWelcomePage = {
      ...welcomeForm,
      id: Date.now().toString(),
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    welcomePages.value.push(newWelcomePage)
  }

  showWelcomeDialog.value = false
  resetWelcomeForm()
}

const deleteWelcomePage = (welcomePage) => {
  confirm.require({
    message: `Are you sure you want to delete the welcome page for "${welcomePage.cmp_name}"?`,
    header: 'Delete Welcome Page',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: 'Delete',
    accept: () => {
      const index = welcomePages.value.findIndex(p => p.id === welcomePage.id)
      if (index !== -1) {
        welcomePages.value.splice(index, 1)
      }
    }
  })
}

const toggleWelcomeStatus = (welcomePage) => {
  const index = welcomePages.value.findIndex(p => p.id === welcomePage.id)
  if (index !== -1) {
    welcomePages.value[index].isActive = !welcomePages.value[index].isActive
    welcomePages.value[index].updatedAt = new Date()
  }
}

const addDescription = () => {
  welcomeForm.descriptions.push({ description: '' })
}

const removeDescription = (index) => {
  welcomeForm.descriptions.splice(index, 1)
}

const addAudienceMessage = () => {
  welcomeForm.audience_messages.push({ message: '' })
}

const removeAudienceMessage = (index) => {
  welcomeForm.audience_messages.splice(index, 1)
}

const addTestimonial = () => {
  welcomeForm.testimonials.push({ author: '', content: '' })
}

const removeTestimonial = (index) => {
  welcomeForm.testimonials.splice(index, 1)
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const clearFilters = () => {
  searchTerm.value = ''
  showActiveOnly.value = null
}

const exportWelcomePages = () => {
  // Simple CSV export
  const csvContent = [
    ['ID', 'Company Name', 'Message', 'Base URL', 'Show Signup', 'Show Login', 'Active', 'Created At'].join(','),
    ...filteredWelcomePages.value.map(page => [
      page.id,
      `"${page.cmp_name.replace(/"/g, '""')}"`,
      `"${page.message.replace(/"/g, '""')}"`,
      page.base_url || '',
      page.show_signup,
      page.show_login,
      page.isActive,
      page.createdAt.toISOString()
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `welcome-pages-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}
</script>

<template>
  <div class="welcome-page-view">
    <!-- Left Sidebar with Filters -->
    <div class="filters-sidebar">
      <div class="filters-header">
        <h3>
          <i class="pi pi-filter"></i>
          Filters
        </h3>
        <Button 
          icon="pi pi-filter-slash" 
          text
          rounded
          size="small"
          @click="clearFilters"
          :disabled="!searchTerm && showActiveOnly === null"
          v-tooltip="'Clear All Filters'"
        />
      </div>

      <div class="filters-content">
        <!-- Search -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-search"></i>
            Search
          </label>
          <InputText 
            v-model="searchTerm" 
            placeholder="Search welcome pages..." 
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-check-circle"></i>
            Status
          </label>
          <div class="status-buttons">
            <Button 
              label="All" 
              :severity="showActiveOnly === null ? 'primary' : 'secondary'"
              size="small"
              @click="showActiveOnly = null"
              class="w-full"
            />
            <Button 
              label="Active" 
              :severity="showActiveOnly === true ? 'success' : 'secondary'"
              size="small"
              @click="showActiveOnly = true"
              class="w-full"
            />
            <Button 
              label="Inactive" 
              :severity="showActiveOnly === false ? 'warning' : 'secondary'"
              size="small"
              @click="showActiveOnly = false"
              class="w-full"
            />
          </div>
        </div>

        <!-- Statistics -->
        <div class="filter-stats">
          <h4>Statistics</h4>
          <div class="stats-list">
            <div class="stat-item">
              <span class="stat-label">Total Pages</span>
              <span class="stat-value">{{ welcomeStats.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active</span>
              <span class="stat-value success">{{ welcomeStats.active }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">With Signup</span>
              <span class="stat-value">{{ welcomeStats.withSignup }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">With Login</span>
              <span class="stat-value">{{ welcomeStats.withLogin }}</span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h4>Quick Actions</h4>
          <div class="action-buttons">
            <Button 
              label="Add Welcome Page" 
              icon="pi pi-plus" 
              class="w-full"
              @click="openWelcomeDialog()"
            />
            <Button 
              label="Export Data" 
              icon="pi pi-download" 
              severity="secondary"
              class="w-full"
              @click="exportWelcomePages"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div>
            <h1>Welcome Page Management</h1>
            <p>Manage welcome page configurations and content</p>
          </div>
        </div>
      </div>

      <!-- Welcome Pages Table -->
      <div class="table-container">
        <DataTable 
          :value="filteredWelcomePages" 
          class="welcome-table"
          :paginator="true" 
          :rows="10"
          :rows-per-page-options="[10, 20, 50]"
          stripedRows
          :rowHover="true"
          sort-field="updatedAt"
          :sort-order="-1"
          :scrollable="true"
          scroll-height="flex"
        >
          <Column field="cmp_name" header="Company" sortable>
            <template #body="slotProps">
              <div class="company-cell">
                <div class="company-logo" v-if="slotProps.data.logo_path">
                  <img :src="slotProps.data.logo_path" :alt="slotProps.data.cmp_name" />
                </div>
                <div class="company-info">
                  <span class="company-name">{{ slotProps.data.cmp_name }}</span>
                  <span class="company-url">{{ slotProps.data.base_url || 'No URL' }}</span>
                </div>
              </div>
            </template>
          </Column>
          
          <Column field="message" header="Welcome Message" sortable>
            <template #body="slotProps">
              <span class="message-text" :title="slotProps.data.message">
                {{ slotProps.data.message }}
              </span>
            </template>
          </Column>
          
          <Column header="Features" style="width: 150px">
            <template #body="slotProps">
              <div class="features-tags">
                <Tag 
                  v-if="slotProps.data.show_signup" 
                  value="Signup" 
                  severity="success" 
                  class="feature-tag"
                />
                <Tag 
                  v-if="slotProps.data.show_login" 
                  value="Login" 
                  severity="info" 
                  class="feature-tag"
                />
              </div>
            </template>
          </Column>
          
          <Column header="Content" style="width: 120px">
            <template #body="slotProps">
              <div class="content-counts">
                <span class="count-item">{{ slotProps.data.descriptions?.length || 0 }} desc</span>
                <span class="count-item">{{ slotProps.data.testimonials?.length || 0 }} test</span>
              </div>
            </template>
          </Column>
          
          <Column field="updatedAt" header="Updated" sortable style="width: 150px">
            <template #body="slotProps">
              <span class="updated-date">{{ formatDate(slotProps.data.updatedAt) }}</span>
            </template>
          </Column>
          
          <Column field="isActive" header="Status" sortable style="width: 100px">
            <template #body="slotProps">
              <Tag 
                :value="slotProps.data.isActive ? 'Active' : 'Inactive'" 
                :severity="slotProps.data.isActive ? 'success' : 'warning'"
              />
            </template>
          </Column>
          
          <Column header="Actions" style="width: 120px">
            <template #body="slotProps">
              <div class="action-buttons">
                <Button 
                  icon="pi pi-pencil" 
                  text 
                  rounded 
                  size="small" 
                  @click="openWelcomeDialog(slotProps.data)"
                  v-tooltip="'Edit'"
                />
                <Button 
                  :icon="slotProps.data.isActive ? 'pi pi-eye-slash' : 'pi pi-eye'" 
                  text 
                  rounded 
                  size="small" 
                  :severity="slotProps.data.isActive ? 'warning' : 'success'"
                  @click="toggleWelcomeStatus(slotProps.data)"
                  :v-tooltip="slotProps.data.isActive ? 'Deactivate' : 'Activate'"
                />
                <Button 
                  icon="pi pi-trash" 
                  text 
                  rounded 
                  size="small" 
                  severity="danger"
                  @click="deleteWelcomePage(slotProps.data)"
                  v-tooltip="'Delete'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Welcome Page Dialog -->
    <Dialog 
      v-model:visible="showWelcomeDialog" 
      :header="isEditMode ? 'Edit Welcome Page' : 'Add Welcome Page'"
      :modal="true" 
      class="welcome-dialog"
      :style="{ width: '900px' }"
      :maximizable="true"
    >
      <div class="dialog-content">
        <div class="form-grid">
          <!-- Basic Information -->
          <div class="form-section">
            <h4>Basic Information</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="cmp-name">Company Name *</label>
                <InputText 
                  id="cmp-name"
                  v-model="welcomeForm.cmp_name" 
                  placeholder="Enter company name"
                  class="w-full"
                  :class="{ 'p-invalid': !welcomeForm.cmp_name.trim() }"
                />
              </div>
              
              <div class="form-group">
                <label for="logo-path">Logo Path</label>
                <InputText 
                  id="logo-path"
                  v-model="welcomeForm.logo_path" 
                  placeholder="Enter logo path or URL"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-group">
              <label for="welcome-message">Welcome Message *</label>
              <Textarea 
                id="welcome-message"
                v-model="welcomeForm.message" 
                placeholder="Enter welcome message"
                rows="3"
                class="w-full"
                :class="{ 'p-invalid': !welcomeForm.message.trim() }"
              />
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="image-url">Welcome Image URL</label>
                <InputText 
                  id="image-url"
                  v-model="welcomeForm.imageUrl" 
                  placeholder="Enter welcome image URL"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="popup-msg">Welcome Popup Message</label>
                <InputText 
                  id="popup-msg"
                  v-model="welcomeForm.welcome_pop_msg" 
                  placeholder="Enter popup message"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Configuration -->
          <div class="form-section">
            <h4>Configuration</h4>
            
            <div class="form-row">
              <div class="form-group checkbox-group">
                <Checkbox 
                  id="show-signup"
                  v-model="welcomeForm.show_signup" 
                  :binary="true"
                />
                <label for="show-signup">Show Signup Button</label>
              </div>
              
              <div class="form-group checkbox-group">
                <Checkbox 
                  id="show-login"
                  v-model="welcomeForm.show_login" 
                  :binary="true"
                />
                <label for="show-login">Show Login Button</label>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="base-url">Base URL</label>
                <InputText 
                  id="base-url"
                  v-model="welcomeForm.base_url" 
                  placeholder="https://example.com"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="notification-url">Notification Service URL</label>
                <InputText 
                  id="notification-url"
                  v-model="welcomeForm.notification_url" 
                  placeholder="https://api.example.com/notifications"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="user-url">User Service URL</label>
                <InputText 
                  id="user-url"
                  v-model="welcomeForm.user_url" 
                  placeholder="https://api.example.com/users"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Descriptions -->
          <div class="form-section">
            <div class="section-header">
              <h4>Descriptions</h4>
              <Button 
                label="Add Description" 
                icon="pi pi-plus" 
                size="small"
                @click="addDescription"
              />
            </div>
            
            <div v-for="(desc, index) in welcomeForm.descriptions" :key="index" class="dynamic-item">
              <InputText 
                v-model="desc.description" 
                placeholder="Enter description"
                class="flex-1"
              />
              <Button 
                icon="pi pi-trash" 
                text 
                rounded 
                size="small" 
                severity="danger"
                @click="removeDescription(index)"
              />
            </div>
          </div>

          <!-- Audience Messages -->
          <div class="form-section">
            <div class="section-header">
              <h4>Audience Messages</h4>
              <Button 
                label="Add Message" 
                icon="pi pi-plus" 
                size="small"
                @click="addAudienceMessage"
              />
            </div>
            
            <div v-for="(msg, index) in welcomeForm.audience_messages" :key="index" class="dynamic-item">
              <InputText 
                v-model="msg.message" 
                placeholder="Enter audience message"
                class="flex-1"
              />
              <Button 
                icon="pi pi-trash" 
                text 
                rounded 
                size="small" 
                severity="danger"
                @click="removeAudienceMessage(index)"
              />
            </div>
          </div>

          <!-- Testimonials -->
          <div class="form-section">
            <div class="section-header">
              <h4>Testimonials</h4>
              <Button 
                label="Add Testimonial" 
                icon="pi pi-plus" 
                size="small"
                @click="addTestimonial"
              />
            </div>
            
            <div v-for="(testimonial, index) in welcomeForm.testimonials" :key="index" class="testimonial-item">
              <div class="form-row">
                <div class="form-group">
                  <label>Author</label>
                  <InputText 
                    v-model="testimonial.author" 
                    placeholder="Enter author name"
                    class="w-full"
                  />
                </div>
                <div class="form-group">
                  <Button 
                    icon="pi pi-trash" 
                    text 
                    rounded 
                    size="small" 
                    severity="danger"
                    @click="removeTestimonial(index)"
                    class="remove-testimonial-btn"
                  />
                </div>
              </div>
              <div class="form-group">
                <label>Content</label>
                <Textarea 
                  v-model="testimonial.content" 
                  placeholder="Enter testimonial content"
                  rows="3"
                  class="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showWelcomeDialog = false"
          />
          <Button 
            :label="isEditMode ? 'Update' : 'Create'" 
            @click="saveWelcomePage"
            :disabled="!welcomeForm.cmp_name.trim() || !welcomeForm.message.trim()"
          />
        </div>
      </template>
    </Dialog>

    <ConfirmDialog />
  </div>
</template>

<style scoped>
.welcome-page-view {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Left Filters Sidebar */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filters-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.status-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.stat-value.success {
  color: var(--p-green-600);
}

.quick-actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.quick-actions h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--p-surface-border);
  background: var(--p-surface-card);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-content p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

/* Table Container */
.table-container {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.welcome-table {
  flex: 1;
  height: 100%;
}

.company-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.company-logo {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.company-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.company-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.company-name {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.company-url {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.message-text {
  color: var(--p-text-color);
  font-size: 0.875rem;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.features-tags {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.feature-tag {
  font-size: 0.75rem;
}

.content-counts {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.count-item {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
  background: var(--p-surface-100);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  text-align: center;
}

:global(.dark) .count-item {
  background: var(--p-surface-700);
}

.updated-date {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 1rem 0;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-section h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  border-bottom: 1px solid var(--p-surface-border);
  padding-bottom: 0.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h4 {
  border-bottom: none;
  padding-bottom: 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.dynamic-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.testimonial-item {
  border: 1px solid var(--p-surface-border);
  border-radius: 8px;
  padding: 1rem;
  background: var(--p-surface-50);
}

:global(.dark) .testimonial-item {
  background: var(--p-surface-800);
}

.remove-testimonial-btn {
  margin-top: 1.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .welcome-page-view {
    flex-direction: column;
    height: auto;
  }
  
  .filters-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    order: 2;
  }
  
  .main-content {
    order: 1;
    height: 60vh;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .table-container {
    padding: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .company-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>