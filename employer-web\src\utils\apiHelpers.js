// API Helper utilities
import { api } from '@/api'

// Format API errors for user display
export const formatApiError = (error) => {
  if (error.response?.data?.message) {
    return error.response.data.message
  }
  
  if (error.message) {
    return error.message
  }
  
  // Default error messages based on status code
  switch (error.status) {
    case 400:
      return 'Invalid request. Please check your input.'
    case 401:
      return 'You are not authorized. Please log in again.'
    case 403:
      return 'You do not have permission to perform this action.'
    case 404:
      return 'The requested resource was not found.'
    case 422:
      return 'Validation failed. Please check your input.'
    case 429:
      return 'Too many requests. Please try again later.'
    case 500:
      return 'Server error. Please try again later.'
    case 503:
      return 'Service temporarily unavailable. Please try again later.'
    default:
      return 'An unexpected error occurred. Please try again.'
  }
}

// Check if user is authenticated (job seeker)
export const isAuthenticated = () => {
  const token = localStorage.getItem('emp_token')
  const user = localStorage.getItem('emp_user')
  return !!(token && user)
}

// Get current user from localStorage (job seeker)
export const getCurrentUser = () => {
  try {
    const user = localStorage.getItem('emp_user')
    return user ? JSON.parse(user) : null
  } catch (error) {
    console.error('Error parsing user data:', error)
    return null
  }
}

// Get auth token (job seeker)
export const getAuthToken = () => {
  return localStorage.getItem('emp_token')
}

// Clear auth data (job seeker)
export const clearAuthData = () => {
  localStorage.removeItem('emp_token')
  localStorage.removeItem('emp_user')
}

// Format currency in Indian Rupees
export const formatCurrency = (amount, currency = 'INR') => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date relative to now
export const formatRelativeDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '1 day ago'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  return `${Math.floor(diffDays / 30)} months ago`
}

// Format full date
export const formatFullDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Debounce function for search inputs
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Validate email format
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate phone number format
export const isValidPhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

// Generate avatar initials
export const getInitials = (name) => {
  if (!name) return 'U'
  
  const names = name.trim().split(' ')
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase()
  }
  
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
}

// Generate random color for avatars
export const generateAvatarColor = (name) => {
  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
    '#8b5cf6', '#06b6d4', '#ec4899', '#84cc16'
  ]
  
  if (!name) return colors[0]
  
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  return colors[Math.abs(hash) % colors.length]
}

// File size formatter
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Validate file type
export const isValidFileType = (file, allowedTypes) => {
  return allowedTypes.includes(file.type)
}

// Validate file size
export const isValidFileSize = (file, maxSizeInMB) => {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024
  return file.size <= maxSizeInBytes
}

// Validate URL or domain-like string (relaxed)
export const isValidUrl = (url) => {
  if (!url || typeof url !== 'string') return false;
  // Allow if contains a dot and at least one letter, or starts with http/https
  const relaxedPattern = /([a-zA-Z0-9-]+\.[a-zA-Z]{2,})|^(https?:\/\/)/;
  return relaxedPattern.test(url.trim());
}

// Validate alphabetic (letters and spaces only)
export const isAlphaOnly = (str) => {
  return /^[A-Za-z\s]+$/.test(str.trim());
}

// Validate numeric (digits only)
export const isNumericOnly = (str) => {
  return /^\d+$/.test(str.trim());
}