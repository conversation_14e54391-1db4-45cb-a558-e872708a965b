import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { AdminActionType } from '../enums/admin.enum';

@Entity('admin_activities')
export class AdminActivityEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserEntity, (user) => user.adminActivities)
  admin: UserEntity;

  @Column()
  adminId: string;

  @Column({
    type: 'enum',
    enum: AdminActionType,
  })
  actionType: AdminActionType;

  @Column({ nullable: true })
  targetId: string;

  @Column({ type: 'jsonb', nullable: true })
  details: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
