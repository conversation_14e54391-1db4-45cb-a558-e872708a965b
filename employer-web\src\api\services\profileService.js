import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const profileService = {
  // Get user profile
  async getProfile() {
    try {
      const response = await httpClient.get(ENDPOINTS.PROFILE.GET)
      return {
        success: true,
        data: response.profile || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch profile')
    }
  },

  // Update user profile
  async updateProfile(profileData) {
    try {
      // Map frontend fields to backend fields
      const mappedData = {
        ...profileData,
        addressLandmark: profileData.address || '',
        addressCity: profileData.city || '',
        pinCode: profileData.postalCode || '',
        phoneNumber: profileData.phone ? profileData.phone : null,
        dateOfBirth: profileData.dateOfBirth ? profileData.dateOfBirth : null,
        jobTitle: profileData.jobTitle || '',
        experienceYears: profileData.experienceYears || 0,
        industry: profileData.industry || '',
        desiredSalary: profileData.desiredSalary || 0,
        bio: profileData.bio || '',
        skills: profileData.skills || [],
        workExperience: profileData.workExperience || [],
        education: profileData.education || [],
      }
      const response = await httpClient.put(ENDPOINTS.PROFILE.UPDATE, mappedData)
      return {
        success: true,
        data: response.profile || response.data,
        message: response.message || 'Profile updated successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to update profile')
    }
  },

  // Upload avatar
  async uploadAvatar(file) {
    try {
      const formData = new FormData()
      formData.append('avatar', file)
      
      const response = await httpClient.post(ENDPOINTS.PROFILE.UPLOAD_AVATAR, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return {
        success: true,
        data: response.avatar || response.data,
        message: response.message || 'Avatar uploaded successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to upload avatar')
    }
  },

  // Delete avatar
  async deleteAvatar() {
    try {
      const response = await httpClient.delete(ENDPOINTS.PROFILE.DELETE_AVATAR)
      return {
        success: true,
        message: response.message || 'Avatar deleted successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to delete avatar')
    }
  },

  // Update privacy settings
  async updatePrivacySettings(settings) {
    try {
      const response = await httpClient.put(ENDPOINTS.PROFILE.PRIVACY_SETTINGS, settings)
      return {
        success: true,
        data: response.settings || response.data,
        message: response.message || 'Privacy settings updated successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to update privacy settings')
    }
  },

  async getApplicantProfile(id) {
    try {
      const response = httpClient.get(ENDPOINTS.PROFILE.APPLICANT_PROFILE(id))

      return response;
    }
    catch (error) {
      throw new Error(error.message || 'Failed to get Applicant Profile')
    }
  }
}