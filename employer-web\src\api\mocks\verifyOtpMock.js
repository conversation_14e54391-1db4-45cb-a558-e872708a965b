// Mock API for Verify OTP endpoint
// POST: auth/login/email/verify

const delay = (ms = 600) => new Promise(resolve => setTimeout(resolve, ms))

export const verifyOtpMock = {
  async verifyOtp(email, otp, userType = 'employer') {
    await delay(600)
    
    if (!email || !email.includes('@')) {
      return {
        data: null,
        success: false,
        status: 401,
        errorCode: "OTP_VERIFY_ERROR",
        errorMessage: "Invalid email address"
      }
    }
    
    if (!otp || otp.length !== 6) {
      return {
        data: null,
        success: false,
        status: 401,
        errorCode: "OTP_VERIFY_ERROR",
        errorMessage: "Invalid OTP"
      }
    }
    
    // Test scenarios based on OTP
    if (otp === '000000') {
      // Simulate expired OTP
      return {
        data: null,
        success: false,
        status: 401,
        errorCode: "OTP_VERIFY_ERROR",
        errorMessage: "OTP expired"
      }
    }
    
    if (otp === '111111') {
      // Simulate invalid OTP
      return {
        data: null,
        success: false,
        status: 401,
        errorCode: "OTP_VERIFY_ERROR",
        errorMessage: "Invalid OTP"
      }
    }
    
    // Test different user roles based on email
    let userRole = "employer" // Default role
    
    // Test unauthorized user (not employer)
    if (email.includes('employer') || email.includes('admin') || email.includes('hr')) {
      userRole = "employer" // This will trigger unauthorized alert
    }
    
    // Success response with your exact format
    return {
      data: {
        access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.QBIyXEjtUq0xnGYy26znPzYM6y44LEy1G1zjQBkqWRQ",
        refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.tqFQSKpSc-ElgWb9Z7npD_G_U4lbIFCRTWLjpj_KJKU",
        user: {
          id: "fc6f26e6-f1a9-4b6b-8529-cf5552b54274",
          email: email,
          phone: null,
          role: userRole, // Dynamic role based on email
          profile: {
            id: "d2bb5119-2cc8-4fb7-9575-c75ef6606da0",
            userId: "fc6f26e6-f1a9-4b6b-8529-cf5552b54274",
            firstName: null,
            lastName: null,
            phoneNumber: null,
            address: null,
            city: null,
            state: null,
            country: null,
            postalCode: null,
            profileImage: null,
            profileImageThumbnail: null,
            imageMetadata: null,
            isComplete: false,
            updatedBy: null,
            createdAt: "2025-06-09T18:44:54.785Z",
            updatedAt: "2025-06-09T18:44:54.785Z"
          }
        }
      },
      success: true,
      status: 200
    }
  }
}