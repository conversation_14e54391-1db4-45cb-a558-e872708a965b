import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestimonialEntity } from '../entities/testimonial.entity';
import {
  CreateTestimonialDto,
  UpdateTestimonialDto,
  ApproveTestimonialDto,
} from '../dto/testimonial.dto';

@Injectable()
export class TestimonialService {
  constructor(
    @InjectRepository(TestimonialEntity)
    private testimonialRepository: Repository<TestimonialEntity>,
  ) {}

  async create(
    userId: string,
    createTestimonialDto: CreateTestimonialDto,
  ): Promise<TestimonialEntity> {
    const testimonial = this.testimonialRepository.create({
      ...createTestimonialDto,
      userId,
    });
    return this.testimonialRepository.save(testimonial);
  }

  async findAll(approvedOnly = true): Promise<TestimonialEntity[]> {
    const query = this.testimonialRepository
      .createQueryBuilder('testimonial')
      .leftJoinAndSelect('testimonial.user', 'user')
      .select([
        'testimonial.id',
        'testimonial.content',
        'testimonial.rating',
        'testimonial.isApproved',
        'testimonial.createdAt',
        'user.id',
        'user.firstName',
        'user.lastName',
      ]);

    if (approvedOnly) {
      query.where('testimonial.isApproved = :isApproved', { isApproved: true });
    }

    return query.getMany();
  }

  async findOne(id: string): Promise<TestimonialEntity> {
    const testimonial = await this.testimonialRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!testimonial) {
      throw new NotFoundException('Testimonial not found');
    }

    return testimonial;
  }

  async update(
    id: string,
    userId: string,
    updateTestimonialDto: UpdateTestimonialDto,
  ): Promise<TestimonialEntity> {
    const testimonial = await this.findOne(id);

    if (testimonial.userId !== userId) {
      throw new BadRequestException('You can only update your own testimonials');
    }

    Object.assign(testimonial, updateTestimonialDto);
    return this.testimonialRepository.save(testimonial);
  }

  async approve(
    id: string,
    approveTestimonialDto: ApproveTestimonialDto,
  ): Promise<TestimonialEntity> {
    const testimonial = await this.findOne(id);
    testimonial.isApproved = approveTestimonialDto.isApproved;
    return this.testimonialRepository.save(testimonial);
  }

  async remove(id: string, userId: string): Promise<void> {
    const testimonial = await this.findOne(id);

    if (testimonial.userId !== userId) {
      throw new BadRequestException('You can only delete your own testimonials');
    }

    await this.testimonialRepository.remove(testimonial);
  }

  async getAverageRating(): Promise<number> {
    const result = await this.testimonialRepository
      .createQueryBuilder('testimonial')
      .where('testimonial.isApproved = :isApproved', { isApproved: true })
      .select('AVG(testimonial.rating)', 'average')
      .getRawOne();

    return result?.average || 0;
  }
}
