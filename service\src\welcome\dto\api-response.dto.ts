import { ApiProperty } from '@nestjs/swagger';

export class ApiResponseDto<T> {
  @ApiProperty({ description: 'Response data' })
  data: T;

  @ApiProperty({ description: 'Whether the request was successful', example: true })
  success: boolean;

  @ApiProperty({ description: 'HTTP status code', example: 200 })
  status: number;

  @ApiProperty({ description: 'Error code if any', required: false, example: 'WELCOME_NOT_FOUND' })
  errorCode?: string;

  @ApiProperty({
    description: 'Error message if any',
    required: false,
    example: 'Welcome message not found',
  })
  errorMessage?: string;

  constructor(
    data: T,
    success: boolean,
    status: number,
    errorCode?: string,
    errorMessage?: string,
  ) {
    this.data = data;
    this.success = success;
    this.status = status;
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
  }

  static success<T>(data: T, status = 200): ApiResponseDto<T> {
    return new ApiResponseDto(data, true, status);
  }

  static error<T>(errorCode: string, errorMessage: string, status = 400): ApiResponseDto<T> {
    return new ApiResponseDto(null, false, status, errorCode, errorMessage);
  }
}
