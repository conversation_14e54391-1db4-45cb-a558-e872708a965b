import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { UpdateTestimonialDto } from './testimonial.dto';

export class UpdateWelcomeDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  message?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  updatedBy?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  app_version?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  app_update_message?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  cmp_name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  logo_path?: string;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  show_signup?: boolean;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  show_login?: boolean;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  welcome_pop_msg?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  base_url?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  notification_url?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  user_url?: string;

  @ApiProperty({ type: [UpdateTestimonialDto], required: false })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateTestimonialDto)
  testimonials?: UpdateTestimonialDto[];
}
