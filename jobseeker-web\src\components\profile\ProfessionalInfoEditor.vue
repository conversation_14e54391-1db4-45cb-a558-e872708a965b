<template>
  <Dialog 
    :visible="localVisible"
    @update:visible="localVisible = $event"
    header="Edit Professional Information"
    :modal="true"
    :closable="true"
    class="professional-info-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="professional-info-form">
      <div class="form-grid">
        <div class="form-group">
          <label for="jobTitle">Current Job Title</label>
          <InputText
            id="jobTitle"
            v-model="formData.jobTitle"
            placeholder="e.g., Construction Worker"
          />
        </div>

        <div class="form-group">
          <label for="experienceYears">Experience</label>
          <div class="experience-row" style="display: flex; gap: 0.5rem; align-items: center;">
            <InputNumber
              id="experienceYears"
              v-model="formData.experienceYears"
              :min="0"
              :max="50"
              placeholder="Years"
              style="width: 80px;"
            />
            <span>Years</span>
            <InputNumber
              id="experienceMonths"
              v-model="formData.experienceMonths"
              :min="0"
              :max="11"
              placeholder="Months"
              style="width: 80px;"
            />
            <span>Months</span>
          </div>
        </div>

        <div class="form-group">
          <label for="industry">Industry</label>
          <Select
            id="industry"
            v-model="formData.industry"
            :options="industryOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Industry"
            showClear
          />
        </div>

        <div class="form-group">
          <label for="desiredSalary">Desired Salary (₹)</label>
          <InputNumber
            id="desiredSalary"
            v-model="formData.desiredSalary"
            mode="currency"
            currency="INR"
            locale="en-IN"
            placeholder="Annual salary"
          />
        </div>

        <div class="form-group full-width">
          <label for="bio">Professional Bio</label>
          <Textarea
            id="bio"
            v-model="formData.bio"
            rows="4"
            placeholder="Write a brief description about your professional background and skills..."
            :maxlength="500"
          />
          <small class="char-count">{{ (formData.bio || '').length }}/500 characters</small>
        </div>
      </div>
    </form>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          label="Save Changes"
          :loading="isSaving"
          class="save-btn"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import Select from 'primevue/dropdown'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import alertManager from '@/utils/alertManager'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  profileData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)

const formData = ref({
  jobTitle: '',
  experienceYears: 0,
  experienceMonths: 0,
  industry: '',
  desiredSalary: null,
  bio: ''
})

const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

const industryOptions = [
  { label: 'Construction', value: 'Construction' },
  { label: 'Manufacturing', value: 'Manufacturing' },
  { label: 'Logistics', value: 'Logistics' },
  { label: 'Maintenance', value: 'Maintenance' },
  { label: 'Security', value: 'Security' },
  { label: 'Hospitality', value: 'Hospitality' },
  { label: 'Healthcare Support', value: 'Healthcare Support' },
  { label: 'Automotive', value: 'Automotive' },
  { label: 'Food Service', value: 'Food Service' },
  { label: 'Retail', value: 'Retail' }
]

// Watch for prop changes
watch(() => props.profileData, (newData) => {
  if (newData) {
    formData.value = {
      jobTitle: newData.jobTitle || '',
      experienceYears: newData.experienceYears || 0,
      experienceMonths: newData.experienceMonths || 0,
      industry: newData.industry || '',
      desiredSalary: newData.desiredSalary || null,
      bio: newData.bio || ''
    }
  }
}, { immediate: true, deep: true })

const handleSubmit = async () => {
  isSaving.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('save', formData.value)
    emit('update:visible', false)
    
  
  } catch (error) {
    alertManager.showError('Error', 'Failed to update professional information. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.professional-info-dialog {
  border-radius: 12px !important;
  max-width: 100vw;
  overflow-x: hidden;
}

.professional-info-form {
  padding: 1rem 0;
  overflow-x: hidden;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-dropdown,
.form-group .p-inputnumber,
.form-group .p-inputtextarea {
  width: 100% !important;
}

.char-count {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  text-align: right;
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
  /* Make experience row stack vertically on mobile */
  .experience-row {
    flex-direction: column !important;
    gap: 0.5rem !important;
    align-items: stretch !important;
  }
  .experience-row > * {
    width: 100% !important;
  }
}

.p-error {
  color: #e53935 !important;
  font-size: 0.85rem;
  margin-top: 2px;
}
</style>