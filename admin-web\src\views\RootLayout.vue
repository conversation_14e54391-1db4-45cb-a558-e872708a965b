<script setup>
import { onMounted, ref, watch } from 'vue';
import Sidebar from '@/components/Sidebar.vue';
import Toolbar from 'primevue/toolbar';
import { useAppStore } from '@/stores/app'
import Image from 'primevue/image';
import logo from '@/assets/jd-logo.png'

const appStore = useAppStore();

const title = ref('');
const subTitle = ref('')

watch(() => appStore.pageInfo, (newValue) => {
  title.value = newValue.title;
  subTitle.value = newValue.subTitle
})


</script>
<template>
  <div class="admin-layout">
    <Toolbar>
      <template #start>
        <Image :src="logo" alt="jd-logo" width="180"/>
      </template>

      <template #center>
        <div class="title-container">
          <div class="title-el">{{ title }}</div>
          <div class="subtitle-el">{{ subTitle }}</div>
        </div>
      </template>

      <template #end>
        
      </template>
    </Toolbar>
    <div class="center-container">
      <Sidebar />
      <router-view />
    </div>
  </div>
</template>
<style scoped>
.admin-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}

.center-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.title-container {
  text-align: center;
}

.title-el {
  font-size: 1.2rem;
  font-weight: 600;
}

.subtitle-el {
  font-size: 0.8rem;
}
</style>
