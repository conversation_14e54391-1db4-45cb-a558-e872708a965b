// Mock API for Send OTP endpoint
// POST: auth/login/email/otp

const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

export const sendOtpMock = {
  async sendOtp(email, userType = 'employer') {
    await delay(800)
    
    if (!email || !email.includes('@')) {
      throw new Error('Invalid email address')
    }
    
    // Simulate OTP sending with your exact response format
    console.log(`Mock OTP sent to ${email}: 123456`)
    
    return {
      data: {
        message: "OTP sent successfully"
      },
      success: true,
      status: 200
    }
  }
}