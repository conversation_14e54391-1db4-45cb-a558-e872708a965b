import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, Length, Matches, IsDateString } from 'class-validator';

export class UpdateProfileDto {
  @ApiProperty({ example: 'Near City Mall', required: false })
  @IsString()
  @IsOptional()
  @Length(3, 100)
  addressLandmark?: string;

  @ApiProperty({ example: 'Mumbai', required: false })
  @IsString()
  @IsOptional()
  @Length(2, 50)
  addressCity?: string;

  @IsOptional()
  @IsDateString()
  @ApiProperty({ required: false, example: '1995-12-20' })
  dateOfBirth?: string;

  @IsOptional()
  @IsString()
  @Length(2, 255)
  @ApiProperty({ required: false, example: 'Pune, Maharashtra' })
  location?: string;

  @ApiProperty({ example: 'Maharashtra', required: false })
  @IsString()
  @IsOptional()
  @Length(2, 50)
  state?: string;

  @ApiProperty({ example: 'India', required: false })
  @IsString()
  @IsOptional()
  @Length(2, 50)
  country?: string;

  @ApiProperty({ example: '400001', required: false })
  @IsString()
  @IsOptional()
  @Length(6, 6)
  @Matches(/^\d{6}$/, { message: 'Pin code must be exactly 6 digits' })
  pinCode?: string;

  @ApiProperty({ example: 'https://example.com/profile.jpg', required: false })
  @IsString()
  @IsOptional()
  @Matches(/^https?:\/\/.+/, { message: 'Profile image must be a valid URL' })
  profileImageSrc?: string;
}
