import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api'
import { formatApiError, clearAuthData } from '@/utils/apiHelpers'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const enums = ref(null)
  const isInitialised = ref(false);
  const industries = ref(null)

  // Helper function to get cookie value
  const getCookie = (name) => {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop().split(';').shift()
    return null
  }

  // Getters - Updated to use cookies for token
  const isAuthenticated = computed(() => {
    // Check both in-memory state and cookies
    // const hasMemoryAuth = !!(user.value && token.value)
    // const hasStoredAuth = !!(getCookie('jsac.t') && localStorage.getItem('emp_user'))
    // return hasMemoryAuth || hasStoredAuth

    return !!user.value
  })
  
  const userEmail = computed(() => user.value?.email || '')
  const userName = computed(() => user.value?.name || user.value?.firstName || '')
  const userRole = computed(() => user.value?.role || 'employer')

  const getEnumData = async () => {
    const response = await api.jobEnums.getJobEnums()

    if (response.success) {
      enums.value = response.data
    }
  }

  const getIndustries = async () => {
    const response = await api.jobEnums.getIndustries()

    if (response.success) {
      industries.value = response.data
    }
  }
  // Actions
  const afterLogin = async (response) => {
    try {
      if (response.user) {
        user.value = response.user
        // token.value = getCookie('jsac.t') // Get token from cookie
        // Store only user in localStorage
        // localStorage.setItem('emp_user', JSON.stringify(response.user))
        
        return { success: true, user: response.user }
      } else {
        throw new Error(response.message || 'Login failed')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Login error:', err)
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      await api.auth.logout()
    } catch (err) {
      console.warn('Logout API call failed:', err)
    } finally {
      // Clear state regardless of API call result
      user.value = null
      token.value = null
      error.value = null
      
      // Clear localStorage
      clearemployerAuthData()

      // Expire cookies by setting past date and empty value
      const expireDate = new Date(0).toUTCString()
      document.cookie = `jsac.t=; expires=${expireDate}; path=/;`
      document.cookie = `jsac.t=; expires=${expireDate}; path=/;`
    }
  }

  const refreshToken = async () => {
    const currentToken = getCookie('jsac.t')
    if (!currentToken) return false

    try {
      const response = await api.auth.refreshToken(currentToken)
      
      if (response.success && response.token) {
        token.value = getCookie('jsac.t') // Get new token from cookie
        
        // Update user if provided
        if (response.user) {
          user.value = response.user
          // localStorage.setItem('emp_user', JSON.stringify(response.user))
        }
        
        return true
      }
      
      return false
    } catch (err) {
      console.error('Token refresh failed:', err)
      await logout()
      return false
    }
  }

  const initializeAuth = async () => {
    try{
      const res = await api.auth.getProfile()
      if (res && res.success) {
        afterLogin(res.data)
        isInitialised.value = true
      }
      else if (res.status === 401) {
        isInitialised.value = true
        await logout()
        return;
      }
      else {
        isInitialised.value = false;
      }
    }
    catch(err) {
      isInitialised.value = false;
    }

    const storedUser = user.value
    
    if (!storedUser) {
        console.error('Failed to parse stored auth data:', err)
        clearemployerAuthData()
    }
  }

  const clearError = () => {
    error.value = null
  }

  const updateUser = (userData) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      // localStorage.setItem('emp_user', JSON.stringify(user.value))
    }
  }

  // Helper function to clear employer auth data
  const clearemployerAuthData = () => {
    // localStorage.removeItem('emp_user')
  }

  // Initialize auth state on store creation
  // initializeAuth()

  return {
    // State
    user,
    token,
    isLoading,
    error,
    // Getters
    isAuthenticated,
    userEmail,
    userName,
    userRole,
    enums,
    isInitialised,
    getEnumData,
    industries,
    getIndustries,
    // Actions
    afterLogin,
    logout,
    refreshToken,
    initializeAuth,
    clearError,
    updateUser,
    clearemployerAuthData
  }
})