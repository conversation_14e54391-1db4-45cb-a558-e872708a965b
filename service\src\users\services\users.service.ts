import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { UserEntity } from '../entities/user.entity';
import { AdminActivityEntity } from '../entities/admin-activity.entity';
import { AdminActionType } from '../enums/admin.enum';
import { UserRole, SubscriptionType } from '../enums/user.enum';
import * as bcrypt from 'bcrypt';
import { readFileSync } from 'fs';
import { join } from 'path';
import { ProfileEntity } from '../entities/profile.entity';
import { Throttle } from '@nestjs/throttler';
import { PhoneSignupDto, EmailSignupDto } from '../dto/auth.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { SmsService } from '../../sms/sms.service';
import { EmailService } from '../../email/email.service';

interface DefaultUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  isEmailVerified: boolean;
  isProfileComplete: boolean;
}

interface RoleConfig {
  role: UserRole;
  defaultUser: DefaultUser;
}

interface RolesConfig {
  roles: RoleConfig[];
}

interface UserCreateData extends Partial<UserEntity> {
  firstName?: string;
  lastName?: string;
  password?: string;
  phone?: string;
  email?: string;
}

@Injectable()
export class UsersService {
  private readonly rolesConfig: RolesConfig;

  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(AdminActivityEntity)
    private readonly adminActivityRepository: Repository<AdminActivityEntity>,
    @InjectRepository(ProfileEntity)
    private readonly profileRepository: Repository<ProfileEntity>,
    private readonly smsService: SmsService,
    private readonly emailService: EmailService,
  ) {
    const configPath = join(process.cwd(), 'src', 'users', 'config', 'roles.json');
    this.rolesConfig = JSON.parse(readFileSync(configPath, 'utf8'));
  }

  private isProfileComplete(profile: ProfileEntity): boolean {
    return !!(
      profile.firstName &&
      profile.lastName &&
      profile.phoneNumber &&
      profile.city &&
      profile.state &&
      profile.jobTitle &&
      profile.experienceYears &&
      profile.experienceMonths &&
      profile.industry &&
      profile.desiredSalary &&
      profile.bio &&
      profile.skills?.length > 0 &&
      profile.workExperience?.length > 0 &&
      profile.education?.length > 0 &&
      profile.profileImage
    );
  }

  @Throttle({ default: { limit: 3, ttl: 60 } })
  async initiatePhoneSignup(phoneSignupDto: PhoneSignupDto) {
    const existingUser = await this.userRepository.findOne({
      where: { phone: phoneSignupDto.phone },
    });

    if (existingUser) {
      throw new BadRequestException('Phone number already registered');
    }

    // Create user
    const user = this.userRepository.create({
      phone: phoneSignupDto.phone,
      isPhoneVerified: false,
      isEmailVerified: false,
      isProfileComplete: false,
      isAadharVerified: false,
      isBlocked: false,
      role: UserRole.JOB_SEEKER,
      subscriptionType: SubscriptionType.DEFAULT,
    });
    await this.userRepository.save(user);

    // Create profile
    const profile = this.profileRepository.create({
      userId: user.id,
      firstName: phoneSignupDto.firstName,
      lastName: phoneSignupDto.lastName,
      phoneNumber: phoneSignupDto.phone,
      isComplete: false,
    });
    await this.profileRepository.save(profile);

    // Update user with profileId
    user.profileId = profile.id;
    await this.userRepository.save(user);

    // Send verification code
    await this.smsService.sendOtpSms(phoneSignupDto.phone, '');

    return { message: 'Verification code sent successfully. Please check your phone.' };
  }

  @Throttle({ default: { limit: 3, ttl: 60 } })
  async initiateEmailSignup(emailSignupDto: EmailSignupDto) {
    const existingUser = await this.userRepository.findOne({
      where: { email: emailSignupDto.email },
    });

    if (existingUser) {
      throw new BadRequestException('Email already registered');
    }

    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const user = this.userRepository.create({
      email: emailSignupDto.email,
      emailOtp: otp,
      emailOtpExpiry: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
      isPhoneVerified: false,
      isEmailVerified: false,
      isProfileComplete: false,
      isAadharVerified: false,
      isBlocked: false,
      role: UserRole.JOB_SEEKER,
      subscriptionType: SubscriptionType.DEFAULT,
    });
    await this.userRepository.save(user);

    // Create profile
    const profile = this.profileRepository.create({
      userId: user.id,
      firstName: emailSignupDto.firstName,
      lastName: emailSignupDto.lastName,
      isComplete: false,
    });
    await this.profileRepository.save(profile);

    // Update user with profileId
    user.profileId = profile.id;
    await this.userRepository.save(user);

    await this.emailService.sendOtpEmail(emailSignupDto.email, otp);

    return { message: 'OTP sent successfully. Please check your email.' };
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Update user fields
    if (updateUserDto.role) user.role = updateUserDto.role;
    if (updateUserDto.subscriptionType) user.subscriptionType = updateUserDto.subscriptionType;

    // Update profile if provided
    if (user.profile) {
      if (updateUserDto.firstName) user.profile.firstName = updateUserDto.firstName;
      if (updateUserDto.lastName) user.profile.lastName = updateUserDto.lastName;
      if (
        Object.keys(updateUserDto).some((key) =>
          ['address', 'city', 'state', 'country', 'postalCode', 'profileImage'].includes(key),
        )
      ) {
        Object.assign(user.profile, updateUserDto);
      }
      await this.profileRepository.save(user.profile);
    }

    // Check if profile is complete
    const isProfileComplete = this.isProfileComplete(user.profile);
    if (isProfileComplete !== user.isProfileComplete) {
      user.isProfileComplete = isProfileComplete;
    }

    await this.userRepository.save(user);

    return {
      message: 'User updated successfully',
      user,
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    role?: UserRole,
    isBlocked?: boolean,
    searchTerm?: string,
  ) {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile');

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (isBlocked !== undefined) {
      queryBuilder.andWhere('user.isBlocked = :isBlocked', { isBlocked });
    }

    if (searchTerm) {
      queryBuilder.andWhere(
        '(user.email ILIKE :searchTerm OR profile.firstName ILIKE :searchTerm OR profile.lastName ILIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` },
      );
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  async findByEmail(email: string): Promise<UserEntity | null> {
    return this.userRepository.findOne({
      where: { email },
      relations: ['profile'],
    });
  }

  async findOrgUser(
    criteria: Partial<Pick<UserEntity, 'id' | 'email' | 'phone'>>,
  ): Promise<UserEntity | null> {
    const where: any = {
      role: UserRole.EMPLOYER,
    };

    if (criteria.id) {
      where.id = criteria.id;
    }
    if (criteria.email) {
      where.email = ILike(criteria.email.trim()); // Case-insensitive
    }
    if (criteria.phone) {
      where.phone = ILike(criteria.phone.trim()); // Case-insensitive
    }

    return await this.userRepository.findOne({
      where,
      relations: ['profile', 'company'],
    });
  }
  async create(userData: UserCreateData, adminId: string | null): Promise<UserEntity> {
    console.log('UsersService - Creating user with data:', userData);

    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }

    // Ensure role is properly set
    console.log('UsersService - Role from userData:', userData.role);

    const hashedPassword = await bcrypt.hash(userData.password || '', 10);
    const userFields: Partial<UserEntity> = {
      email: userData.email,
      password: hashedPassword,
      firstName: userData.firstName || '',
      lastName: userData.lastName || '',
      middleName: userData.middleName || '',
      role: userData.role || UserRole.JOB_SEEKER, // Default to JOB_SEEKER if not specified
      subscriptionType: userData.subscriptionType || SubscriptionType.DEFAULT,
      isPhoneVerified: userData.isPhoneVerified || false,
      isEmailVerified: userData.isEmailVerified || false,
      isProfileComplete: userData.isProfileComplete || false,
      isAadharVerified: userData.isAadharVerified || false,
      isBlocked: userData.isBlocked || false,
      createdBy: adminId,
    };

    console.log('UsersService - Creating user with role:', userFields.role);

    // Only add phone if it's provided
    if (userData.phone) {
      userFields.phone = userData.phone;
    }

    const user = this.userRepository.create(userFields);
    const savedUser = await this.userRepository.save(user);
    console.log('UsersService - Saved user with role:', savedUser.role);

    // Create profile
    const profile = this.profileRepository.create({
      userId: savedUser.id,
      firstName: userData.firstName || '',
      lastName: userData.lastName || '',
      isComplete: false,
    });
    const savedProfile = await this.profileRepository.save(profile);

    // Update user with profileId
    savedUser.profileId = savedProfile.id;
    const updatedUser = await this.userRepository.save(savedUser);
    console.log('UsersService - Updated user with role:', updatedUser.role);

    // Only log admin activity if adminId is provided
    if (adminId) {
      await this.adminActivityRepository.save({
        adminId,
        action: AdminActionType.PROFILE_UPDATE,
        userId: updatedUser.id,
        details: { action: 'create', userData },
        actionType: AdminActionType.USER_CREATE,
      });
    }

    return this.findOne(updatedUser.id);
  }

  async update(id: string, userData: Partial<UserEntity>, adminId: string): Promise<UserEntity> {
    const user = await this.findOne(id);

    if (userData.email && userData.email !== user.email) {
      const existingUser = await this.findByEmail(userData.email);
      if (existingUser) {
        throw new BadRequestException('User with this email already exists');
      }
    }

    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
    }

    Object.assign(user, userData);
    await this.userRepository.save(user);

    await this.adminActivityRepository.save({
      adminId,
      actionType: AdminActionType.PROFILE_UPDATE,
      userId: id,
      details: { action: 'update', userData },
    });

    return this.findOne(id);
  }

  async delete(id: string, adminId: string): Promise<void> {
    const user = await this.findOne(id);

    if (user.role === UserRole.SUPER_ADMIN) {
      const superAdminCount = await this.getSuperAdminCount();
      if (superAdminCount <= 1) {
        throw new BadRequestException('Cannot delete the last super admin');
      }
    }

    await this.userRepository.remove(user);

    await this.adminActivityRepository.save({
      adminId,
      action: AdminActionType.PROFILE_UPDATE,
      userId: id,
      details: { action: 'delete' },
    });
  }

  async toggleBlock(id: string, adminId: string, action: 'block' | 'unblock'): Promise<UserEntity> {
    const user = await this.findOne(id);

    if (user.role === UserRole.SUPER_ADMIN) {
      throw new BadRequestException('Cannot block a super admin');
    }

    user.isBlocked = action === 'block';
    await this.userRepository.save(user);

    await this.adminActivityRepository.save({
      adminId,
      action: action === 'block' ? AdminActionType.USER_BLOCKED : AdminActionType.USER_UNBLOCKED,
      userId: id,
      details: { action },
    });

    return this.findOne(id);
  }

  async updateRole(id: string, role: UserRole, adminId: string): Promise<UserEntity> {
    const user = await this.findOne(id);

    if (user.role === UserRole.SUPER_ADMIN && role !== UserRole.SUPER_ADMIN) {
      const superAdminCount = await this.getSuperAdminCount();
      if (superAdminCount <= 1) {
        throw new BadRequestException('Cannot demote the last super admin');
      }
    }

    user.role = role;
    await this.userRepository.save(user);

    await this.adminActivityRepository.save({
      adminId,
      action: AdminActionType.PROFILE_UPDATE,
      userId: id,
      details: { action: 'update_role', role },
    });

    return this.findOne(id);
  }

  async getSuperAdminCount(): Promise<number> {
    return this.userRepository.count({
      where: { role: UserRole.SUPER_ADMIN },
    });
  }

  async getJobseekerCount(): Promise<number> {
    return this.userRepository.count({
      where: { role: UserRole.JOB_SEEKER },
    });
  }

  async getEmployerCount(): Promise<number> {
    return this.userRepository.count({
      where: { role: UserRole.EMPLOYER },
    });
  }

  async ensureDefaultUsersExist(): Promise<void> {
    for (const roleConfig of this.rolesConfig.roles) {
      const { role, defaultUser } = roleConfig;
      const existingUser = await this.userRepository.findOne({
        where: { email: defaultUser.email },
        relations: ['profile'],
      });

      if (!existingUser) {
        await this.createDefaultUser(role, defaultUser);
      }
    }
  }

  private async createDefaultUser(role: UserRole, defaultUser: DefaultUser): Promise<UserEntity> {
    const hashedPassword = await bcrypt.hash(defaultUser.password, 10);

    // Create user first
    const user = this.userRepository.create({
      email: defaultUser.email,
      password: hashedPassword,
      role,
      isEmailVerified: defaultUser.isEmailVerified,
      isProfileComplete: defaultUser.isProfileComplete,
      isPhoneVerified: false,
      isAadharVerified: false,
      isBlocked: false,
      subscriptionType: SubscriptionType.DEFAULT,
    });
    const savedUser = await this.userRepository.save(user);

    // Create profile with userId
    const profile = this.profileRepository.create({
      userId: savedUser.id,
      firstName: defaultUser.firstName,
      lastName: defaultUser.lastName,
      isComplete: defaultUser.isProfileComplete,
    });
    const savedProfile = await this.profileRepository.save(profile);

    // Update user with profileId
    savedUser.profileId = savedProfile.id;
    await this.userRepository.save(savedUser);

    // Reload user with profile
    return this.userRepository.findOne({
      where: { id: savedUser.id },
      relations: ['profile'],
    });
  }

  async getUsers(): Promise<{ users: UserEntity[] }> {
    const users = await this.userRepository.find({
      relations: ['profile'],
    });
    return { users };
  }
}
