import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { JobEntity } from './job.entity';

@Entity('job_favorites')
export class JobFavoriteEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => JobEntity, (job) => job.favorites)
  @JoinColumn()
  job: JobEntity;

  @ManyToOne(() => UserEntity, (user) => user.jobFavorites)
  @JoinColumn()
  user: UserEntity;

  @CreateDateColumn()
  createdAt: Date;
}
