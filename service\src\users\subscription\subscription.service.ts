import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubscriptionEntity } from './subscription.entity';
import { SubscriptionStatus } from './subscription.enum';
import { AdminActivityService } from '../services/admin-activity.service';
import { AdminActionType } from '../enums/admin.enum';
import { UserEntity } from '../entities/user.entity';
import { AdminActivityEntity } from '../entities/admin-activity.entity';

export interface SubscriptionFeature {
  id: string;
  name: string;
  description: string;
  isEnabled: boolean;
  limits?: {
    max?: number;
    current?: number;
  };
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  features: SubscriptionFeature[];
}

@Injectable()
export class SubscriptionService {
  private readonly subscriptionPlans: Record<string, SubscriptionPlan> = {
    basic: {
      id: 'basic',
      name: 'Basic Plan',
      description: 'Essential features for small businesses',
      price: 29.99,
      features: [
        {
          id: 'basic_analytics',
          name: 'Basic Analytics',
          description: 'View basic business metrics',
          isEnabled: true,
          limits: { max: 1000, current: 0 },
        },
        {
          id: 'basic_support',
          name: 'Email Support',
          description: 'Email support during business hours',
          isEnabled: true,
        },
      ],
    },
    pro: {
      id: 'pro',
      name: 'Professional Plan',
      description: 'Advanced features for growing businesses',
      price: 79.99,
      features: [
        {
          id: 'advanced_analytics',
          name: 'Advanced Analytics',
          description: 'View detailed business metrics and reports',
          isEnabled: true,
          limits: { max: 5000, current: 0 },
        },
        {
          id: 'priority_support',
          name: 'Priority Support',
          description: '24/7 priority email and chat support',
          isEnabled: true,
        },
        {
          id: 'api_access',
          name: 'API Access',
          description: 'Access to our API for custom integrations',
          isEnabled: true,
          limits: { max: 10000, current: 0 },
        },
      ],
    },
    enterprise: {
      id: 'enterprise',
      name: 'Enterprise Plan',
      description: 'Custom solutions for large organizations',
      price: 299.99,
      features: [
        {
          id: 'enterprise_analytics',
          name: 'Enterprise Analytics',
          description: 'Custom analytics and reporting solutions',
          isEnabled: true,
          limits: { max: 100000, current: 0 },
        },
        {
          id: 'dedicated_support',
          name: 'Dedicated Support',
          description: '24/7 dedicated support team',
          isEnabled: true,
        },
        {
          id: 'enterprise_api',
          name: 'Enterprise API',
          description: 'Unlimited API access with custom endpoints',
          isEnabled: true,
        },
        {
          id: 'custom_integrations',
          name: 'Custom Integrations',
          description: 'Custom integration development and support',
          isEnabled: true,
        },
      ],
    },
  };

  constructor(
    @InjectRepository(SubscriptionEntity)
    private readonly subscriptionRepository: Repository<SubscriptionEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(AdminActivityEntity)
    private readonly adminActivityRepository: Repository<AdminActivityEntity>,
    private readonly adminActivityService: AdminActivityService,
  ) {}

  async findAll(
    page: number = 1,
    limit: number = 10,
    status?: SubscriptionStatus,
    searchTerm?: string,
  ) {
    const queryBuilder = this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user');

    if (status) {
      queryBuilder.andWhere('subscription.status = :status', { status });
    }

    if (searchTerm) {
      queryBuilder.andWhere(
        '(user.email ILIKE :searchTerm OR user.firstName ILIKE :searchTerm OR user.lastName ILIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` },
      );
    }

    const [subscriptions, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      subscriptions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string): Promise<SubscriptionEntity> {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id },
      relations: ['user'],
    });
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
    return subscription;
  }

  async findByUserId(userId: string): Promise<SubscriptionEntity | null> {
    return this.subscriptionRepository.findOne({
      where: { userId },
      relations: ['user'],
    });
  }

  async create(
    subscriptionData: Partial<SubscriptionEntity>,
    adminId: string,
  ): Promise<SubscriptionEntity> {
    const existingSubscription = await this.findByUserId(subscriptionData.userId);
    if (existingSubscription) {
      throw new BadRequestException('User already has a subscription');
    }

    const subscription = this.subscriptionRepository.create(subscriptionData);
    const savedSubscription = await this.subscriptionRepository.save(subscription);

    await this.adminActivityService.logActivity(
      adminId,
      AdminActionType.SUBSCRIPTION_CHANGE,
      savedSubscription.userId,
      { action: 'create', subscriptionData },
    );

    return savedSubscription;
  }

  async update(
    id: string,
    subscriptionData: Partial<SubscriptionEntity>,
    adminId: string,
  ): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(id);

    Object.assign(subscription, subscriptionData);
    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    await this.adminActivityService.logActivity(
      adminId,
      AdminActionType.SUBSCRIPTION_CHANGE,
      subscription.userId,
      { action: 'update', subscriptionData },
    );

    return updatedSubscription;
  }

  async cancel(id: string, adminId: string): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(id);

    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelledAt = new Date();
    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    await this.adminActivityService.logActivity(
      adminId,
      AdminActionType.SUBSCRIPTION_CHANGE,
      subscription.userId,
      { action: 'cancel' },
    );

    return updatedSubscription;
  }

  async renew(id: string, adminId: string): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(id);

    if (subscription.status !== SubscriptionStatus.CANCELLED) {
      throw new BadRequestException('Only cancelled subscriptions can be renewed');
    }

    subscription.status = SubscriptionStatus.ACTIVE;
    subscription.cancelledAt = null;
    subscription.renewedAt = new Date();
    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    await this.adminActivityService.logActivity(
      adminId,
      AdminActionType.SUBSCRIPTION_CHANGE,
      subscription.userId,
      { action: 'renew' },
    );

    return updatedSubscription;
  }

  async upgradeSubscription(
    id: string,
    newPlan: string,
    newAmount: number,
    adminId: string,
  ): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(id);

    if (subscription.status !== SubscriptionStatus.ACTIVE) {
      throw new BadRequestException('Only active subscriptions can be upgraded');
    }

    // Calculate prorated amount if needed
    const daysRemaining = Math.ceil(
      (subscription.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24),
    );
    const totalDays = Math.ceil(
      (subscription.endDate.getTime() - subscription.startDate.getTime()) / (1000 * 60 * 60 * 24),
    );
    const proratedAmount = (newAmount - subscription.amount) * (daysRemaining / totalDays);

    subscription.plan = newPlan;
    subscription.amount = newAmount;
    subscription.metadata = {
      ...subscription.metadata,
      upgradeDate: new Date(),
      previousPlan: subscription.plan,
      previousAmount: subscription.amount,
      proratedAmount,
    };

    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    await this.adminActivityService.logActivity(
      adminId,
      AdminActionType.SUBSCRIPTION_CHANGE,
      subscription.userId,
      {
        action: 'upgrade',
        newPlan,
        newAmount,
        proratedAmount,
      },
    );

    return updatedSubscription;
  }

  async getSubscriptionFeatures(subscriptionId: string): Promise<SubscriptionFeature[]> {
    const subscription = await this.findOne(subscriptionId);
    const plan = this.subscriptionPlans[subscription.plan];

    if (!plan) {
      throw new NotFoundException('Subscription plan not found');
    }

    // Update current usage for features with limits
    const features = plan.features.map((feature) => {
      if (feature.limits) {
        const currentUsage = subscription.metadata?.featureUsage?.[feature.id] || 0;
        return {
          ...feature,
          limits: {
            ...feature.limits,
            current: currentUsage,
          },
        };
      }
      return feature;
    });

    return features;
  }

  async getAvailablePlans(): Promise<SubscriptionPlan[]> {
    return Object.values(this.subscriptionPlans);
  }

  async getPlanDetails(planId: string): Promise<SubscriptionPlan> {
    const plan = this.subscriptionPlans[planId];
    if (!plan) {
      throw new NotFoundException('Subscription plan not found');
    }
    return plan;
  }

  async updateFeatureUsage(
    subscriptionId: string,
    featureId: string,
    usage: number,
    adminId: string,
  ): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(subscriptionId);
    const plan = this.subscriptionPlans[subscription.plan];
    const feature = plan.features.find((f) => f.id === featureId);

    if (!feature) {
      throw new NotFoundException('Feature not found in subscription plan');
    }

    if (feature.limits && usage > feature.limits.max) {
      throw new BadRequestException('Usage exceeds feature limit');
    }

    const featureUsage = subscription.metadata?.featureUsage || {};
    featureUsage[featureId] = usage;

    subscription.metadata = {
      ...subscription.metadata,
      featureUsage,
    };

    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    await this.adminActivityService.logActivity(
      adminId,
      AdminActionType.SUBSCRIPTION_CHANGE,
      subscription.userId,
      {
        action: 'update_feature_usage',
        featureId,
        usage,
      },
    );

    return updatedSubscription;
  }
}
