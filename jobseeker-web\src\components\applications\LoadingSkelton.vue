<template>
  <div v-for="count in counts" class="p-4 border border-gray-300 rounded-xl shadow-sm space-y-3 m-4">
    <!-- Title & Subtitle -->
    <Skeleton width="70%" height="1rem" />
    <Skeleton width="50%" height="0.75rem" />

    <!-- Status & Date -->
    <div class="flex items-center space-x-2">
      <Skeleton shape="rectangle" width="4rem" height="1.25rem" />
      <Skeleton width="40%" height="0.75rem" />
    </div>

    <!-- Icon & Message -->
    <div class="flex items-center space-x-3 mt-2">
      <div class="space-y-1">
        <Skeleton width="8rem" height="0.75rem" />
        <Skeleton width="12rem" height="0.75rem" />
      </div>
    </div>

    <!-- Buttons -->
    <div class="flex justify-end">
        <Skeleton class="mr-4" width="6rem" height="2rem" borderRadius="0.5rem" />
        <Skeleton width="6rem" height="2rem" borderRadius="0.5rem" />
    </div>
  </div>
</template>

<script setup>
import Skeleton from 'primevue/skeleton';
const counts = new Array(10)
</script>

<style scoped>
/* optional styling adjustments if needed */
</style>
