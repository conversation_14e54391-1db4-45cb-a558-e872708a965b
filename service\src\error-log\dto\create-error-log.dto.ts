import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsEnum } from 'class-validator';

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export class CreateErrorLogDto {
  @ApiProperty({
    description: 'Error message or stack trace',
    example: 'TypeError: Cannot read property of undefined',
  })
  @IsString()
  errorMessage: string;

  @ApiProperty({
    description: 'Screen or component where error occurred',
    example: 'UserProfile',
    required: false,
  })
  @IsOptional()
  @IsString()
  screen?: string;

  @ApiProperty({
    description: 'Browser information',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    required: false,
  })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiProperty({
    description: 'URL where error occurred',
    example: 'https://example.com/profile',
    required: false,
  })
  @IsOptional()
  @IsString()
  url?: string;

  @ApiProperty({
    description: 'Additional error context or metadata',
    example: { componentState: { userId: '123' } },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Error severity level',
    enum: ErrorSeverity,
    example: ErrorSeverity.MEDIUM,
    required: false,
  })
  @IsOptional()
  @IsEnum(ErrorSeverity)
  severity?: ErrorSeverity;
}
