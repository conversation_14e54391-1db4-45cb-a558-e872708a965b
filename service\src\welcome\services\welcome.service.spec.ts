import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WelcomeService } from './welcome.service';
import { WelcomeEntity } from '../entities/welcome.entity';
import { CreateWelcomeDto } from '../dto/create-welcome.dto';
import { UpdateWelcomeDto } from '../dto/update-welcome.dto';
import { NotFoundException } from '@nestjs/common';

describe('WelcomeService', () => {
  let service: WelcomeService;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WelcomeService,
        {
          provide: getRepositoryToken(WelcomeEntity),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<WelcomeService>(WelcomeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a welcome message', async () => {
      const adminId = 'admin-id';
      const createDto: CreateWelcomeDto = {
        message: 'Welcome message',
        imageUrl: 'https://example.com/image.jpg',
      };

      const expectedWelcome = {
        ...createDto,
        updatedBy: adminId,
      };

      mockRepository.create.mockReturnValue(expectedWelcome);
      mockRepository.save.mockResolvedValue({
        id: 'welcome-id',
        ...expectedWelcome,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const result = await service.create(adminId, createDto);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.message).toBe(createDto.message);
      expect(result.imageUrl).toBe(createDto.imageUrl);
      expect(result.updatedBy).toBe(adminId);
      expect(mockRepository.create).toHaveBeenCalledWith(expectedWelcome);
      expect(mockRepository.save).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a welcome message', async () => {
      const expectedWelcome = {
        id: 'welcome-id',
        message: 'Welcome message',
        imageUrl: 'https://example.com/image.jpg',
        updatedBy: 'admin-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(expectedWelcome);

      const result = await service.findOne();

      expect(result).toEqual(expectedWelcome);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        order: { createdAt: 'DESC' },
      });
    });

    it('should throw NotFoundException when no welcome message exists', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne()).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a welcome message', async () => {
      const adminId = 'admin-id';
      const updateDto: UpdateWelcomeDto = {
        message: 'Updated welcome message',
      };

      const existingWelcome = {
        id: 'welcome-id',
        message: 'Old welcome message',
        imageUrl: 'https://example.com/image.jpg',
        updatedBy: 'old-admin-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const expectedWelcome = {
        ...existingWelcome,
        ...updateDto,
        updatedBy: adminId,
      };

      mockRepository.findOne.mockResolvedValue(existingWelcome);
      mockRepository.save.mockResolvedValue(expectedWelcome);

      const result = await service.update(adminId, updateDto);

      expect(result).toEqual(expectedWelcome);
      expect(result.message).toBe(updateDto.message);
      expect(result.updatedBy).toBe(adminId);
      expect(mockRepository.save).toHaveBeenCalledWith(expectedWelcome);
    });

    it('should throw NotFoundException when updating non-existent welcome message', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update('admin-id', { message: 'New message' })).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
