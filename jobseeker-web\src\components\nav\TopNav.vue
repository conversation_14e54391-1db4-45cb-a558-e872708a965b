<script setup>
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router' 
import Toolbar from 'primevue/toolbar';
import Image from 'primevue/image';
import Avatar from 'primevue/avatar';
import AvatarDrawer from './AvatarDrawer.vue';
import { useAppStore } from '@/stores/app'
import logo from '@/assets/jd-logo.png'
import { useAuthStore } from '@/stores/auth'

const appStore = useAppStore();
const router = useRouter();
const authStore = useAuthStore();

const title = ref('');
const subTitle = ref('')
const isMobile = ref(false)
const avatarDrawerVisible = ref(false)

watch(() => appStore.pageInfo, (newValue) => {
  title.value = newValue.title;
  subTitle.value = newValue.subTitle
})

watch(() => appStore.isMobile, (newValue) =>{
    isMobile.value = newValue;
})

// Avatar click toggles drawer open
const openAvatarDrawer = () => {
    avatarDrawerVisible.value = true;
}

const onLogoClick = () => {
    router.push('/jobseeker/dashboard')
}


const getUserInitials = () => {
    const user = authStore.user
    if (!user) return 'U'

    if (user.profile?.firstName || user.profile?.lastName) {
        const firstName = user.profile.firstName || ''
        const lastName = user.profile.lastName || ''
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || 'U'
    }

    if (user.email) {
        return user.email.charAt(0).toUpperCase()
    }

    return 'U'
}
</script>

<template>
    <Toolbar class="top-nav">
      <template #start>
        <Image :src="logo" alt="Logo" width="200" @click="onLogoClick"/>
      </template>

      <template #center>
        <div class="title-container" v-if="!isMobile">
          <h4 class="title-el">{{ title }}</h4>
          <h6 class="subtitle-el">{{ subTitle }}</h6>
        </div>
      </template>

      <template #end>
        <Avatar @click="openAvatarDrawer" :label="getUserInitials()" class="mr-2" size="large" shape="circle"  aria-haspopup="true" aria-controls="overlay_menu"/>
      </template>
    </Toolbar>

    <!-- AvatarDrawer visibility is controlled by v-model:visible -->
    <AvatarDrawer v-model:visible="avatarDrawerVisible" />
</template>

<style scoped>
.top-nav {
    position: sticky;
    z-index: 1;
    top: 0;
}
</style>