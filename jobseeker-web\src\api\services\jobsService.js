import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const jobsService = {
  // Get all jobs with filters and pagination
  async getAll(params = {}) {
    try {
      return await httpClient.get(ENDPOINTS.JOBS.LIST, { params })
      
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch jobs')
    }
  },

  // Get job by ID
  async getById(id) {
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.DETAILS(id))
      return {
        success: true,
        data: response.job || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Job not found')
    }
  },

  // Create new job (for employers)
  async create(jobData) {
    try {
      const response = await httpClient.post(ENDPOINTS.JOBS.CREATE, jobData)
      return {
        success: true,
        data: response.job || response.data,
        message: response.message || 'Job created successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to create job')
    }
  },

  // Update job
  async update(id, jobData) {
    try {
      const response = await httpClient.put(ENDPOINTS.JOBS.UPDATE(id), jobData)
      return {
        success: true,
        data: response.job || response.data,
        message: response.message || 'Job updated successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to update job')
    }
  },

  // Delete job
  async delete(id) {
    try {
      const response = await httpClient.delete(ENDPOINTS.JOBS.DELETE(id))
      return {
        success: true,
        message: response.message || 'Job deleted successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to delete job')
    }
  },

  // Get job statistics
  async getStats() {
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.STATS)
      return {
        success: true,
        data: response.stats || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch job statistics')
    }
  },

  // Search jobs
  async search(query, filters = {}) {
    // Use the same getAll method with search parameters
    return this.getAll({ search: query, ...filters })
  },

  // Get featured jobs
  async getFeatured(limit = 10) {
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.FEATURED, { 
        params: { limit } 
      })

      return {
        success: true,
        data: response.jobs || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch featured jobs')
    }
  },

  // Get recent jobs
  async getRecent(limit = 10) {
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.RECENT, { 
        params: { limit } 
      })
      return {
        success: true,
        data: response.jobs || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recent jobs')
    }
  },

  async toggleFavoriteJob(jobId) {
    try {
      return await httpClient.post(ENDPOINTS.JOBS.TOGGLE_JOB_FAVORITE(jobId))

      // return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to toggle Favorite jobs')
    }
  }
}