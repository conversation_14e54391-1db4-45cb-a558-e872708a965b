import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestimonialEntity } from '../entities/testimonial.entity';
import { CreateTestimonialDto, UpdateTestimonialDto } from '../dto/testimonial.dto';
import { WelcomeEntity } from '../entities/welcome.entity';

@Injectable()
export class TestimonialService {
  constructor(
    @InjectRepository(TestimonialEntity)
    private readonly testimonialRepository: Repository<TestimonialEntity>,
    @InjectRepository(WelcomeEntity)
    private readonly welcomeRepository: Repository<WelcomeEntity>,
  ) {}

  async create(welcomeId: string, createDto: CreateTestimonialDto): Promise<TestimonialEntity> {
    const welcome = await this.welcomeRepository.findOne({ where: { id: welcomeId } });
    if (!welcome) {
      throw new NotFoundException('Welcome not found');
    }

    const testimonial = this.testimonialRepository.create({
      ...createDto,
      welcome,
    });

    return this.testimonialRepository.save(testimonial);
  }

  async findAll(welcomeId: string): Promise<TestimonialEntity[]> {
    return this.testimonialRepository.find({
      where: { welcome: { id: welcomeId } },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<TestimonialEntity> {
    const testimonial = await this.testimonialRepository.findOne({ where: { id } });
    if (!testimonial) {
      throw new NotFoundException('Testimonial not found');
    }
    return testimonial;
  }

  async update(id: string, updateDto: UpdateTestimonialDto): Promise<TestimonialEntity> {
    const testimonial = await this.findOne(id);

    Object.assign(testimonial, updateDto);
    return this.testimonialRepository.save(testimonial);
  }

  async delete(id: string): Promise<void> {
    const testimonial = await this.findOne(id);
    await this.testimonialRepository.remove(testimonial);
  }

  async deleteAll(welcomeId: string): Promise<void> {
    await this.testimonialRepository.delete({ welcome: { id: welcomeId } });
  }
}
