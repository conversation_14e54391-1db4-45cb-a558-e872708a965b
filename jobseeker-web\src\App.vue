<template>
  <!-- Global Toast -->
   <TopLoader v-if="isLoading" />
  <Toast />
  <router-view />
</template>

<script setup>
import { useLoader } from './composables/useLoader';
import TopLoader from './components/TopLoader.vue';

const { isLoading } = useLoader();
import Toast from 'primevue/toast';
// The router-view will handle rendering the appropriate component
</script>

<style>
/* Global styles are handled in style.css */
</style>