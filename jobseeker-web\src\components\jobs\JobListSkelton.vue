<template>
  <div v-for="count in counts" class="p-4 border border-gray-300 rounded-xl shadow-sm w-full mb-4 space-y-3">
    <!-- Job Title -->
    <Skeleton width="70%" height="1.25rem" class="mb-2" />

    <!-- Chips (e.g., Full-time / Remote) -->
    <div class="flex gap-2 mb-2">
      <Skeleton width="3rem" height="1.25rem" />
      <Skeleton width="3.5rem" height="1.25rem" />
    </div>

    <!-- Company Name -->
    <Skeleton width="50%" height="1rem" class="mb-2" />

    <!-- Meta: Location, Exp, Type -->
    <div class="space-y-2">
      <div class="flex items-center gap-2">
        <Skeleton shape="circle" size="1rem" />
        <Skeleton width="40%" height="1rem" />
      </div>
      <div class="flex items-center gap-2">
        <Skeleton shape="circle" size="1rem" />
        <Skeleton width="30%" height="1rem" />
      </div>
      <div class="flex items-center gap-2">
        <Skeleton shape="circle" size="1rem" />
        <Skeleton width="30%" height="1rem" />
      </div>
    </div>

    <!-- Buttons -->
    <div class="flex gap-2 mt-3">
      <Skeleton width="4rem" height="2rem" borderRadius="8px" />
      <Skeleton width="6rem" height="2rem" borderRadius="8px" />
      <Skeleton width="6rem" height="2rem" borderRadius="8px" />
    </div>

    <!-- Skills -->
    <div class="flex flex-wrap gap-2 mt-2">
      <Skeleton width="3rem" height="1.25rem" />
      <Skeleton width="3rem" height="1.25rem" />
      <Skeleton width="2rem" height="1.25rem" />
      <Skeleton width="2rem" height="1.25rem" />
      <Skeleton width="4rem" height="1.25rem" />
    </div>

    <!-- Apply Button -->
    <div class="border-t border-amber-50 mt-3 pt-3">
      <div class="flex justify-end">
        <Skeleton width="5rem" height="2rem" borderRadius="8px" />
      </div>
    </div>
  </div>
</template>

<script setup>
import Skeleton from 'primevue/skeleton';
const counts = new Array(15);
</script>
