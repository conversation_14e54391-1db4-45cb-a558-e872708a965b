import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { JobStatus } from '../entities/job.entity';

export class JobScheduleDto {
  @ApiProperty({
    enum: JobStatus,
    description: 'New status for the job',
    example: JobStatus.ACTIVE,
  })
  @IsEnum(JobStatus)
  status: JobStatus;

  @ApiProperty({
    description: 'Reason for rejection or additional information needed',
    required: false,
    example: 'Please provide more details about the job requirements',
  })
  @IsString()
  @IsOptional()
  comment?: string;
}
