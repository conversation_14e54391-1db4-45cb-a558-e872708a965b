<script setup>
import { ref, onMounted, defineExpose } from 'vue'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Skeleton from 'primevue/skeleton';
import Tag from 'primevue/tag';
import api from '@/api'
import JobStatusDialog from './JobStatusDialog.vue'

const emit = defineEmits(['edit-job']);

const jobs = ref([])
const emptyJobs = ref(new Array(50))

// Status dialog state
const statusDialogVisible = ref(false)
const selectedJob = ref(null)

const loadJobs = async (page = 1, limit = 200) => {
    const response = await api.jobs.getJobList(page, limit)

    if (response.success) {
        jobs.value = response.data.items;
    }
}

const openStatusDialog = (job) => {
  selectedJob.value = job
  statusDialogVisible.value = true
}

const handleStatusUpdate = async (statusData) => {
  try {
    // Call API to update job status
    const response = await api.jobs.updateJobStatus(statusData.jobId, statusData.newStatus)
    
    if (response.success) {
      // Update the job status in the local state
      const index = jobs.value.findIndex(j => j.id === statusData.jobId)
      if (index !== -1) {
        jobs.value[index].status = statusData.newStatus
      }
    }
  } catch (error) {
    console.error('Error updating job status:', error)
  }
}

onMounted(async () => {
    setTimeout(() => {
        loadJobs();
    }, 100)
})

// Expose the method to parent
defineExpose({ loadJobs });

const formatSalary = (job) => {
    const amount = new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0
    }).format(job.salary)

    const frequency = job.paymentType.toLowerCase().replace('_', ' ')
    return `${amount}/${frequency}`
}

const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(new Date(date))
}

const getUrgencyColor = (urgency) => {
    // const colors = {
    //     [JobUrgency.FLEXIBLE]: 'success',
    //     [JobUrgency.URGENT]: 'warning',
    //     [JobUrgency.IMMEDIATE]: 'danger'
    // }
    // return colors[urgency] || 'info'

    return 'info'
}

const getJobTypeColor = (jobType) => {
    // const colors = {
    //     [JobType.FULL_TIME]: 'success',
    //     [JobType.PART_TIME]: 'info',
    //     [JobType.CONTRACT]: 'warning',
    //     [JobType.DAILY_WAGE]: 'secondary'
    // }
    // return colors[jobType] || 'info'

    return 'info';
}

const openJobDialog = (job) => {
  emit('edit-job', job);
};
</script>

<template>
    <div v-if="jobs.length == 0" class=" flex-1">
        <DataTable :value="emptyJobs">
            <Column field="fld" header="Job Title">
                <template #body>
                    <Skeleton></Skeleton>
                </template>
            </Column>
            <Column field="fld" header="Location">
                <template #body>
                    <Skeleton></Skeleton>
                </template>
            </Column>
            <Column field="fld" header="Salary">
                <template #body>
                    <Skeleton></Skeleton>
                </template>
            </Column>
            <Column field="fld" header="Urgency">
                <template #body>
                    <Skeleton></Skeleton>
                </template>
            </Column>
            <Column field="fld" header="Status">
                <template #body>
                    <Skeleton></Skeleton>
                </template>
            </Column>
            <Column field="fld" header="Action">
                <template #body>
                    <Skeleton></Skeleton>
                </template>
            </Column>
        </DataTable>
    </div>

    <DataTable v-if="jobs.length" :value="jobs" class="jobs-table" :paginator="true" :rows="20"
        :rows-per-page-options="[10, 20, 50]" stripedRows :rowHover="true" sort-field="createdAt" :sort-order="-1"
        :scrollable="true" scroll-height="flex">
        <Column field="title" header="Job Title" sortable>
            <template #body="slotProps">
                <div class="job-title-cell">
                    <div class="job-thumbnail" v-if="slotProps.data.thumbnail">
                        <img :src="slotProps.data.thumbnail" :alt="slotProps.data.title" />
                    </div>
                    <div class="job-info">
                        <span class="job-title">{{ slotProps.data.title }}</span>
                        <span class="job-industry">{{ slotProps.data.industryName }}</span>
                    </div>
                </div>
            </template>
        </Column>

        <Column field="location" header="Location" sortable>
            <template #body="slotProps">
                <span class="location-text">{{ slotProps.data.location || 'Remote' }}</span>
            </template>
        </Column>

        <Column field="salary" header="Salary" sortable>
            <template #body="slotProps">
                <span class="salary-text">{{ formatSalary(slotProps.data) }}</span>
            </template>
        </Column>

        <Column field="jobType" header="Type" sortable>
            <template #body="slotProps">
                <Tag :value="slotProps.data.jobType.replace(/_/g, ' ')"
                    :severity="getJobTypeColor(slotProps.data.jobType)" />
            </template>
        </Column>

        <Column field="urgency" header="Urgency" sortable>
            <template #body="slotProps">
                <Tag :value="slotProps.data.urgency" :severity="getUrgencyColor(slotProps.data.urgency)" />
            </template>
        </Column>

        <Column field="vacancies" header="Vacancies" sortable>
            <template #body="slotProps">
                <span class="vacancies-count">{{ slotProps.data.vacancies || 1 }}</span>
            </template>
        </Column>

        <Column field="createdAt" header="Posted" sortable>
            <template #body="slotProps">
                <span class="posted-date">{{ formatDate(slotProps.data.createdAt) }}</span>
            </template>
        </Column>

        <Column field="status" header="Status" sortable>
            <template #body="slotProps">
                <Tag :value="slotProps.data.status"
                    :severity="slotProps.data.status == 'ACTIVE' ? 'success' : 'secondary'" />
            </template>
        </Column>

        <Column header="Actions">
            <template #body="slotProps">
                <div class="action-buttons">
                    <Button icon="pi pi-pencil" text rounded size="small" @click="openJobDialog(slotProps.data)"
                        v-tooltip.left="'Edit Job'" />
                    <Button icon="pi pi-cog" text rounded size="small"
                        severity="info"
                        @click="openStatusDialog(slotProps.data)"
                        v-tooltip.left="'Change Status'" />
                </div>
            </template>
        </Column>
    </DataTable>

    <!-- Job Status Dialog -->
    <JobStatusDialog
        v-model:visible="statusDialogVisible"
        :job="selectedJob"
        @status-updated="handleStatusUpdate"
    />
</template>