import { ApiProperty, ApiExtraModels, ApiSchema } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

@ApiSchema({ name: 'CreateDescription' })
export class CreateDescriptionDto {
  @ApiProperty({
    description: 'The description content',
    example: 'Your trusted platform for professional services',
  })
  @IsString()
  @IsNotEmpty()
  description: string;
}

@ApiSchema({ name: 'UpdateDescription' })
export class UpdateDescriptionDto {
  @ApiProperty({
    description: 'The description content',
    example: 'Your trusted platform for professional services',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}

@ApiSchema({ name: 'DescriptionResponse' })
export class DescriptionResponseDto {
  @ApiProperty({ description: 'Description ID' })
  id: string;

  @ApiProperty({ description: 'Description content' })
  description: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
