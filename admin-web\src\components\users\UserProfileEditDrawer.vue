<script setup>
import { reactive, ref, watch } from 'vue';
import Drawer from 'primevue/drawer';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import InputNumber from 'primevue/inputnumber';
import Checkbox from 'primevue/checkbox';
import DatePicker from 'primevue/datepicker';
import Button from 'primevue/button';
import { usersService } from '@/api/services/usersService';
import { useGlobalToast } from '@/composables/useGlobalToast';

const props = defineProps({
  visible: Boolean,
  user: Object
});

const emit = defineEmits(['close', 'success']);

const loading = ref(false);
const error = ref('');
const fieldErrors = ref({});

const form = reactive({
  // addressLandmark: '',
  city: '',
  address: '',
  state: '',
  country: '',
  postalCode: '',
  bio: '',
  jobTitle: '',
  desiredSalary: null,
  experienceYears: null,
  language: 'en',
  skills: '',
  phoneNotification: true,
  emailNotification: true,
  isPublic: true,
  dateOfBirth: '',
  profileImageSrc: ''
});

const { showToast } = useGlobalToast();

watch(() => props.user, (currentUser) => {
  if (currentUser) {
    const profileData = currentUser.profile || {};
    form.addressLandmark = profileData.addressLandmark || '';
    form.city = profileData.city || '';
    form.location = profileData.location || '';
    form.state = profileData.state || '';
    form.country = profileData.country || '';
    form.postalCode = profileData.postalCode || '';
    form.profileImageSrc = profileData.profileImageSrc || '';
    form.dateOfBirth = profileData.dateOfBirth ? new Date(profileData.dateOfBirth) : null;
    
    form.bio = profileData.bio || '';
    form.jobTitle = profileData.jobTitle || '';
    form.desiredSalary = profileData.desiredSalary || null;
    form.experienceYears = profileData.experienceYears || null;
    form.language = profileData.language || 'en';
    form.skills = Array.isArray(profileData.skills) ? profileData.skills.join(', ') : (profileData.skills || '');
    form.phoneNotification = profileData.phoneNotification !== false;
    form.emailNotification = profileData.emailNotification !== false;
    form.isPublic = profileData.isPublic !== false;

    error.value = '';
  }
}, { immediate: true, deep: true });

function isAlphaOnly(str) {
  return /^[a-zA-Z\s]*$/.test(str);
}
function isNumericOnly(str) {
  return /^\d*$/.test(str);
}

function formatDateDDMMYYYY(date) {
  if (!(date instanceof Date)) return '';
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

function validateForm() {
  const errors = {};
  if (form.city && !isAlphaOnly(form.city)) {
    errors.city = 'City must contain only letters.';
  }
  if (form.state && !isAlphaOnly(form.state)) {
    errors.state = 'State must contain only letters.';
  }
  if (form.country && !isAlphaOnly(form.country)) {
    errors.country = 'Country must contain only letters.';
  }
  if (form.postalCode && !isNumericOnly(form.postalCode)) {
    errors.postalCode = 'Pin Code must contain only numbers.';
  }
  fieldErrors.value = errors;
  return Object.keys(errors).length === 0;
}

async function handleSubmit() {
  if (!props.user) return;
  error.value = '';
  if (!validateForm()) {
    return;
  }
  loading.value = true;
  try {
    const payload = { ...form };
    if (payload.dateOfBirth instanceof Date) {
        payload.dateOfBirth = payload.dateOfBirth.toISOString().split('T')[0];
    }
    if (typeof payload.skills === 'string') {
        payload.skills = payload.skills.split(',').map(s => s.trim()).filter(Boolean);
    }
    await usersService.updateUserProfile(props.user.id, payload);
    emit('success');
    showToast({ severity: 'success', summary: 'Success', detail: 'Profile updated successfully.', life: 3000 });
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Failed to update profile.';
    showToast({ severity: 'error', summary: 'Error', detail: error.value, life: 3000 });
  } finally {
    loading.value = false;
  }
}

function handleClose() {
  emit('close');
}
</script>

<template>
  <Drawer :visible="visible" position="right" class="profile-drawer" :style="{ width: '500px' }" @update:visible="handleClose">
    <template #header>
      <h3>Edit User Profile</h3>
    </template>
    <div class="drawer-content p-4">
      <form @submit.prevent="handleSubmit">
        <!-- <div class="form-group">
          <label>Address Landmark</label>
          <InputText v-model="form.addressLandmark" class="w-full" />
        </div> -->
        <div class="form-group">
          <label>Address</label>
          <InputText v-model="form.address" class="w-full" />
        </div>
        <div class="form-group">
          <label>City</label>
          <InputText v-model="form.city" class="w-full" />
          <div v-if="fieldErrors.city" class="field-error">{{ fieldErrors.city }}</div>
        </div>
        
        <div class="form-group">
          <label>State</label>
          <InputText v-model="form.state" class="w-full" />
          <div v-if="fieldErrors.state" class="field-error">{{ fieldErrors.state }}</div>
        </div>
        <div class="form-group">
          <label>Country</label>
          <InputText v-model="form.country" class="w-full" />
          <div v-if="fieldErrors.country" class="field-error">{{ fieldErrors.country }}</div>
        </div>
        <div class="form-group">
          <label>Pin Code</label>
          <InputText v-model="form.postalCode" class="w-full" />
          <div v-if="fieldErrors.postalCode" class="field-error">{{ fieldErrors.postalCode }}</div>
        </div>
        <div class="form-group">
          <label>Date of Birth</label>
          <DatePicker v-model="form.dateOfBirth" class="w-full" dateFormat="dd/mm/yy" :show-icon="true" />
          <div v-if="form.dateOfBirth" class="text-xs text-gray-500 mt-1">Selected: {{ formatDateDDMMYYYY(form.dateOfBirth) }}</div>
        </div>
        <!-- <div class="form-group">
          <label>Profile Image URL</label>
          <InputText v-model="form.profileImageSrc" class="w-full" />
        </div> -->
        <div class="form-group">
          <label>Bio</label>
          <Textarea v-model="form.bio" class="w-full" rows="3" />
        </div>
        <div class="form-group">
          <label>Job Title</label>
          <InputText v-model="form.jobTitle" class="w-full" />
        </div>
        <div class="form-group">
          <label>Desired Salary</label>
          <InputNumber v-model="form.desiredSalary" class="w-full" />
        </div>
        <div class="form-group">
          <label>Years of Experience</label>
          <InputNumber v-model="form.experienceYears" class="w-full" />
        </div>
        <div class="form-group">
          <label>Language</label>
          <InputText v-model="form.language" class="w-full" />
        </div>
        <div class="form-group">
          <label>Skills (comma-separated)</label>
          <InputText v-model="form.skills" class="w-full" />
        </div>
        <div class="grid grid-cols-3 gap-4">
            <div class="form-group flex items-center">
                <Checkbox v-model="form.phoneNotification" :binary="true" input-id="phoneNotif" />
                <label for="phoneNotif" class="ml-2">Phone Notifications</label>
            </div>
            <div class="form-group flex items-center">
                <Checkbox v-model="form.emailNotification" :binary="true" input-id="emailNotif" />
                <label for="emailNotif" class="ml-2">Email Notifications</label>
            </div>
            <div class="form-group flex items-center">
                <Checkbox v-model="form.isPublic" :binary="true" input-id="isPublic" />
                <label for="isPublic" class="ml-2">Public Profile</label>
            </div>
        </div>
        <div v-if="error" class="error-message p-error">{{ error }}</div>
        <div class="drawer-footer">
          <Button label="Cancel" severity="secondary" @click="handleClose" :disabled="loading" />
          <Button label="Update Profile" type="submit" :loading="loading" />
        </div>
      </form>
    </div>
  </Drawer>
</template>

<style scoped>
.form-group { margin-bottom: 1rem; }
.drawer-footer { display: flex; justify-content: flex-end; gap: 1rem; margin-top: 1.5rem; }
.error-message { margin-top: 1rem; }
+.field-error { color: #e24c4b; font-size: 0.92em; margin-top: 0.25rem; }
</style> 