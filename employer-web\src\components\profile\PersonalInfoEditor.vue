<template>
  <Dialog 
    v-model:visible="localVisible" 
    :header="isEditing ? 'Edit Personal Information' : 'Personal Information'"
    :modal="true"
    :closable="true"
    class="personal-info-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="personal-info-form">
      <div class="form-grid">
        <div class="form-group">
          <label for="firstName">First Name <span class="required">*</span></label>
          <InputText
            id="firstName"
            v-model="formData.firstName"
            :class="{ 'p-invalid': errors.firstName }"
            placeholder="Enter your first name"
            @blur="validateField('firstName')"
          />
          <small v-if="errors.firstName" class="p-error">{{ errors.firstName }}</small>
        </div>

        <div class="form-group">
          <label for="lastName">Last Name <span class="required">*</span></label>
          <InputText
            id="lastName"
            v-model="formData.lastName"
            :class="{ 'p-invalid': errors.lastName }"
            placeholder="Enter your last name"
            @blur="validateField('lastName')"
          />
          <small v-if="errors.lastName" class="p-error">{{ errors.lastName }}</small>
        </div>

        <div class="form-group">
          <label for="email">Email <span class="required">*</span></label>
          <InputText
            id="email"
            v-model="formData.email"
            type="email"
            :class="{ 'p-invalid': errors.email }"
            placeholder="Enter your email"
            disabled
          />
          <small class="form-help">Email cannot be changed</small>
        </div>

        <div class="form-group">
          <label for="phone">Phone Number <span class="required">*</span></label>
          <div class="phone-input-container">
            <span class="phone-prefix">+91</span>
            <InputText
              id="phone"
              v-model="formData.phone"
              :class="{ 'p-invalid': errors.phone }"
              placeholder="Enter 10 digit number"
              maxlength="10"
              @input="validatePhoneNumber"
              @blur="validateField('phone')"
            />
          </div>
          <small v-if="errors.phone" class="p-error">{{ errors.phone }}</small>
        </div>

        <div class="form-group">
          <label for="dateOfBirth">Date of Birth</label>
          <Calendar
            id="dateOfBirth"
            v-model="formData.dateOfBirth"
            :showIcon="true"
            :maxDate="maxDate"
            dateFormat="dd/mm/yy"
            placeholder="Select date"
          />
          <small v-if="errors.dateOfBirth" class="p-error">{{ errors.dateOfBirth }}</small>
        </div>
      </div>
      <!-- Map and address form for personal info -->
      <Location 
        v-model="locationData" 
        :errors="errors" 
        :required="{ address: true, city: true, state: true, postalCode: true, country: false }"
        @validate="validateLocationField" 
      />
    </form>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          label="Save Changes"
          :loading="isSaving"
          :disabled="!isFormValid || isSaving"
          class="save-btn"
          v-tooltip="!isFormValid ? 'Please fill all required fields correctly' : ''"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Calendar from 'primevue/calendar'
import Button from 'primevue/button'
import alertManager from '@/utils/alertManager'
import Location from '@/components/Location.vue'
import { isAlphaOnly } from '@/utils/apiHelpers'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  profileData: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)
const errors = ref({})

const formData = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  dateOfBirth: null
})

const locationData = ref({
  address: '',
  city: '',
  state: '',
  country: '',
  postalCode: '',
  latitude: null,
  longitude: null
})

// Create a computed property for two-way binding with the visible prop
const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

const maxDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 16) // Minimum age 16
  return date
})

// Computed property to check if form is valid
const isFormValid = computed(() => {
  // Required fields validation
  if (!formData.value.firstName?.trim()) return false
  if (!formData.value.lastName?.trim()) return false
  if (!formData.value.email?.trim()) return false
  if (!formData.value.phone?.trim()) return false
  
  // Phone number format validation
  if (!/^\d{10}$/.test(formData.value.phone)) return false
  
  // Required location fields
  if (!locationData.value.address?.trim()) return false
  if (!locationData.value.city?.trim()) return false
  if (!locationData.value.state?.trim()) return false
  if (!locationData.value.postalCode?.trim()) return false
  
  // Location format validations
  if (!isAlphaOnly(locationData.value.city)) return false
  if (!isAlphaOnly(locationData.value.state)) return false
  if (!isAlphaOnly(locationData.value.country)) return false
  if (!/^\d{6}$/.test(locationData.value.postalCode)) return false
  
  return true
})

// Watch for prop changes
watch(() => props.profileData, (newData) => {
  if (newData) {
    // Handle phone number - remove +91 prefix if present
    let phoneNumber = newData.phone || newData.phoneNumber || ''
    if (phoneNumber.startsWith('+91')) {
      phoneNumber = phoneNumber.substring(3)
    }
    
    formData.value = {
      firstName: newData.firstName || '',
      lastName: newData.lastName || '',
      email: newData.email || '',
      phone: phoneNumber,
      dateOfBirth: newData.dateOfBirth ? new Date(newData.dateOfBirth) : null
    }
    locationData.value = {
      address: newData.address || '',
      city: newData.city || '',
      state: newData.state || '',
      country: newData.country || 'India',
      postalCode: newData.postalCode || '',
      latitude: newData.latitude || null,
      longitude: newData.longitude || null
    }
  }
}, { immediate: true, deep: true })

const validateField = (fieldName) => {
  switch (fieldName) {
    case 'firstName':
      if (!formData.value.firstName?.trim()) {
        errors.value.firstName = 'First name is required'
      } else if (!isAlphaOnly(formData.value.firstName)) {
        errors.value.firstName = 'First name should contain only letters and spaces'
      } else {
        delete errors.value.firstName
      }
      break
      
    case 'lastName':
      if (!formData.value.lastName?.trim()) {
        errors.value.lastName = 'Last name is required'
      } else if (!isAlphaOnly(formData.value.lastName)) {
        errors.value.lastName = 'Last name should contain only letters and spaces'
      } else {
        delete errors.value.lastName
      }
      break
      
    case 'phone':
      if (!formData.value.phone?.trim()) {
        errors.value.phone = 'Phone number is required'
      } else if (!/^\d{10}$/.test(formData.value.phone)) {
        errors.value.phone = 'Phone number should be exactly 10 digits'
      } else {
        delete errors.value.phone
      }
      break
  }
}

const validateLocationField = (fieldName) => {
  switch (fieldName) {
    case 'address':
      if (!locationData.value.address?.trim()) {
        errors.value.address = 'Address is required'
      } else {
        delete errors.value.address
      }
      break
      
    case 'city':
      if (!locationData.value.city?.trim()) {
        errors.value.city = 'City is required'
      } else if (!isAlphaOnly(locationData.value.city)) {
        errors.value.city = 'City should contain only letters and spaces'
      } else {
        delete errors.value.city
      }
      break
      
    case 'state':
      if (!locationData.value.state?.trim()) {
        errors.value.state = 'State is required'
      } else if (!isAlphaOnly(locationData.value.state)) {
        errors.value.state = 'State should contain only letters and spaces'
      } else {
        delete errors.value.state
      }
      break
      
    case 'postalCode':
      if (!locationData.value.postalCode?.trim()) {
        errors.value.postalCode = 'PIN code is required'
      } else if (!/^\d{6}$/.test(locationData.value.postalCode)) {
        errors.value.postalCode = 'PIN code should be exactly 6 digits'
      } else {
        delete errors.value.postalCode
      }
      break
  }
}

const validateForm = () => {
  errors.value = {}
  
  // Validate all required fields
  validateField('firstName')
  validateField('lastName')
  validateField('phone')
  validateLocationField('address')
  validateLocationField('city')
  validateLocationField('state')
  validateLocationField('postalCode')
  
  // Email validation (should always be valid since it's disabled)
  if (!formData.value.email?.trim()) {
    errors.value.email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email)) {
    errors.value.email = 'Please enter a valid email address'
  }
  
  return Object.keys(errors.value).length === 0
}

const validatePhoneNumber = () => {
  // Remove any non-digit characters
  formData.value.phone = formData.value.phone.replace(/\D/g, '')
  
  // Limit to 10 digits
  if (formData.value.phone.length > 10) {
    formData.value.phone = formData.value.phone.slice(0, 10)
  }
  
  // Validate format
  validateField('phone')
}

const handleSubmit = async () => {
  // Clear previous errors
  errors.value = {}
  
  // Validate form
  const isValid = validateForm()
  
  if (!isValid) {
    // Show error message to user
    alertManager.showError('Validation Error', 'Please fill all required fields correctly before saving.')
    return
  }
  
  isSaving.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const updatedData = {
      ...formData.value,
      ...locationData.value,
      phoneNumber: formData.value.phone ? `+91${formData.value.phone}` : '',
      dateOfBirth: formData.value.dateOfBirth ? formData.value.dateOfBirth.toISOString().split('T')[0] : null
    }
    
    emit('save', updatedData)
    emit('update:visible', false)
    
    // Success alert removed - parent component will handle it
  } catch (error) {
    alertManager.showError('Error', 'Failed to update personal information. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  errors.value = {}
}
</script>

<style scoped>
.personal-info-dialog {
  border-radius: 12px !important;
}

.personal-info-form {
  padding: 1rem 0;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-calendar {
  width: 100% !important;
}

.form-help {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
}

.phone-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.phone-prefix {
  position: absolute;
  left: 12px;
  color: var(--text-color-secondary);
  font-weight: 500;
  z-index: 1;
  pointer-events: none;
}

.phone-input-container input {
  padding-left: 40px !important;
}

.required {
  color: #e53935;
  font-weight: bold;
}
</style>