<template>
  <Toast />
  <div class="login-container">
    <div class="theme-toggle-wrapper">
      <ThemeToggle />
    </div>
    
    <div class="login-card">
      <!-- Company Logo -->
      <div class="logo-container">
        <div class="company-logo">
          <img src="../assets/jd-icon.png" alt="Logo" width="60" height="60" />
        </div>
      </div>

      <h1>{{ t('auth.welcomeBack') }}</h1>
      <p class="subtitle">{{ t('auth.signInToAccount') }}</p>

      <!-- Google Login Button -->
      <!-- <Button 
        @click="handleGoogleLogin" 
        class="google-login-button"
        severity="secondary"
        outlined
        size="large"
      >
        <i class="pi pi-google google-icon"></i>
        {{ t('auth.continueWithGoogle') }}
      </Button> -->

      <!-- Divider -->
      <!-- <Divider align="center" type="solid" class="custom-divider">
        <span class="divider-text">{{ t('auth.orLoginWithEmail') }}</span>
      </Divider> -->

      <!-- Email/OTP Form -->
      <form @submit.prevent="handleEmailSubmit" class="login-form">
        <!-- Email Input Phase -->
        <div v-if="!otpSent" class="form-group">
          <label for="email" class="input-label">{{ t('auth.email') }}</label>
          <InputText
            id="email"
            v-model="email"
            type="email"
            :placeholder="t('auth.enterEmail')"
            class="custom-input"
            :class="{ 'p-invalid': emailError }"
            required
            :disabled="isLoading"
          />
          <small v-if="emailError" class="p-error">{{ emailError }}</small>
          
          <Button 
            type="submit" 
            class="primary-button"
            :loading="isLoading"
            :label="isLoading ? t('auth.sendingOTP') : t('auth.sendOTP')"
            size="large"
          />
        </div>

        <!-- OTP Input Phase -->
        <div v-if="otpSent" class="form-group">
          <label class="input-label">{{ t('auth.enterOTP') }}</label>
          <Message severity="info" class="custom-message">
            {{ t('auth.otpSentTo', { email }) }}
          </Message>
          
          <div class="otp-container">
            <InputText
              v-for="(digit, index) in otpDigits"
              :key="index"
              :ref="el => otpInputs[index] = el"
              v-model="otpDigits[index]"
              @input="handleOtpInput(index, $event)"
              @keydown="handleOtpKeydown(index, $event)"
              @paste="handleOtpPaste($event)"
              class="otp-input"
              maxlength="1"
              :class="{ 'filled': otpDigits[index] }"
              inputmode="numeric"
              pattern="[0-9]*"
            />
          </div>

          <div class="otp-actions">
            <Button 
              type="button" 
              @click="resendOtp"
              severity="secondary"
              outlined
              :disabled="resendCooldown > 0"
              :label="resendCooldown > 0 ? t('auth.resendIn', { seconds: resendCooldown }) : t('auth.resendOTP')"
              class="flex-1"
            />

            <Button 
              type="button" 
              @click="handleLogin"
              :disabled="!isOtpComplete || isLoading"
              :loading="isLoading"
              :label="isLoading ? t('auth.verifying') : t('common.login')"
              class="flex-1 primary-button"
            />
          </div>

          <Button 
            type="button" 
            @click="goBackToEmail"
            text
            severity="secondary"
            class="back-button"
          >
            <i class="pi pi-arrow-left"></i>
            {{ t('auth.backToEmail') }}
          </Button>
        </div>
      </form>

      <div class="form-footer" v-if="!otpSent">
        <!-- <a href="#" class="forgot-link">
          <i class="pi pi-question-circle"></i>
          {{ t('auth.forgotPassword') }}
        </a> -->
        <!-- <p class="signup-text">
          {{ t('auth.noAccount') }}
          <a href="#" class="signup-link">{{ t('common.signup') }}</a>
        </p> -->
      </div>

      <!-- Error Display (Bottom failure text) -->
      <Message 
        v-if="authStore.error" 
        severity="error" 
        class="error-message"
        @close="authStore.clearError"
      >
        {{ authStore.error }}
      </Message>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Divider from 'primevue/divider'
import Message from 'primevue/message'
import ThemeToggle from '@/components/ThemeToggle.vue'
import alertManager from '@/utils/alertManager'
import { api } from '@/api'
import Toast from 'primevue/toast';
import { useToast } from 'primevue/usetoast';

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()
const toast = useToast();

const email = ref('')
const emailError = ref('')
const otpDigits = ref(['', '', '', '', '', ''])
const otpInputs = ref([])
const otpSent = ref(false)
const resendCooldown = ref(0)
const isLoading = ref(false)

const isOtpComplete = computed(() => {
  const isComplete = otpDigits.value.every(digit => digit.length === 1);

  if (isComplete) {
    nextTick(() => {
      handleLogin();
    })
  }

  return isComplete;
})

const handleGoogleLogin = () => {
  console.log('Google login clicked')
  alertManager.showInfo('Coming Soon', 'Google login will be available soon!')
}

const handleEmailSubmit = async () => {
  if (otpSent.value) return

  emailError.value = ''
  
  if (!email.value) {
    emailError.value = t('auth.invalidEmail')
    return
  }

  try {
    isLoading.value = true;
    const result = await api.auth.sendOtp(email.value)
    
    if (result.success) {
      otpSent.value = true
      startResendCooldown()
      
      // Focus first OTP input
      nextTick(() => {
        if (otpInputs.value[0]) {
          otpInputs.value[0].$el.focus()
        }
      })
    }
    else {
      toast.add({ severity: 'error', summary: t('common.error'), detail: (result.message?.[0] || result.errorMessage), life: 3000 });
    }

    isLoading.value = false;
  } catch (error) {
    toast.add({ severity: 'error', summary: t('common.error'), detail: (result.message?.[0] || result.errorMessage), life: 3000 });
    emailError.value = error.message || t('auth.loginFailed')
    isLoading.value = false;
  }
}

const handleOtpInput = (index, event) => {
  const value = event.target.value
  
  // Only allow numeric input
  if (value && !/^\d$/.test(value)) {
    event.target.value = ''
    otpDigits.value[index] = ''
    return
  }

  if (value && index < 5) {
    // Move to next input
    nextTick(() => {
      if (otpInputs.value[index + 1]) {
        otpInputs.value[index + 1].$el.focus()
      }
    })
  }
}

const handleOtpKeydown = (index, event) => {
  // Only allow numeric keys, backspace, delete, tab, and arrow keys
  const allowedKeys = [
    'Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 
    'ArrowUp', 'ArrowDown', 'Home', 'End'
  ]
  
  const isNumeric = /^[0-9]$/.test(event.key)
  const isAllowedKey = allowedKeys.includes(event.key)
  
  if (!isNumeric && !isAllowedKey) {
    event.preventDefault()
    return
  }

  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    // Move to previous input on backspace
    nextTick(() => {
      if (otpInputs.value[index - 1]) {
        otpInputs.value[index - 1].$el.focus()
      }
    })
  }
}

const handleOtpPaste = (event) => {
  event.preventDefault()
  const pastedData = event.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6)

  for (let i = 0; i < Math.min(pastedData.length, 6); i++) {
    otpDigits.value[i] = pastedData[i]
  }
  
  // Focus the next empty input or the last input
  const nextEmptyIndex = otpDigits.value.findIndex(digit => !digit)
  const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : 5
  
  nextTick(() => {
    if (otpInputs.value[focusIndex]) {
      otpInputs.value[focusIndex].$el.focus()
    }
  })
}

const clearOtpInputs = () => {
  otpDigits.value = ['', '', '', '', '', '']
  nextTick(() => {
    if (otpInputs.value[0]) {
      otpInputs.value[0].$el.focus()
    }
  })
}

const handleLogin = async () => {
  if (!isOtpComplete.value) return

  const otpCode = otpDigits.value.join('')

  try {
    isLoading.value = true;
    // First verify OTP
    const result = await api.auth.verifyOtp(email.value, otpCode)
    if (result.success) {
      // Check if user role is employer
      const userRole = result.data?.user?.role;
      
      if (userRole !== 'employer') {
        // Clear OTP inputs for unauthorized user
        clearOtpInputs()
        
        // Show unauthorized alert with contact number
        toast.add({ 
          severity: 'error',
          summary: t('common.unAuthError'), 
          detail: 'You are not authorized to use this app. Please contact +91 8888890974 for assistance.',
          life: 3000 
        });
        
        // Clear auth data since user is not authorized
        authStore.logout()
        isLoading.value = false;
      }
      authStore.afterLogin(result.data);
      
      // User is authorized, proceed to dashboard
      router.push('/employer/dashboard')
    }
    else {
      toast.add({ severity: 'error', summary: t('common.error'), detail: result.errorMessage, life: 3000 });
    }
    isLoading.value = false;
  } catch (error) {
    console.error('Login error:', error)
    
    isLoading.value = false;
    // Clear OTP inputs on any authorization failure
    clearOtpInputs()
    
    // Show appropriate alert dialog based on error
    if (error.message.includes('expired')) {
      await alertManager.showOtpExpired({
        confirmText: t('auth.resendOTP')
      })
      // Auto-trigger resend OTP
      resendOtp()
    } else if (error.message.includes('Invalid OTP')) {
      await alertManager.showInvalidOtp({
        confirmText: t('auth.tryAgain')
      })
    } else {
      // Generic error
      alertManager.showError('Login Failed', error.message || 'Please try again.')
    }
  }
}

const resendOtp = async () => {
  if (resendCooldown.value > 0) return

  try {
    otpSent.value = false;
    await handleEmailSubmit()
    startResendCooldown()
    
    // Clear OTP inputs when resending
    clearOtpInputs()
    
    // Show success message
    toast.add({ severity: 'success', summary: t('OTP Sent'), detail: 'A new OTP has been sent to your email.', life: 3000 });
    // alertManager.showSuccess('OTP Sent', 'A new OTP has been sent to your email.')
  } catch (error) {
    alertManager.showError('Failed to Send OTP', error.message || 'Please try again.')
  }
}

const startResendCooldown = () => {
  resendCooldown.value = 30
  const interval = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(interval)
    }
  }, 1000)
}

const goBackToEmail = () => {
  otpSent.value = false
  clearOtpInputs()
  resendCooldown.value = 0
  emailError.value = ''
  authStore.clearError()
}
</script>

<style scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--login-bg-start) 0%, var(--login-bg-end) 100%);
  padding: 1rem;
  transition: background var(--transition-duration) ease;
  overflow: hidden;
}

.theme-toggle-wrapper {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
}

.login-card {
  background: var(--surface-card);
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 420px;
  transition: all var(--transition-duration) ease;
  position: relative;
  z-index: 1;
  border: 1px solid var(--surface-border);
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.company-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border-radius: 50%;
  background: var(--highlight-bg);
}

.company-logo-icon {
  font-size: 3rem !important;
  color: var(--primary-color) !important;
}

h1 {
  text-align: center;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 700;
}

.subtitle {
  text-align: center;
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
  font-size: 0.95rem;
}

.google-login-button {
  width: 100% !important;
  margin-bottom: 1.5rem !important;
  height: 48px !important;
  border-radius: 8px !important;
}

.google-icon {
  margin-right: 0.75rem !important;
}

.custom-divider {
  margin: 1.5rem 0 !important;
}

.divider-text {
  padding: 0 1rem;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
  background: var(--surface-card);
  font-weight: 500;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.custom-input {
  width: 100% !important;
  height: 48px !important;
  border-radius: 8px !important;
  font-size: 1rem !important;
}

.custom-message {
  margin-bottom: 1rem !important;
  border-radius: 8px !important;
}

.otp-container {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin: 1rem 0 1.5rem 0;
}

.otp-input {
  width: 48px !important;
  height: 48px !important;
  text-align: center !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.otp-input.filled {
  border-color: var(--primary-color) !important;
  background-color: var(--highlight-bg) !important;
  transform: scale(1.05);
}

/* Ensure only numbers can be entered */
.otp-input::-webkit-outer-spin-button,
.otp-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.otp-input[type=number] {
  -moz-appearance: textfield;
}

.otp-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.flex-1 {
  flex: 1;
}

.primary-button {
  height: 48px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  width: 100% !important;
  margin-top: 0.5rem !important;
}

.back-button {
  align-self: center;
  margin-top: 1rem !important;
  gap: 0.5rem !important;
}

.form-footer {
  margin-top: 2rem;
  text-align: center;
}

.forgot-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.forgot-link:hover {
  text-decoration: underline;
}

.signup-text {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.signup-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.signup-link:hover {
  text-decoration: underline;
}

.error-message {
  margin-top: 1rem !important;
  border-radius: 8px !important;
}

@media (max-width: 480px) {
  .login-card {
    padding: 2rem;
    margin: 1rem;
  }
  
  .otp-container {
    gap: 0.5rem;
  }
  
  .otp-input {
    width: 40px !important;
    height: 40px !important;
    font-size: 1rem !important;
  }
}
</style>