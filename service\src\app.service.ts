import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  getAdmin(): string {
    try {
      // Read the Vue.js built index.html file
      const indexPath = join(__dirname, '..', 'public/admin/dist', 'index.html');
      this.logger.debug(`Reading admin index file from: ${indexPath}`);
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      this.logger.error('Error reading admin index.html:', error);
      return 'Vue.js application not found. Please build the frontend application first.';
    }
  }

  getEmployer(): string {
    try {
      const indexPath = join(__dirname, '..', 'public/employer/dist', 'index.html');
      this.logger.debug(`Reading employer index file from: ${indexPath}`);
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      this.logger.error('Error reading employer index.html:', error);
      return 'Application not found.';
    }
  }

  getJobseeker(): string {
    try {
      const indexPath = join(__dirname, '..', 'public/jobseeker/dist', 'index.html');
      this.logger.debug(`Reading jobseeker index file from: ${indexPath}`);
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      this.logger.error('Error reading jobseeker index.html:', error);
      return 'Application not found.';
    }
  }

  getWelcome(): string {
    try {
      const indexPath = join(__dirname, '..', 'public/welcome/dist', 'index.html');
      this.logger.debug(`Reading welcome index file from: ${indexPath}`);
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      this.logger.error('Error reading welcome index.html:', error);
      return 'Application not found.';
    }
  }
}
