<script setup>
import { reactive, ref, watch } from 'vue';
import Drawer from 'primevue/drawer';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import InputNumber from 'primevue/inputnumber';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import { companiesService } from '@/api/services/companiesService';

const props = defineProps({
  visible: Boolean,
  user: Object
});

const emit = defineEmits(['close', 'success']);

const loading = ref(false);
const error = ref('');
const initialLoading = ref(false);

const form = reactive({
  name: '',
  description: '',
  website: '',
  logo: '',
  address: '',
  city: '',
  state: '',
  country: '',
  postalCode: '',
  phone: '',
  email: '',
  industry: '',
  companySize: '',
  foundedYear: null,
  registrationNumber: '',
  taxId: '',
  isActive: true
});

async function fetchCompanyData(userId) {
  if (!userId) return;
  initialLoading.value = true;
  error.value = '';
  try {
    const response = await companiesService.getCompanyByUserId(userId);
    if (response && response.data) {
      // Map all fields from the backend response
      Object.assign(form, {
        name: response.data.name || '',
        description: response.data.description || '',
        website: response.data.website || '',
        logo: response.data.logo || '',
        address: response.data.address || '',
        city: response.data.city || '',
        state: response.data.state || '',
        country: response.data.country || '',
        postalCode: response.data.postalCode || '',
        phone: response.data.phone || '',
        email: response.data.email || '',
        industry: response.data.industry || '',
        companySize: response.data.companySize || '',
        foundedYear: response.data.foundedYear || null,
        registrationNumber: response.data.registrationNumber || '',
        taxId: response.data.taxId || '',
        isActive: response.data.isActive !== false
      });
    } else {
      // If no company exists, initialize with user's email if available
      form.name = `${props.user.firstName}'s Company` || 'New Company';
      form.email = props.user.email || '';
    }
  } catch (err) {
    error.value = "Failed to load company data. It may not exist yet.";
  } finally {
    initialLoading.value = false;
  }
}

watch(() => props.visible, (isVisible) => {
  if (isVisible && props.user?.id) {
    // Reset form before fetching new data
    Object.keys(form).forEach(key => form[key] = (typeof form[key] === 'number' ? null : key === 'isActive' ? true : ''));
    fetchCompanyData(props.user.id);
  }
}, { immediate: true });

async function handleSubmit() {
  if (!props.user) return;
  error.value = '';
  loading.value = true;
  try {
    // Only send fields that are present in the form and DTO
    const payload = { ...form };
    await companiesService.updateCompanyByUserId(props.user.id, payload);
    emit('success');
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Failed to update organization.';
  } finally {
    loading.value = false;
  }
}

function handleClose() {
  emit('close');
}
</script>

<template>
  <Drawer :visible="visible" position="right" class="organization-drawer" :style="{ width: '600px' }" @update:visible="handleClose">
    <template #header>
      <h3>Edit Organization Details</h3>
    </template>
    <div class="drawer-content p-4" v-if="initialLoading">
        <div class="flex justify-center items-center h-full">
            <ProgressSpinner />
        </div>
    </div>
    <div class="drawer-content p-4" v-else>
      <form @submit.prevent="handleSubmit">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-group">
                <label>Organization Name *</label>
                <InputText v-model="form.name" class="w-full" required />
            </div>
            <div class="form-group">
                <label>Industry</label>
                <InputText v-model="form.industry" class="w-full" />
            </div>
            <div class="form-group col-span-2">
                <label>Description</label>
                <Textarea v-model="form.description" class="w-full" rows="3" />
            </div>
            <div class="form-group">
                <label>Website</label>
                <InputText v-model="form.website" class="w-full" />
            </div>
            <div class="form-group">
                <label>Email</label>
                <InputText v-model="form.email" type="email" class="w-full" />
            </div>
            <div class="form-group">
                <label>Phone</label>
                <InputText v-model="form.phone" class="w-full" />
            </div>
            <div class="form-group">
                <label>Address</label>
                <InputText v-model="form.address" class="w-full" />
            </div>
            <div class="form-group">
                <label>City</label>
                <InputText v-model="form.city" class="w-full" />
            </div>
            <div class="form-group">
                <label>State</label>
                <InputText v-model="form.state" class="w-full" />
            </div>
            <div class="form-group">
                <label>Country</label>
                <InputText v-model="form.country" class="w-full" />
            </div>
            <div class="form-group">
                <label>Postal Code</label>
                <InputText v-model="form.postalCode" class="w-full" />
            </div>
            <div class="form-group">
                <label>Founded Year</label>
                <InputNumber v-model="form.foundedYear" class="w-full" :use-grouping="false" />
            </div>
            <div class="form-group">
                <label>Registration Number</label>
                <InputText v-model="form.registrationNumber" class="w-full" />
            </div>
            <div class="form-group">
                <label>Tax ID</label>
                <InputText v-model="form.taxId" class="w-full" />
            </div>
             <div class="form-group">
                <label>Logo URL</label>
                <InputText v-model="form.logo" class="w-full" />
            </div>
            <div class="form-group">
                <label>Company Size</label>
                <InputText v-model="form.companySize" class="w-full" />
            </div>
            <div class="form-group flex items-center">
                <label for="isActive" class="mr-2">Active</label>
                <input id="isActive" type="checkbox" v-model="form.isActive" />
            </div>
        </div>
        <div v-if="error" class="error-message p-error mt-4">{{ error }}</div>
        <div class="drawer-footer">
          <Button label="Cancel" severity="secondary" @click="handleClose" :disabled="loading" />
          <Button label="Update Organization" type="submit" :loading="loading" />
        </div>
      </form>
    </div>
  </Drawer>
</template>

<style scoped>
.form-group { margin-bottom: 1rem; }
.drawer-footer { display: flex; justify-content: flex-end; gap: 1rem; margin-top: 1.5rem; border-top: 1px solid #dee2e6; padding-top: 1rem; }
.error-message { margin-top: 1rem; }
</style> 