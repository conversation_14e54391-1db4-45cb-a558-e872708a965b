import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { JobService } from '../jobs/services/job.service';

@Injectable()
export class SchedulerService {
  private readonly logger = new Logger(SchedulerService.name);

  constructor(private readonly jobService: JobService) {}

  @Cron(CronExpression.EVERY_YEAR)
  async handlePendingJobs() {
    try {
      await this.jobService.activatePendingJobs();
      this.logger.debug('Successfully processed pending jobs');
    } catch (error) {
      this.logger.error('Error processing pending jobs:', error);
    }
  }
}
