<template>
  <Dialog 
    :visible="localVisible"
    @update:visible="localVisible = $event"
    header="Edit Organisation Information"
    :modal="true"
    :closable="true"
    class="professional-info-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="professional-info-form">
      <div class="form-grid">
        <div class="form-group full-width">
          <label for="name">Organisation Name <span class="required">*</span></label>
          <InputText
            id="name"
            v-model="formData.name"
            :class="{ 'p-invalid': errors.name }"
            placeholder="Enter your organization name"
            @blur="validateField('name')"
          />
          <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
        </div>

        <div class="form-group full-width">
          <label for="description">About Organization</label>
          <Textarea
            id="description"
            v-model="formData.description"
            rows="4"
            placeholder="Write a brief description about your organization..."
            :maxlength="500"
          />
          <small class="char-count">{{ (formData.description || '').length }}/500 characters</small>
        </div>

        <div class="form-group">
          <label for="foundedYear">Established On</label>
          <Calendar
            id="foundedYear"
            v-model="formData.foundedYear"
            dateFormat="dd/mm/yy"
            :showIcon="true"
            :yearRange="'1900:' + new Date().getFullYear()"
            placeholder="Select founding date"
          />
        </div>

        <div class="form-group">
          <label for="industry">Industry <span class="required">*</span></label>
          <Select
            id="industry"
            v-model="formData.industry"
            :options="industryOptions"
            optionLabel="name"
            optionValue="id"
            :class="{ 'p-invalid': errors.industry }"
            placeholder="Select Industry"
            showClear
            @change="validateField('industry')"
          />
          <small v-if="errors.industry" class="p-error">{{ errors.industry }}</small>
        </div>

        <div class="form-group">
          <label for="companySize">Organisation size <span class="required">*</span></label>
          <InputNumber
            id="companySize"
            v-model="formData.companySize"
            :class="{ 'p-invalid': errors.companySize }"
            placeholder="Total employee"
            :min="1"
            @blur="validateField('companySize')"
          />
          <small v-if="errors.companySize" class="p-error">{{ errors.companySize }}</small>
        </div>

        <div class="form-group">
          <label for="website">Website</label>
          <InputText
            id="website"
            v-model="formData.website"
            placeholder="e.g., https://www.example.com"
            :class="{ 'p-invalid': errors.website }"
            @blur="validateField('website')"
          />
          <small v-if="errors.website" class="p-error">{{ errors.website }}</small>
        </div>

        <div class="form-group">
          <label for="registrationNumber">Registration Number</label>
          <InputText
            id="registrationNumber"
            v-model="formData.registrationNumber"
            placeholder="e.g., 1234567890"
          />
        </div>

        <div class="form-group">
          <label for="taxId">Tax ID</label>
          <InputText
            id="taxId"
            v-model="formData.taxId"
            placeholder="e.g., 123-45-6789"
          />
        </div>
      </div>
      <Location 
        v-model="formData.location" 
        :errors="errors" 
        :required="{ address: true, city: true, state: true, postalCode: true, country: false }"
        @validate="validateLocationField" 
      />
    </form>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          label="Save Changes"
          :loading="isSaving"
          :disabled="!isFormValid || isSaving"
          class="save-btn"
          v-tooltip="!isFormValid ? 'Please fill all required fields correctly' : ''"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import Select from 'primevue/select'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import Calendar from 'primevue/calendar'
import alertManager from '@/utils/alertManager'
import Location from '@/components/Location.vue'
import { useAuthStore } from '@/stores/auth'
import { companiesService } from '@/api/services/companiesService'
import { isValidUrl, isAlphaOnly, isNumericOnly } from '@/utils/apiHelpers'

const authStore = useAuthStore()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  profileData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)

const formData = ref({
  name: '',
  description: '',
  industry: '',
  companySize: null,
  website: '',
  foundedYear: null,
  registrationNumber: '',
  taxId: '',
  location: {
    address: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
    latitude: null,
    longitude: null
  }
})

const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

const industryOptions = authStore.industries

const errors = ref({})

// Computed property to check if form is valid
const isFormValid = computed(() => {
  // Required fields validation
  if (!formData.value.name?.trim()) return false
  if (!/^[A-Za-z0-9\s\-&.,()]+$/.test(formData.value.name)) return false
  if (!formData.value.industry) return false
  if (!formData.value.companySize || formData.value.companySize < 1) return false
  
  // Required location fields
  if (!formData.value.location.address?.trim()) return false
  if (!formData.value.location.city?.trim()) return false
  if (!formData.value.location.state?.trim()) return false
  if (!formData.value.location.postalCode?.trim()) return false
  
  // Location format validations
  if (!isAlphaOnly(formData.value.location.city)) return false
  if (!isAlphaOnly(formData.value.location.state)) return false
  if (!isAlphaOnly(formData.value.location.country)) return false
  if (!/^\d{6}$/.test(formData.value.location.postalCode)) return false
  
  // Website validation (if provided)
  if (formData.value.website && !isValidUrl(formData.value.website)) return false
  
  return true
})

// Watch for prop changes
watch(() => props.profileData, (newData) => {
  if (newData) {
    formData.value = {
      name: newData.company?.name || '',
      description: newData.company?.description || '',
      industry: newData.company?.industry || '',
      companySize: newData.company?.companySize || null,
      website: newData.company?.website || '',
      foundedYear: newData.company?.foundedYear ? new Date(newData.company.foundedYear) : null,
      registrationNumber: newData.company?.registrationNumber || '',
      taxId: newData.company?.taxId || '',
      location: {
        address: newData.company?.address || '',
        city: newData.company?.city || '',
        state: newData.company?.state || '',
        country: newData.company?.country || '',
        postalCode: newData.company?.postalCode || '',
        latitude: newData.company?.latitude || null,
        longitude: newData.company?.longitude || null
      }
    }
  }
}, { immediate: true, deep: true })

const validateField = (fieldName) => {
  switch (fieldName) {
    case 'name':
      if (!formData.value.name?.trim()) {
        errors.value.name = 'Organization name is required'
      } else if (!/^[A-Za-z0-9\s\-&.,()]+$/.test(formData.value.name)) {
        errors.value.name = 'Organization name should contain only letters, numbers, spaces, and common punctuation'
      } else {
        delete errors.value.name
      }
      break
      
    case 'industry':
      if (!formData.value.industry) {
        errors.value.industry = 'Industry is required'
      } else {
        delete errors.value.industry
      }
      break
      
    case 'companySize':
      if (!formData.value.companySize || formData.value.companySize < 1) {
        errors.value.companySize = 'Organization size is required and must be at least 1'
      } else {
        delete errors.value.companySize
      }
      break
      
    case 'website':
      if (formData.value.website && !isValidUrl(formData.value.website)) {
        errors.value.website = 'Please enter a valid website URL (e.g., https://example.com)'
      } else {
        delete errors.value.website
      }
      break
  }
}

const validateLocationField = (fieldName) => {
  switch (fieldName) {
    case 'address':
      if (!formData.value.location.address?.trim()) {
        errors.value.address = 'Address is required'
      } else {
        delete errors.value.address
      }
      break
      
    case 'city':
      if (!formData.value.location.city?.trim()) {
        errors.value.city = 'City is required'
      } else if (!isAlphaOnly(formData.value.location.city)) {
        errors.value.city = 'City should contain only letters and spaces'
      } else {
        delete errors.value.city
      }
      break
      
    case 'state':
      if (!formData.value.location.state?.trim()) {
        errors.value.state = 'State is required'
      } else if (!isAlphaOnly(formData.value.location.state)) {
        errors.value.state = 'State should contain only letters and spaces'
      } else {
        delete errors.value.state
      }
      break
      
    case 'postalCode':
      if (!formData.value.location.postalCode?.trim()) {
        errors.value.postalCode = 'PIN code is required'
      } else if (!/^\d{6}$/.test(formData.value.location.postalCode)) {
        errors.value.postalCode = 'PIN code should be exactly 6 digits'
      } else {
        delete errors.value.postalCode
      }
      break
  }
}

const validateForm = () => {
  errors.value = {}
  
  // Validate all required fields
  validateField('name')
  validateField('industry')
  validateField('companySize')
  validateField('website')
  validateLocationField('address')
  validateLocationField('city')
  validateLocationField('state')
  validateLocationField('postalCode')
  
  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    alertManager.showError('Validation Error', 'Please fill all required fields correctly before saving.')
    return
  }
  isSaving.value = true
  try {
    const companyData = {
      name: formData.value.name,
      description: formData.value.description,
      industry: formData.value.industry,
      companySize: formData.value.companySize,
      website: formData.value.website,
      foundedYear: formData.value.foundedYear ? formData.value.foundedYear.getFullYear() : null,
      registrationNumber: formData.value.registrationNumber,
      taxId: formData.value.taxId,
      address: formData.value.location.address,
      city: formData.value.location.city,
      state: formData.value.location.state,
      country: formData.value.location.country,
      postalCode: formData.value.location.postalCode,
      latitude: formData.value.location.latitude,
      longitude: formData.value.location.longitude
    }

    if (props.profileData.company) {
      await companiesService.update(companyData)
    } else {
      await companiesService.create(companyData)
    }

    // alertManager.showSuccess('Success', 'Company information saved successfully!')
    emit('update:visible', false)
    emit('save', { company: companyData })
  } catch (error) {
    alertManager.showError('Error', error.message || 'Failed to save company information. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  errors.value = {}
}
</script>

<style scoped>
.professional-info-dialog {
  border-radius: 12px !important;
}

.professional-info-form {
  padding: 1rem 0;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-dropdown,
.form-group .p-inputnumber,
.form-group .p-inputtextarea,
.form-group .p-calendar {
  width: 100% !important;
}

.char-count {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  text-align: right;
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.required {
  color: #e53935;
  font-weight: bold;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}
</style>