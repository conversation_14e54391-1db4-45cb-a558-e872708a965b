import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints';


export const dashboardService = {
  // Get dashboard statistics
  async getStats() {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.STATS);
      return response
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch dashboard statistics')
    }
  },

  // Get recent activity from jobs table
  async getRecentActivity(limit = 10) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECENT_ACTIVITY, {
        params: { limit },
      });
      return {
        success: true,
        data: response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recent activity')
    }
  },

  // Get job recommendations
  async getRecommendations(limit = 5) {
    try {
      const response = await httpClient.get(ENDPOINTS.DASHBOARD.RECOMMENDATIONS, {
        params: { limit },
      });
      return {
        success: true,
        data: response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recommendations')
    }
  }
}