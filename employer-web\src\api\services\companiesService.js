import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const companiesService = {
  // Get all companies
  async getAll(params = {}) {
    try {
      const response = await httpClient.get(ENDPOINTS.COMPANIES.LIST, { params })
      return {
        success: true,
        data: response.companies || response.data || [],
        pagination: response.pagination,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch companies')
    }
  },

  // Get company by ID
  async getById(id) {
    try {
      const response = await httpClient.get(ENDPOINTS.COMPANIES.DETAILS(id))
      return {
        success: true,
        data: response.company || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Company not found')
    }
  },

  // Get jobs by company
  async getJobs(id, params = {}) {
    try {
      const response = await httpClient.get(ENDPOINTS.COMPANIES.JOBS(id), { params })
      return {
        success: true,
        data: response.jobs || response.data || [],
        pagination: response.pagination,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch company jobs')
    }
  },

   // Update company profile
   async update(data) {
    try {
      const response = await httpClient.put(ENDPOINTS.COMPANIES.LIST, data)
      return {
        success: true,
        data: response.company || response.data,
        message: response.message || 'Company updated successfully'
      }
    } catch (error) {
      // If PUT fails, try the alternative route
      if (error.response?.status === 404) {
        try {
          const altResponse = await httpClient.put(`${ENDPOINTS.COMPANIES.LIST}/profile`, data)
          return {
            success: true,
            data: altResponse.company || altResponse.data,
            message: altResponse.message || 'Company updated successfully'
          }
        } catch (altError) {
          throw new Error(altError.response?.data?.message || altError.message || 'Failed to update company')
        }
      }
      throw new Error(error.response?.data?.message || error.message || 'Failed to update company')
    }
  },

  // Create or update company (smart method)
  async createOrUpdate(data) {
    try {
      // First try to get existing company
      const existingCompany = await this.getMyCompany()
      if (existingCompany.success && existingCompany.data) {
        // Company exists, update it
        return await this.update(data)
      }
    } catch (error) {
      // Company doesn't exist, create it
      return await this.create(data)
    }
  }

}