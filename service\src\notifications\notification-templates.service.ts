import { Injectable } from '@nestjs/common';
import { NotificationType } from './entities/notification.entity';

interface TemplateData {
  [key: string]: any;
}

@Injectable()
export class NotificationTemplatesService {
  private templates = {
    [NotificationType.JOB_CREATED]: {
      title: 'New Job: {{jobTitle}}',
      message: 'A new {{jobTitle}} position has been posted at {{companyName}}. Salary: {{salary}}',
      email: {
        subject: 'New Job Alert: {{jobTitle}}',
        body: `
          <h2>New Job Alert!</h2>
          <p>A new position matching your skills has been posted:</p>
          <ul>
            <li><strong>Position:</strong> {{jobTitle}}</li>
            <li><strong>Company:</strong> {{companyName}}</li>
            <li><strong>Location:</strong> {{location}}</li>
            <li><strong>Salary:</strong> {{salary}}</li>
          </ul>
          <p><a href="{{jobUrl}}">View Job Details</a></p>
        `,
      },
    },
    [NotificationType.JOB_UPDATED]: {
      title: 'Job Updated: {{jobTitle}}',
      message: 'The {{jobTitle}} position at {{companyName}} has been updated',
      email: {
        subject: 'Job Update: {{jobTitle}}',
        body: `
          <h2>Job Update Alert</h2>
          <p>The following job has been updated:</p>
          <ul>
            <li><strong>Position:</strong> {{jobTitle}}</li>
            <li><strong>Company:</strong> {{companyName}}</li>
            <li><strong>Changes:</strong> {{changes}}</li>
          </ul>
          <p><a href="{{jobUrl}}">View Updated Job</a></p>
        `,
      },
    },
    [NotificationType.JOB_APPLIED]: {
      title: 'Application Received',
      message: 'Your application for {{jobTitle}} has been received',
      email: {
        subject: 'Application Confirmation: {{jobTitle}}',
        body: `
          <h2>Application Received</h2>
          <p>Your application has been successfully submitted:</p>
          <ul>
            <li><strong>Position:</strong> {{jobTitle}}</li>
            <li><strong>Company:</strong> {{companyName}}</li>
            <li><strong>Applied Date:</strong> {{appliedDate}}</li>
          </ul>
          <p><a href="{{applicationUrl}}">View Application Status</a></p>
        `,
      },
    },
    [NotificationType.APPLICATION_STATUS]: {
      title: 'Application Status Update',
      message: 'Your application for {{jobTitle}} has been {{status}}',
      email: {
        subject: 'Application Status Update: {{jobTitle}}',
        body: `
          <h2>Application Status Update</h2>
          <p>Your application status has been updated:</p>
          <ul>
            <li><strong>Position:</strong> {{jobTitle}}</li>
            <li><strong>Company:</strong> {{companyName}}</li>
            <li><strong>New Status:</strong> {{status}}</li>
            <li><strong>Updated Date:</strong> {{updatedDate}}</li>
          </ul>
          <p><a href="{{applicationUrl}}">View Application Details</a></p>
        `,
      },
    },
    [NotificationType.SYSTEM]: {
      title: '{{title}}',
      message: '{{message}}',
      email: {
        subject: '{{title}}',
        body: `
          <h2>{{title}}</h2>
          <p>{{message}}</p>
          {{#if actionUrl}}
          <p><a href="{{actionUrl}}">{{actionText}}</a></p>
          {{/if}}
        `,
      },
    },
  };

  getTemplate(type: NotificationType, data: TemplateData) {
    const template = this.templates[type];
    if (!template) {
      throw new Error(`Template not found for type: ${type}`);
    }

    return {
      title: this.replacePlaceholders(template.title, data),
      message: this.replacePlaceholders(template.message, data),
      email: {
        subject: this.replacePlaceholders(template.email.subject, data),
        body: this.replacePlaceholders(template.email.body, data),
      },
    };
  }

  private replacePlaceholders(template: string, data: TemplateData): string {
    return template.replace(/\{\{([^}]+)\}\}/g, (match, key) => {
      return data[key.trim()] || match;
    });
  }
}
