import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';
import { ErrorStatus } from '../entities/error-log.entity';

export class UpdateErrorLogDto {
  @ApiProperty({
    description: 'New status of the error',
    enum: ErrorStatus,
    example: ErrorStatus.IN_PROGRESS,
    required: false,
  })
  @IsOptional()
  @IsEnum(ErrorStatus)
  status?: ErrorStatus;

  @ApiProperty({
    description: 'Resolution notes or comments',
    example: 'Fixed by updating the user validation logic',
    required: false,
  })
  @IsOptional()
  @IsString()
  resolutionNotes?: string;
}
