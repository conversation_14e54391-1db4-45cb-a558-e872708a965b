import {
  Controller,
  Post,
  Body,
  Get,
  Query,
  Param,
  UseGuards,
  Request,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ErrorLogService } from '../services/error-log.service';
import { CreateErrorLogDto } from '../dto/create-error-log.dto';
import { UpdateErrorLogDto } from '../dto/update-error-log.dto';
import { ErrorLogEntity, ErrorStatus } from '../entities/error-log.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';

@ApiTags('Error Logs')
@Controller('error-logs')
export class ErrorLogController {
  constructor(private readonly errorLogService: ErrorLogService) {}

  @Post()
  @ApiOperation({ summary: 'Log an application error' })
  @ApiResponse({
    status: 201,
    description: 'Error log created successfully',
    type: ErrorLogEntity,
  })
  async create(
    @Body() createErrorLogDto: CreateErrorLogDto,
    @Request() req,
  ): Promise<ErrorLogEntity> {
    return this.errorLogService.create(createErrorLogDto, req.user?.id);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all error logs with pagination and filters' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'severity', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, enum: ErrorStatus })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated error logs',
    type: [ErrorLogEntity],
  })
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('severity') severity?: string,
    @Query('status') status?: ErrorStatus,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    return this.errorLogService.findAll(page, limit, severity, status, startDate, endDate);
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get error statistics' })
  @ApiResponse({
    status: 200,
    description: 'Returns error statistics',
  })
  async getStats() {
    return this.errorLogService.getErrorStats();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a specific error log by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the error log',
    type: ErrorLogEntity,
  })
  async findOne(@Param('id') id: string): Promise<ErrorLogEntity> {
    return this.errorLogService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an error log status and resolution notes' })
  @ApiResponse({
    status: 200,
    description: 'Error log updated successfully',
    type: ErrorLogEntity,
  })
  async update(
    @Param('id') id: string,
    @Body() updateErrorLogDto: UpdateErrorLogDto,
    @Request() req,
  ): Promise<ErrorLogEntity> {
    return this.errorLogService.update(id, updateErrorLogDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an error log' })
  @ApiResponse({
    status: 200,
    description: 'Error log deleted successfully',
  })
  async delete(@Param('id') id: string): Promise<void> {
    return this.errorLogService.delete(id);
  }
}
