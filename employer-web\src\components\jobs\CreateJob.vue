<script setup lang="ts">
import {
  ref,
  reactive,
  watch,
  onMounted,
  computed,
  defineProps,
  nextTick,
} from "vue";
import { useRouter } from "vue-router";
// import { JobType, PaymentType, JobStatus, JobUrgency, ExperienceLevel, ContactDisplayType } from '@/constants/enums'
import InputText from "primevue/inputtext";
import Textarea from "primevue/textarea";
import Chips from "primevue/chips";
import InputNumber from "primevue/inputnumber";
import Button from "primevue/button";
import Card from "primevue/card";
import { useI18n } from "vue-i18n";
import { useToast } from "primevue/usetoast";
import Editor from "primevue/editor";
import Select from "primevue/select";
import axios from "axios";
import httpClient from "@/api/httpClient";
import { ENDPOINTS } from "@/api/endpoints";
import { useAuthStore } from "@/stores/auth";
import ProgressSpinner from "primevue/progressspinner";
import InputSwitch from "primevue/inputswitch";
import { useJobsStore } from "@/stores/jobs";
import ProgressBar from "primevue/progressbar";

const router = useRouter();
const toast = useToast();
const isSubmitting = ref(false);
const authStore = useAuthStore();
const isLoading = ref(true);
const jobsStore = useJobsStore();
const isJobLoading = ref(false);
const currentStep = ref(1);

const props = defineProps({
  editMode: { type: Boolean, default: false },
  jobId: { type: [String, Number], default: null },
});

const { t } = useI18n();

// Initialize job enums
const jobEnums = ref({
  jobTypes: [],
  paymentTypes: [],
  jobUrgencies: [],
  experienceLevels: [],
  contactTypes: [],
  urgencyLevels: [],
});

const industries = ref<{ id: string; name: string; subIndustries?: { id: string; name: string }[] }[]>([]);
const subIndustries = ref<{ id: string; name: string }[]>([]);

// Watch for enums to become available
watch(
  () => authStore.enums,
  (newEnums) => {
    if (newEnums) {
      console.log("Enums available:", newEnums); // Debug log
      jobEnums.value = {
        jobTypes: newEnums.jobTypes || [],
        paymentTypes: newEnums.paymentTypes || [],
        jobUrgencies: newEnums.jobUrgencies || [],
        experienceLevels: newEnums.experienceLevels || [],
        contactTypes: newEnums.contactDisplayTypes || [],
        urgencyLevels: newEnums.urgencyLevels || [],
      };
      isLoading.value = false;
    }
  },
  { immediate: true, deep: true }
);

// Watch for industries to become available
watch(
  () => authStore.industries,
  (newIndustries) => {
    if (newIndustries) {
      console.log("Industries available:", newIndustries); // Debug log
      industries.value = newIndustries;
      isLoading.value = false;
    }
  },
  { immediate: true, deep: true }
);

// Add computed property for salary display based on payment type
const salaryDisplay = computed(() => {
  if (!job.paymentType) return "";

  const paymentTypeMap = {
    DAILY: 5000,
    WEEKLY: 50000,
    BI_WEEKLY: 100000,
    MONTHLY: 500000,
  };
  const amount = paymentTypeMap[job.paymentType];
  return amount ? `₹${amount.toLocaleString()}` : "";
});

// Computed property for progress percentage
const progressPercentage = computed(() => {
  if (currentStep.value === 1) return 40;
  if (currentStep.value === 2) return 80;
  if (currentStep.value === 3) return 100;
  return 0;
});

const progressLabel = computed(() => {
  if (currentStep.value === 1) return "";
  if (currentStep.value === 2) return "";
  if (currentStep.value === 3) return "";
  return "";
});

const job = reactive({
  title: "",
  description: "",
  industryId: "",
  subIndustryId: "",
  salary: 0,
  jobType: null,
  paymentType: null,
  location: "",
  urgency: null,
  experienceLevel: null,
  benefits: [] as string[],
  requirements: [] as string[],
  responsibilities: [] as string[],
  skills: [] as string[],
  contactDisplayType: null,
  contactPhone: "",
  contactEmail: "",
  contactPerson: "",
  vacancies: null,
  workingHours: null,
  accommodation: false,
  transportation: false,
  foodProvided: false,
  safetyEquipment: false,
  trainingProvided: false,
  showContact: true,
  thumbnail: "",
  images: [],
});

// Change phoneInput default to empty string (only digits entered)
const phoneInput = ref("");

// Update onPhoneBlur to combine '+91' and phoneInput for job.contactPhone
const onPhoneBlur = () => {
  if (phoneInput.value) {
    job.contactPhone = "+91 " + phoneInput.value;
  } else {
    job.contactPhone = "";
  }
  validateStep3();
};

// Update isValidPhoneNumber to expect '+91 ' + 10 digits
const isValidPhoneNumber = (phone: string) => {
  const phoneRegex = /^\+91\s[0-9]{10}$/;
  return phoneRegex.test(phone.trim());
};

// When switching contact type, clear phoneInput as needed and validate
watch(
  () => job.contactDisplayType,
  (newType) => {
    if (newType === "NONE") {
      job.contactPhone = "";
      job.contactEmail = "";
      phoneInput.value = "";
      errors.contactPhone = "";
      errors.contactEmail = "";
    } else if (newType === "PHONE") {
      job.contactEmail = "";
      errors.contactEmail = "";
    } else if (newType === "EMAIL") {
      job.contactPhone = "";
      phoneInput.value = "";
      errors.contactPhone = "";
    }

    // Validate step 3 when contact type changes
    validateStep3();
  }
);

// Computed properties for contact type logic
const isPhoneEnabled = computed(() => {
  return (
    job.contactDisplayType === "PHONE" || job.contactDisplayType === "BOTH"
  );
});

const isEmailEnabled = computed(() => {
  return (
    job.contactDisplayType === "EMAIL" || job.contactDisplayType === "BOTH"
  );
});

const isContactRequired = computed(() => {
  return job.contactDisplayType && job.contactDisplayType !== "NONE";
});

const errors = reactive({
  title: "",
  description: "",
  industryId: "",
  subIndustryId: "",
  jobType: "",
  experienceLevel: "",
  location: "",
  contactDisplayType: "",
  contactPhone: "",
  contactEmail: "",
  paymentType: "",
  salary: "",
  urgency: "",
});

watch(
  () => job.industryId,
  (newIndustryId) => {
    if (newIndustryId) {
      const selectedIndustry = industries.value.find(
        (ind) => ind.id === newIndustryId
      );
      if (selectedIndustry) {
        subIndustries.value = selectedIndustry.subIndustries || [];
        // Reset sub-industry selection when industry changes
        job.subIndustryId = null;
      }
    } else {
      subIndustries.value = [];
      job.subIndustryId = null;
    }
  }
);

function stripHtmlTags(html) {
  const stripped = html.replace(/<[^>]*>/g, "");
  const textarea = document.createElement("textarea");
  textarea.innerHTML = stripped;
  return textarea.value.trim();
}

const validateStep1 = () => {
  let isValid = true;

  // Title validation
  if (!job.title.trim()) {
    errors.title = "Title is required";
    isValid = false;
  } else {
    errors.title = "";
  }

  // Description validation
  if (!job.description.trim()) {
    errors.description = "Description is required";
    isValid = false;
  } else {
    errors.description = "";
  }

  // Industry validation
  if (!job.industryId) {
    errors.industryId = "Industry is required";
    isValid = false;
  } else {
    errors.industryId = "";
  }

  // Sub-industry validation
  if (!job.subIndustryId) {
    errors.subIndustryId = "Sub-industry is required";
    isValid = false;
  } else {
    errors.subIndustryId = "";
  }

  // Job Type validation
  if (!job.jobType) {
    errors.jobType = "Job type is required";
    isValid = false;
  } else {
    errors.jobType = "";
  }

  // Experience Level validation
  if (!job.experienceLevel) {
    errors.experienceLevel = "Experience level is required";
    isValid = false;
  } else {
    errors.experienceLevel = "";
  }

  // Location validation
  if (!job.location.trim()) {
    errors.location = "Location is required";
    isValid = false;
  } else {
    errors.location = "";
  }

  return isValid;
};

const validateStep2 = () => {
  let isValid = true;

  // Payment type validation
  if (!job.paymentType) {
    errors.paymentType = "Payment type is required";
    isValid = false;
  } else {
    errors.paymentType = "";
  }

  const paymentTypeMap = {
    DAILY: 5000,
    WEEKLY: 50000,
    BI_WEEKLY: 100000,
    MONTHLY: 500000,
  };

  // Salary validation
  if (!job.salary) {
    errors.salary = "Salary is required";
    isValid = false;
  } else if (
    job.paymentType &&
    paymentTypeMap[job.paymentType] &&
    job.salary > paymentTypeMap[job.paymentType]
  ) {
    errors.salary = `Maximum salary for ${job.paymentType.toLowerCase()} payment is ${
      salaryDisplay.value
    }`;
    isValid = false;
  } else {
    errors.salary = "";
  }

  // Urgency validation
  if (!job.urgency) {
    errors.urgency = "Urgency is required";
    isValid = false;
  } else {
    errors.urgency = "";
  }

  return isValid;
};

const validateStep3 = () => {
  let isValid = true;

  // Contact type validation
  if (!job.contactDisplayType) {
    errors.contactDisplayType = "Contact type is required";
    isValid = false;
  } else {
    errors.contactDisplayType = "";
  }

  // Validate based on contact display type
  if (job.contactDisplayType === "NONE") {
    // No validation needed for none
    errors.contactPhone = "";
    errors.contactEmail = "";
    return isValid;
  }

  if (job.contactDisplayType === "PHONE" || job.contactDisplayType === "BOTH") {
    // Phone is required when PHONE or BOTH is selected
    if (!job.contactPhone || !job.contactPhone.trim()) {
      errors.contactPhone = "Phone number is required";
      isValid = false;
    } else if (!isValidPhoneNumber(job.contactPhone)) {
      errors.contactPhone = "Please enter a valid phone number";
      isValid = false;
    } else {
      errors.contactPhone = "";
    }
  }

  if (job.contactDisplayType === "EMAIL" || job.contactDisplayType === "BOTH") {
    // Email is required when EMAIL or BOTH is selected
    if (!job.contactEmail || !job.contactEmail.trim()) {
      errors.contactEmail = "Email address is required";
      isValid = false;
    } else if (!isValidEmail(job.contactEmail)) {
      errors.contactEmail = "Please enter a valid email address";
      isValid = false;
    } else {
      errors.contactEmail = "";
    }
  }

  return isValid;
};

// Add validation helper functions
const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Step navigation functions
const nextStep = () => {
  let isValid = false;

  if (currentStep.value === 1) {
    isValid = validateStep1();
    if (!isValid) {
      toast.add({
        severity: "error",
        summary: "Validation Error",
        detail: "Please fill in all required fields",
        life: 3000,
      });
      return;
    }
  }

  if (currentStep.value === 2) {
    isValid = validateStep2();
    if (!isValid) {
      toast.add({
        severity: "error",
        summary: "Validation Error",
        detail: "Please fill in all required fields correctly",
        life: 3000,
      });
      return;
    }
  }

  if (currentStep.value < 3) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

// Fetch job details if in edit mode
async function fetchAndPrefillJob() {
  if (props.editMode && props.jobId) {
    isJobLoading.value = true;
    try {
      const jobData = await jobsStore.fetchJobById(props.jobId);

      // Set industryId first
      job.industryId = jobData.industry?.id || jobData.industryId || "";

      // Load sub-industries for the selected industry and wait for it to complete
      if (job.industryId) {
        const selectedIndustry = industries.value.find(
          (ind) => ind.id === job.industryId
        );
        if (selectedIndustry) {
          subIndustries.value = selectedIndustry.subIndustries || [];
          // Use nextTick to ensure DOM updates are complete before setting subIndustryId
          await nextTick();
          // Now set the subIndustryId after sub-industries are loaded
          job.subIndustryId =
            jobData.subIndustry?.id || jobData.subIndustryId || "";
        }
      } else {
        subIndustries.value = [];
        job.subIndustryId = "";
      }

      // Set other fields
      Object.assign(job, {
        title: jobData.title || "",
        description: jobData.description || "",
        salary: parseFloat(jobData.salary) || 0,
        jobType: jobData.jobType || null,
        paymentType: jobData.paymentType || null,
        location: jobData.location || "",
        urgency: jobData.urgency || null,
        experienceLevel: jobData.experienceLevel || null,
        benefits: jobData.benefits || [],
        requirements: jobData.requirements || [],
        responsibilities: jobData.responsibilities || [],
        skills: jobData.skills || [],
        contactDisplayType: jobData.contactDisplayType || null,
        contactPhone: jobData.contactPhone || "",
        contactEmail: jobData.contactEmail || "",
        contactPerson: jobData.contactPerson || "",
        vacancies: jobData.vacancies || null,
        workingHours: jobData.workingHours || null,
        accommodation: jobData.accommodation || false,
        transportation: jobData.transportation || false,
        foodProvided: jobData.foodProvided || false,
        safetyEquipment: jobData.safetyEquipment || false,
        trainingProvided: jobData.trainingProvided || false,
        showContact: jobData.showContact !== false,
        thumbnail: jobData.thumbnail || "",
        images: jobData.images || [],
      });

      if (jobData.contactPhone) {
        // Extract digits after '+91 '
        const match = jobData.contactPhone.match(/^\+91\s([0-9]{10})$/);
        phoneInput.value = match ? match[1] : "";
      }
    } catch (e) {
      toast.add({
        severity: "error",
        summary: "Error",
        detail: "Failed to load job data",
        life: 3000,
      });
      router.push("/employer/jobs");
    } finally {
      isJobLoading.value = false;
    }
  }
}
onMounted(() => {
  fetchAndPrefillJob();
});

// In submit function, use update if editMode, else create
const submit = async () => {
  try {
    isSubmitting.value = true;
    if (!validateStep1() || !validateStep2() || !validateStep3()) {
      toast.add({
        severity: "error",
        summary: "Validation Error",
        detail: "Please fill in all required fields correctly",
        life: 3000,
      });
      return;
    }
    const jobData = {
      ...job,
      salary: Number(job.salary) || 0,
      vacancies: job.vacancies !== null && job.vacancies !== undefined ? Number(job.vacancies) : null,
      workingHours: job.workingHours !== null && job.workingHours !== undefined ? Number(job.workingHours) : null,
    };
    if (job.contactDisplayType === "NONE") {
      jobData.contactPhone = null;
      jobData.contactEmail = null;
    } else if (job.contactDisplayType === "PHONE") {
      jobData.contactEmail = null;
    } else if (job.contactDisplayType === "EMAIL") {
      jobData.contactPhone = null;
    }
    // If BOTH, send both as entered (no need to set to null)

    if (props.editMode && props.jobId) {
      await httpClient.patch(ENDPOINTS.JOBS.UPDATE(props.jobId), jobData);
      toast.add({
        severity: "success",
        summary: "Success",
        detail: "Job updated successfully",
        life: 3000,
      });
      router.push(`/employer/jobs/${props.jobId}`);
    } else {
      await httpClient.post(ENDPOINTS.JOBS.CREATE, jobData);
      toast.add({
        severity: "success",
        summary: "Success",
        detail: "Job created successfully",
        life: 3000,
      });
      router.push("/employer/jobs");
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "Error",
      detail:
        error.response?.data?.message ||
        "Failed to submit job. Please try again.",
      life: 3000,
    });
  } finally {
    isSubmitting.value = false;
  }
};

const requirementInput = ref("");
const responsibilityInput = ref("");
const skillInput = ref("");
const benefitInput = ref("");

function addRequirement() {
  const val = requirementInput.value.trim();
  if (val && !job.requirements.includes(val)) {
    job.requirements.push(val);
  }
  requirementInput.value = "";
}
function removeRequirement(idx) {
  job.requirements.splice(idx, 1);
}
function addResponsibility() {
  const val = responsibilityInput.value.trim();
  if (val && !job.responsibilities.includes(val)) {
    job.responsibilities.push(val);
  }
  responsibilityInput.value = "";
}
function removeResponsibility(idx) {
  job.responsibilities.splice(idx, 1);
}
function addSkill() {
  const val = skillInput.value.trim();
  if (val && !job.skills.includes(val)) {
    job.skills.push(val);
  }
  skillInput.value = "";
}
function removeSkill(idx) {
  job.skills.splice(idx, 1);
}
function addBenefit() {
  const val = benefitInput.value.trim();
  if (val && !job.benefits.includes(val)) {
    job.benefits.push(val);
  }
  benefitInput.value = "";
}
function removeBenefit(idx) {
  job.benefits.splice(idx, 1);
}
</script>

<template>
  <div class="create-job-page">
    <div class="create-job-container">
      <div class="form-header">
        <div class="form-title-row">
          <div class="form-title-left">
            <Button
              label="Back"
              icon="pi pi-arrow-left"
              severity="secondary"
              @click="router.go(-1)"
            />
          </div>
          <div class="form-title-center">
            <h1 class="form-title">{{ editMode ? "Edit Job" : "Post a Job" }}</h1>
          </div>
          <div class="form-title-right"></div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
          <div class="progress-steps">
            <div
              class="step-item"
              :class="{ active: currentStep >= 1, completed: currentStep > 1 }"
            >
              <div class="step-number">1</div>
              <span class="step-label">Basic Info</span>
            </div>
            <div
              class="step-item"
              :class="{ active: currentStep >= 2, completed: currentStep > 2 }"
            >
              <div class="step-number">2</div>
              <span class="step-label">Requirements & Benefits</span>
            </div>
            <div class="step-item" :class="{ active: currentStep >= 3 }">
              <div class="step-number">3</div>
              <span class="step-label">Contact Info</span>
            </div>
          </div>
        </div>
      </div>

      <div v-if="isLoading || isJobLoading" class="loading-container">
        <ProgressSpinner />
        <span>Please wait...</span>
      </div>

      <div v-else class="form-content">
        <!-- Step 1: Basic Information -->
        <div v-show="currentStep === 1" class="step-panel">
          <div class="panel-header">
            <h2>Basic Information</h2>
          </div>

          <div class="form-grid">
            <div class="form-field">
              <label>Job Title <span class="required">*</span></label>
              <InputText
                v-model="job.title"
                placeholder="Enter job title"
                :class="{ 'p-invalid': errors.title }"
                @blur="validateStep1"
              />
              <small class="p-error" v-if="errors.title">{{
                errors.title
              }}</small>
            </div>

            <div class="form-field full-width">
              <label>Description <span class="required">*</span></label>
              <Editor
                v-model="job.description"
                editorStyle="height: 200px"
                :class="{ 'p-invalid': errors.description }"
                @blur="validateStep1"
              >
                <template v-slot:toolbar>
                  <span class="ql-formats">
                    <button v-tooltip.bottom="'Bold'" class="ql-bold"></button>
                    <button
                      v-tooltip.bottom="'Italic'"
                      class="ql-italic"
                    ></button>
                    <button
                      v-tooltip.bottom="'Underline'"
                      class="ql-underline"
                    ></button>
                  </span>
                </template>
              </Editor>
              <small class="p-error" v-if="errors.description">{{
                errors.description
              }}</small>
            </div>

            <div class="form-field">
              <label>Industry <span class="required">*</span></label>
              <Select
                v-model="job.industryId"
                :options="industries"
                optionLabel="name"
                optionValue="id"
                placeholder="Select industry"
                :class="{ 'p-invalid': errors.industryId }"
                @change="validateStep1"
              />
              <small class="p-error" v-if="errors.industryId">{{
                errors.industryId
              }}</small>
            </div>

            <div class="form-field">
              <label
                >Industry sub-category <span class="required">*</span></label
              >
              <Select
                v-model="job.subIndustryId"
                :options="subIndustries"
                optionLabel="name"
                optionValue="id"
                placeholder="Select sub-industry"
                :class="{ 'p-invalid': errors.subIndustryId }"
                @change="validateStep1"
                :disabled="!job.industryId"
              />
              <small class="p-error" v-if="errors.subIndustryId">{{
                errors.subIndustryId
              }}</small>
            </div>

            <div class="form-field">
              <label>Location <span class="required">*</span></label>
              <InputText
                v-model="job.location"
                placeholder="Enter location"
                :class="{ 'p-invalid': errors.location }"
                @blur="validateStep1"
              />
              <small class="p-error" v-if="errors.location">{{
                errors.location
              }}</small>
            </div>

            <div class="form-field">
              <label>Job Type <span class="required">*</span></label>
              <Select
                v-model="job.jobType"
                :options="jobEnums.jobTypes"
                optionLabel="label"
                optionValue="id"
                placeholder="Select job type"
                :class="{ 'p-invalid': errors.jobType }"
                @change="validateStep1"
              />
              <small class="p-error" v-if="errors.jobType">{{
                errors.jobType
              }}</small>
            </div>

            <div class="form-field">
              <label>Experience Level <span class="required">*</span></label>
              <Select
                v-model="job.experienceLevel"
                :options="jobEnums.experienceLevels"
                optionLabel="label"
                optionValue="id"
                placeholder="Select experience level"
                :class="{ 'p-invalid': errors.experienceLevel }"
                @change="validateStep1"
              />
              <small class="p-error" v-if="errors.experienceLevel">{{
                errors.experienceLevel
              }}</small>
            </div>

            <div class="form-field">
              <label>Vacancies</label>
              <InputNumber
                v-model="job.vacancies"
                placeholder="Enter number of vacancies"
              />
            </div>
          </div>
        </div>

        <!-- Step 2: Requirements & Benefits -->
        <div v-show="currentStep === 2" class="step-panel">
          <div class="panel-header">
            <h2>Requirements & Benefits</h2>
          </div>

          <div class="form-grid">
            <div class="form-field">
              <label>Payment Type <span class="required">*</span></label>
              <Select
                v-model="job.paymentType"
                :options="jobEnums.paymentTypes"
                optionLabel="label"
                optionValue="id"
                placeholder="Select payment type"
                :class="{ 'p-invalid': errors.paymentType }"
                @change="validateStep2"
              />
              <small class="p-error" v-if="errors.paymentType">{{
                errors.paymentType
              }}</small>
            </div>

            <div class="form-field">
              <label>Salary <span class="required">*</span></label>
              <InputNumber
                v-model="job.salary"
                placeholder="Enter salary amount"
                prefix="₹"
                :class="{ 'p-invalid': errors.salary }"
                @blur="validateStep2"
              />
              <small class="p-error" v-if="errors.salary">{{
                errors.salary
              }}</small>
              <small v-if="salaryDisplay" class="salary-hint"
                >Maximum: {{ salaryDisplay }}</small
              >
            </div>

            <div class="form-field">
              <label>Urgency <span class="required">*</span></label>
              <Select
                v-model="job.urgency"
                :options="jobEnums.urgencyLevels"
                optionLabel="label"
                optionValue="id"
                placeholder="Select urgency"
                :class="{ 'p-invalid': errors.urgency }"
                @change="validateStep2"
              />
              <small class="p-error" v-if="errors.urgency">{{
                errors.urgency
              }}</small>
            </div>

            <div class="form-field">
              <label>Working Hours</label>
              <InputNumber
                v-model="job.workingHours"
                placeholder="Enter working hours"
              />
            </div>

            <div class="form-field full-width">
              <label>Requirements</label>
              <div class="input-with-button">
                <InputText
                  v-model="requirementInput"
                  placeholder="Add requirement"
                  @keyup.enter="addRequirement"
                />
                <Button
                  icon="pi pi-check"
                  @click="addRequirement"
                  :disabled="!requirementInput.trim()"
                />
              </div>
              <div class="tag-list">
                <span
                  v-for="(req, idx) in job.requirements"
                  :key="req"
                  class="tag-chip"
                >
                  {{ req }}
                  <Button
                    icon="pi pi-times"
                    text
                    size="small"
                    @click="removeRequirement(idx)"
                  />
                </span>
              </div>
            </div>

            <div class="form-field full-width">
              <label>Responsibilities</label>
              <div class="input-with-button">
                <InputText
                  v-model="responsibilityInput"
                  placeholder="Add responsibility"
                  @keyup.enter="addResponsibility"
                />
                <Button
                  icon="pi pi-check"
                  @click="addResponsibility"
                  :disabled="!responsibilityInput.trim()"
                />
              </div>
              <div class="tag-list">
                <span
                  v-for="(resp, idx) in job.responsibilities"
                  :key="resp"
                  class="tag-chip"
                >
                  {{ resp }}
                  <Button
                    icon="pi pi-times"
                    text
                    size="small"
                    @click="removeResponsibility(idx)"
                  />
                </span>
              </div>
            </div>

            <div class="form-field full-width">
              <label>Skills</label>
              <div class="input-with-button">
                <InputText
                  v-model="skillInput"
                  placeholder="Add skill"
                  @keyup.enter="addSkill"
                />
                <Button
                  icon="pi pi-check"
                  @click="addSkill"
                  :disabled="!skillInput.trim()"
                />
              </div>
              <div class="tag-list">
                <span
                  v-for="(skill, idx) in job.skills"
                  :key="skill"
                  class="tag-chip"
                >
                  {{ skill }}
                  <Button
                    icon="pi pi-times"
                    text
                    size="small"
                    @click="removeSkill(idx)"
                  />
                </span>
              </div>
            </div>

            <div class="form-field full-width">
              <label>Benefits</label>
              <div class="input-with-button">
                <InputText
                  v-model="benefitInput"
                  placeholder="Add benefit"
                  @keyup.enter="addBenefit"
                />
                <Button
                  icon="pi pi-check"
                  @click="addBenefit"
                  :disabled="!benefitInput.trim()"
                />
              </div>
              <div class="tag-list">
                <span
                  v-for="(benefit, idx) in job.benefits"
                  :key="benefit"
                  class="tag-chip"
                >
                  {{ benefit }}
                  <Button
                    icon="pi pi-times"
                    text
                    size="small"
                    @click="removeBenefit(idx)"
                  />
                </span>
              </div>
            </div>

            <!-- Benefits toggles -->
            <div class="benefits-toggles">
              <div class="toggle-item">
                <label>Accommodation Provided</label>
                <InputSwitch v-model="job.accommodation" />
              </div>
              <div class="toggle-item">
                <label>Transportation Provided</label>
                <InputSwitch v-model="job.transportation" />
              </div>
              <div class="toggle-item">
                <label>Food Provided</label>
                <InputSwitch v-model="job.foodProvided" />
              </div>
              <div class="toggle-item">
                <label>Safety Equipment Provided</label>
                <InputSwitch v-model="job.safetyEquipment" />
              </div>
              <div class="toggle-item">
                <label>Training Provided</label>
                <InputSwitch v-model="job.trainingProvided" />
              </div>
            </div>
          </div>
        </div>
        <!-- End of Step 2 -->

        <!-- Step 3: Contact Info -->
        <div v-show="currentStep === 3" class="step-panel">
          <div v-if="isLoading || isJobLoading" class="loading-container">
            <ProgressSpinner />
            <span>Please wait...</span>
          </div>
          <div v-else-if="!jobEnums.contactTypes.length" class="form-grid">
            <div class="form-field full-width">
              <span class="p-error">Contact types not available. Please try again later.</span>
            </div>
          </div>
          <div v-else>
            <div class="panel-header">
              <h2>Contact Information</h2>
            </div>
            <div class="form-grid">
              <div class="form-field">
                <label>Contact Type <span class="required">*</span></label>
                <Select
                  v-model="job.contactDisplayType"
                  :options="jobEnums.contactTypes"
                  optionLabel="label"
                  optionValue="id"
                  placeholder="Select contact type"
                  :class="{ 'p-invalid': errors.contactDisplayType }"
                  @change="validateStep3"
                />
                <small class="p-error" v-if="errors.contactDisplayType">{{
                  errors.contactDisplayType
                }}</small>
              </div>
              <div class="form-field">
                <label>Contact Person</label>
                <InputText
                  v-model="job.contactPerson"
                  placeholder="Enter contact person name"
                />
              </div>
              <div class="form-field">
                <label :class="{ required: isPhoneEnabled }">
                  Contact Phone
                  <span v-if="isPhoneEnabled" class="required">*</span>
                </label>
                <div style="display: flex; align-items: center; gap: 0.5rem">
                  <span
                    style="
                      padding: 0.5rem 0.75rem;
                      background: #f4f4f4;
                      border: 1px solid #ccc;
                      border-radius: 4px 0 0 4px;
                    "
                    >+91</span
                  >
                  <InputText
                    v-model="phoneInput"
                    placeholder="Enter 10-digit number"
                    :class="{ 'p-invalid': errors.contactPhone }"
                    :disabled="!isPhoneEnabled"
                    maxlength="10"
                    style="border-radius: 0 4px 4px 0"
                    @blur="onPhoneBlur"
                  />
                </div>
                <small class="p-error" v-if="errors.contactPhone">{{
                  errors.contactPhone
                }}</small>
              </div>
              <div class="form-field">
                <label :class="{ required: isEmailEnabled }">
                  Contact Email
                  <span v-if="isEmailEnabled" class="required">*</span>
                </label>
                <InputText
                  v-model="job.contactEmail"
                  placeholder="Enter contact email"
                  :class="{ 'p-invalid': errors.contactEmail }"
                  :disabled="!isEmailEnabled"
                  @blur="validateStep3"
                />
                <small class="p-error" v-if="errors.contactEmail">{{
                  errors.contactEmail
                }}</small>
              </div>
            </div>
          </div>
        </div>
        <!-- End of Step 3 -->
      </div>

      <!-- Sticky footer for navigation buttons -->
      <div class="step-actions">
        <Button
          v-if="currentStep > 1"
          label="Back"
          severity="secondary"
          @click="prevStep"
        />
        <Button
          v-if="currentStep < 3"
          label="Next"
          @click="nextStep"
        />
        <Button
          v-if="currentStep === 3"
          :label="editMode ? 'Update Job' : 'Post a Job'"
          @click="submit"
          :loading="isSubmitting"
          :disabled="isSubmitting"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.create-job-page {
  background: #f7f9fb;
  width: 100vw;
  overflow-x: hidden;
}
.create-job-container {
  background: #fff;
  width: 98vw;
  max-width:none ;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  padding: 0.5rem 2.5rem 7rem 2.5rem; /* Reduced top padding */
  display: flex;
  flex-direction: column;
}
.form-header {
  margin-bottom: 2.5rem;
  margin-top: 0;
  padding-top: 0;
}
.form-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 1.5rem;
}
.form-title-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}
.form-title-center {
  flex: 1 1 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-title-right {
  flex: 0 0 auto;
  width: 42px; /* same as button width for symmetry */
}
.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a237e;
  margin: 0;
  text-align: center;
}
.progress-section {
  margin-bottom: 1.5rem;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 1rem;
}
.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  position: relative;
}
.progress-steps::before {
  content: '';
  position: absolute;
  top: 18px;
  left: 0;
  right: 0;
  height: 3px;
  background: #e3e7ef;
  z-index: 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 1;
}
.step-item.active .step-number,
.step-item.completed .step-number {
  background: #1976d2;
  color: #fff;
  border: 2px solid #1976d2;
}
.step-item.completed .step-number {
  background: #43a047;
  border-color: #43a047;
}
.step-number {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #e3e7ef;
  color: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  border: 2px solid #e3e7ef;
  margin-bottom: 0.3rem;
  z-index: 1;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
.step-label {
  font-size: 0.98rem;
  color: #5c6bc0;
  font-weight: 500;
  margin-top: 0.1rem;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #1976d2;
  font-size: 1.1rem;
}
.form-content {
  margin-top: 0.5rem;
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
}

.step-panel {
  animation: fadeIn 0.4s;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: none; }
}
.panel-header {
  margin-bottom: 1.5rem;
}
.panel-header h2 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 0.2rem;
}
.panel-header p {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem 2rem;
}
.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}
.form-field.full-width {
  grid-column: 1 / -1;
}
.required {
  color: #d32f2f;
  font-weight: 600;
  margin-left: 0.2rem;
}
.p-error {
  color: #d32f2f;
  font-size: 0.92rem;
  margin-top: 0.1rem;
}
.salary-hint {
  color: #1976d2;
  font-size: 0.92rem;
  margin-top: 0.1rem;
}
.input-with-button {
  display: flex;
  gap: 0.5rem;
}
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.3rem;
}
.tag-chip {
  background: #e3e7ef;
  color: #1976d2;
  border-radius: 16px;
  padding: 0.3rem 0.9rem 0.3rem 0.8rem;
  display: flex;
  align-items: center;
  font-size: 0.98rem;
  font-weight: 500;
  gap: 0.3rem;
}
.benefits-toggles {
  display: flex;
  flex-wrap: wrap;
  gap: 1.2rem 2.5rem;
  margin-top: 1.2rem;
  grid-column: 1 / -1;
}
.toggle-item {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  font-size: 1rem;
  color: #374151;
}
.step-actions {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  border-top: 1px solid #e3e7ef;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 2.5rem;
  z-index: 100;
}
@media (max-width: 1200px) {
  .create-job-container {
    max-width: 98vw;
    padding: 1.2rem 0.5rem;
  }
}
@media (max-width: 900px) {
  .create-job-container {
    max-width: 100vw;
    padding: 1.2rem 0.5rem;
  }
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem 0;
  }
  .benefits-toggles {
    flex-direction: column;
    gap: 1rem;
  }
  .step-actions {
    flex-direction: row;
    gap: 0.7rem;
    align-items: center;
  }
  .step-actions .p-button {
    flex: 1 1 0;
    min-width: 0;
  }
  .step-actions .p-button:only-child {
    flex: none;
    width: 50%;
    margin-left: auto;
    margin-right: 0;
    display: block;
  }
}
</style>
