<script setup>
import { ref, onMounted } from 'vue';
import Card from 'primevue/card';
import SpeedDial from 'primevue/speeddial';
import FeedbackTable from './FeedbackTable.vue';
import FeedbackCreateDrawer from './FeedbackCreateDrawer.vue';
import FeedbackFilterDrawer from './FeedbackFilterDrawer.vue';
import FeedbackViewDrawer from './FeedbackViewDrawer.vue';
import { feedbackService } from '@/api/services/feedbackService';
import { useToast } from 'primevue/usetoast';

const FeedbackType = {
  SUGGESTION: 'SUGGESTION',
  BUG_REPORT: 'BUG_REPORT',
  FEATURE_REQUEST: 'FEATURE_REQUEST',
  COMPLAINT: 'COMPLAINT',
  COMPLIMENT: 'COMPLIMENT',
  GENERAL: 'GENERAL'
};

const feedbacks = ref([]); // Use mock/sample data for now
const showCreateDrawer = ref(false);
const showFilterDrawer = ref(false);
const filters = ref({
  searchTerm: '',
  selectedTypeFilter: [],
  selectedStatusFilter: [],
  dateRange: null
});
const feedbackTypeOptions = Object.values(FeedbackType).map(type => ({
  label: type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}));
const statusOptions = [
  { label: 'Open', value: 'open' },
  { label: 'In Progress', value: 'in_progress' },
  { label: 'Completed', value: 'completed' },
  { label: 'Acknowledged', value: 'acknowledged' },
  { label: 'Rejected', value: 'rejected' }
];

const toast = useToast();
const loading = ref(false);
const page = ref(1);
const limit = ref(20);
const total = ref(0);

const editingFeedback = ref(null);
const viewingFeedback = ref(null);
const showViewDrawer = ref(false);

async function fetchFeedbacks() {
  loading.value = true;
  try {
    const params = {
      page: page.value,
      limit: limit.value,
      ...filters.value,
    };
    const response = await feedbackService.getFeedbackList(params);
    feedbacks.value = response.data.items || response.data || [];
    total.value = response.data.total || response.total || 0;
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to fetch feedback.', life: 3000 });
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchFeedbacks();
});

async function handleEdit(feedback) {
  editingFeedback.value = feedback;
  showCreateDrawer.value = true;
}

function handleCreate() {
  editingFeedback.value = null;
  showCreateDrawer.value = true;
}

async function handleUpdateFeedback(payload) {
  try {
    loading.value = true;
    await feedbackService.updateFeedback(payload.id, payload);
    toast.add({ severity: 'success', summary: 'Success', detail: 'Feedback updated.', life: 3000 });
    showCreateDrawer.value = false;
    editingFeedback.value = null;
    fetchFeedbacks();
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to update feedback.', life: 3000 });
  } finally {
    loading.value = false;
  }
}

function handleView(feedback) {
  viewingFeedback.value = feedback;
  showViewDrawer.value = true;
}

function handleCloseViewDrawer() {
  showViewDrawer.value = false;
  viewingFeedback.value = null;
}

async function handleResolve(feedback) {
  try {
    loading.value = true;
    await feedbackService.updateFeedback(feedback.id, { status: 'IMPLEMENTED' });
    toast.add({ severity: 'success', summary: 'Success', detail: 'Feedback marked as resolved.', life: 3000 });
    fetchFeedbacks();
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to resolve feedback.', life: 3000 });
  } finally {
    loading.value = false;
  }
}

function handleCloseCreateDrawer() {
  showCreateDrawer.value = false;
}
function handleOpenFilter() {
  showFilterDrawer.value = true;
}
function handleApplyFilters(newFilters) {
  filters.value = newFilters;
  page.value = 1;
  fetchFeedbacks();
  showFilterDrawer.value = false;
}
function handleResetFilters() {
  filters.value = {
    searchTerm: '',
    selectedTypeFilter: [],
    selectedStatusFilter: [],
    dateRange: null
  };
  page.value = 1;
  fetchFeedbacks();
  showFilterDrawer.value = false;
}
function handleClearFilters() {
  handleResetFilters();
}
function handleExport() {}
</script>

<template>
  <div class="flex flex-1">
    <div class="p-4 flex flex-col flex-1 min-h-0">
      <Card class="flex flex-col flex-1 min-h-0">
        <template #content>
          <div class="flex flex-col flex-1 min-h-0">
            <FeedbackTable
              :feedbacks="feedbacks"
              :feedback-type-options="feedbackTypeOptions"
              :status-options="statusOptions"
              :search-term="filters.searchTerm"
              :selected-type-filter="filters.selectedTypeFilter"
              :selected-status-filter="filters.selectedStatusFilter"
              :date-range="filters.dateRange"
              :loading="loading"
              @create="handleCreate"
              @export="handleExport"
              @filter-change="handleApplyFilters"
              @clear-filters="handleClearFilters"
              @edit="handleEdit"
              @view="handleView"
              @resolve="handleResolve"
            />
          </div>
        </template>
      </Card>
    </div>
    <FeedbackCreateDrawer
      :visible="showCreateDrawer"
      :feedback="editingFeedback"
      @close="handleCloseCreateDrawer"
      @success="handleCreateSuccess"
      @update="handleUpdateFeedback"
    />
    <FeedbackViewDrawer
      :visible="showViewDrawer"
      :feedback="viewingFeedback"
      @close="handleCloseViewDrawer"
    />
    <FeedbackFilterDrawer
      :visible="showFilterDrawer"
      :filters="filters"
      :feedback-type-options="feedbackTypeOptions"
      :status-options="statusOptions"
      @close="() => showFilterDrawer.value = false"
      @apply-filters="handleApplyFilters"
      @reset-filters="handleResetFilters"
    />
    <SpeedDial
      :model="[
        { label: 'Create', icon: 'pi pi-plus', command: handleCreate },
        { label: 'Filter', icon: 'pi pi-filter', command: handleOpenFilter }
      ]"
      direction="up"
      :style="{ position: 'fixed', right: '1.5rem', bottom: '1.2rem', zIndex: 1000 }"
      :tooltipOptions="{ position: 'left' }"
      showIcon="pi pi-bars"
      hideIcon="pi pi-times"
    />
  </div>
</template> 