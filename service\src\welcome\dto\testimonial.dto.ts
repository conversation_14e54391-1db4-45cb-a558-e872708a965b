import { ApiProperty, ApiSchema } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsUUID, IsBoolean } from 'class-validator';

@ApiSchema({ name: 'CreateTestimonial' })
export class CreateTestimonialDto {
  @ApiProperty({
    description: 'The testimonial content',
    example: 'This platform has transformed how I find work opportunities',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'The name of the person giving the testimonial',
    example: '<PERSON>',
  })
  @IsString()
  @IsOptional()
  authorName: string;

  @ApiProperty({
    description: 'The role or title of the person giving the testimonial',
    example: 'Professional Plumber',
  })
  @IsString()
  @IsOptional()
  authorRole: string;

  @ApiProperty({
    description: 'The ID of the user giving the testimonial',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Whether the testimonial has been approved by an admin',
    example: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isApproved?: boolean;
}

@ApiSchema({ name: 'UpdateTestimonial' })
export class UpdateTestimonialDto {
  @ApiProperty({
    description: 'The testimonial content',
    example: 'This platform has transformed how I find work opportunities',
    required: false,
  })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({
    description: 'The name of the person giving the testimonial',
    example: 'John Doe',
    required: false,
  })
  @IsString()
  @IsOptional()
  authorName?: string;

  @ApiProperty({
    description: 'The role or title of the person giving the testimonial',
    example: 'Professional Plumber',
    required: false,
  })
  @IsString()
  @IsOptional()
  authorRole?: string;

  @ApiProperty({
    description: 'Whether the testimonial has been approved by an admin',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isApproved?: boolean;
}

@ApiSchema({ name: 'TestimonialResponse' })
export class TestimonialResponseDto {
  @ApiProperty({ description: 'Testimonial ID' })
  id: string;

  @ApiProperty({ description: 'Testimonial content' })
  content: string;

  @ApiProperty({ description: 'Author name' })
  authorName: string;

  @ApiProperty({ description: 'Author role' })
  authorRole: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({
    description: 'Whether the testimonial has been approved by an admin',
    example: false,
  })
  isApproved: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
