import {
  Controller,
  Post,
  Body,
  Get,
  Query,
  Param,
  UseGuards,
  Request,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { FeedbackService } from '../services/feedback.service';
import { CreateFeedbackDto } from '../dto/create-feedback.dto';
import { UpdateFeedbackDto } from '../dto/update-feedback.dto';
import { FeedbackEntity, FeedbackStatus, FeedbackType } from '../entities/feedback.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';

@ApiTags('Feedback')
@Controller('feedback')
export class FeedbackController {
  constructor(private readonly feedbackService: FeedbackService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Submit feedback or suggestion' })
  @ApiResponse({
    status: 201,
    description: 'Feedback submitted successfully',
    type: FeedbackEntity,
  })
  async create(
    @Body() createFeedbackDto: CreateFeedbackDto,
    @Request() req,
  ): Promise<FeedbackEntity> {
    return this.feedbackService.create(createFeedbackDto, req.user.id);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all feedback with pagination and filters' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'type', required: false, enum: FeedbackType })
  @ApiQuery({ name: 'status', required: false, enum: FeedbackStatus })
  @ApiQuery({ name: 'userId', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated feedback',
    type: [FeedbackEntity],
  })
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('type') type?: FeedbackType,
    @Query('status') status?: FeedbackStatus,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    return this.feedbackService.findAll(page, limit, type, status, userId, startDate, endDate);
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get feedback statistics' })
  @ApiResponse({
    status: 200,
    description: 'Returns feedback statistics',
  })
  async getStats() {
    return this.feedbackService.getFeedbackStats();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a specific feedback by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the feedback',
    type: FeedbackEntity,
  })
  async findOne(@Param('id') id: string): Promise<FeedbackEntity> {
    return this.feedbackService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update feedback status and admin response' })
  @ApiResponse({
    status: 200,
    description: 'Feedback updated successfully',
    type: FeedbackEntity,
  })
  async update(
    @Param('id') id: string,
    @Body() updateFeedbackDto: UpdateFeedbackDto,
    @Request() req,
  ): Promise<FeedbackEntity> {
    return this.feedbackService.update(id, updateFeedbackDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete feedback' })
  @ApiResponse({
    status: 200,
    description: 'Feedback deleted successfully',
  })
  async delete(@Param('id') id: string): Promise<void> {
    return this.feedbackService.delete(id);
  }
}
