// API Endpoints Configuration
export const ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    SEND_OTP: 'auth/login/email/otp',
    VERIFY_OTP: '/auth/login/email/verify',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile'
  },

  // Jobs
  JOBS: {
    LIST: '/jobs',
    DETAILS: (id) => `/jobs/${id}`,
    ENUMS: '/jobs/job-enums',
    INDUSTRIES: '/industries',
    TOGGLE_JOB_FAVORITE: (jobId) => `/job-applications/jobs/${jobId}/favorite`
  },

  // Applications
  APPLICATIONS: {
    LIST: '/applications',
    DETAILS: (id) => `/applications/${id}`,
    BY_JOB: (jobId) => `/jobs/${jobId}/applications`,
    BY_USER: '/job-applications/my-applications',
    WITHDRAW: (id) => `/job-applications/${id}/withdraw`,
    MY_FAVORITE: '/job-applications/my-favorites',
    CREATE: (jobId) => `/job-applications/jobs/${jobId}/apply`
  },

  // User Profile
  PROFILE: {
    GET: '/profile',
    UPDATE: '/profile',
    UPLOAD_AVATAR: '/profile/avatar',
    DELETE_AVATAR: '/profile/avatar',
    PRIVACY_SETTINGS: '/profile/privacy',
    PUBLIC: '/profile/public'
  },

  // Dashboard - Job Seeker specific endpoint
  DASHBOARD: {
    STATS: '/jobs/jobseeker/dashboard',
  },

  // Companies
  COMPANIES: {
    DETAILS: (id) => `/companies/${id}`,
  },

  // Notifications
  NOTIFICATIONS: {
    LIST: '/notifications',
    MARK_READ: (id) => `/notifications/read/${id}`,
    MARK_ALL_READ: '/notifications/mark-all-read',
  },

  // File Upload
  // UPLOAD: {
  //   RESUME: '/upload/resume',
  //   AVATAR: '/upload/avatar',
  //   DOCUMENTS: '/upload/documents'
  // }
}