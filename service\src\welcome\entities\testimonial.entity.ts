import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { WelcomeEntity } from './welcome.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('testimonials')
export class TestimonialEntity extends BaseEntity {
  @Column({ type: 'text' })
  @ApiProperty({ description: 'The testimonial content' })
  content: string;

  @Column({ type: 'varchar', nullable: true })
  @ApiProperty({
    description: 'The name of the person giving the testimonial',
    required: false,
    example: 'Anonymous',
  })
  authorName: string;

  @Column({ type: 'varchar', nullable: true })
  @ApiProperty({
    description: 'The role or title of the person giving the testimonial',
    required: false,
    example: 'User',
  })
  authorRole: string;

  @Column({ type: 'uuid' })
  @ApiProperty({ description: 'The ID of the user giving the testimonial' })
  userId: string;

  @Column({ type: 'boolean', default: false })
  @ApiProperty({
    description: 'Whether the testimonial has been approved by an admin',
    example: false,
  })
  isApproved: boolean;

  @ManyToOne(() => WelcomeEntity, (welcome) => welcome.testimonials)
  @JoinColumn({ name: 'welcome_id' })
  welcome: WelcomeEntity;
}
