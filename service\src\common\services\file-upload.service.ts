import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import * as sharp from 'sharp';

interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  hasAlpha: boolean;
  orientation?: number;
}

interface ProcessedImage {
  path: string;
  url: string;
  metadata: ImageMetadata;
}

@Injectable()
export class FileUploadService {
  private readonly uploadDir: string;
  private readonly maxFileSize: number;
  private readonly allowedMimeTypes: string[];
  private readonly imageSizes = {
    thumbnail: { width: 150, height: 150 },
    medium: { width: 400, height: 400 },
    large: { width: 800, height: 800 },
  };

  constructor(private readonly configService: ConfigService) {
    this.uploadDir = this.configService.get<string>('UPLOAD_DIR', 'uploads');
    this.maxFileSize = this.configService.get<number>('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
    this.allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
    this.ensureUploadDirExists();
  }

  private ensureUploadDirExists() {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  private validateFile(file: Express.Multer.File) {
    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File size exceeds the limit of ${this.maxFileSize / 1024 / 1024}MB`,
      );
    }

    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type not allowed. Allowed types: ${this.allowedMimeTypes.join(', ')}`,
      );
    }
  }

  private async extractMetadata(buffer: Buffer): Promise<ImageMetadata> {
    const metadata = await sharp(buffer).metadata();
    return {
      width: metadata.width || 0,
      height: metadata.height || 0,
      format: metadata.format || '',
      size: buffer.length,
      hasAlpha: metadata.hasAlpha || false,
      orientation: metadata.orientation,
    };
  }

  private async processImage(
    buffer: Buffer,
    size: { width: number; height: number },
    options: { crop?: boolean } = {},
  ): Promise<Buffer> {
    let processor = sharp(buffer);

    if (options.crop) {
      // Auto-crop to center
      const metadata = await processor.metadata();
      const { width, height } = metadata;
      if (width && height) {
        const cropSize = Math.min(width, height);
        const left = Math.floor((width - cropSize) / 2);
        const top = Math.floor((height - cropSize) / 2);
        processor = processor.extract({ left, top, width: cropSize, height: cropSize });
      }
    }

    return processor
      .resize(size.width, size.height, {
        fit: options.crop ? 'cover' : 'inside',
        withoutEnlargement: true,
      })
      .jpeg({ quality: 80 })
      .toBuffer();
  }

  async uploadFile(
    file: Express.Multer.File,
    subDir: string = '',
    options: { crop?: boolean } = {},
  ): Promise<ProcessedImage[]> {
    this.validateFile(file);

    const uploadPath = path.join(this.uploadDir, subDir);
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    const baseFileName = uuidv4();
    const results: ProcessedImage[] = [];

    // Process each size
    for (const [size, dimensions] of Object.entries(this.imageSizes)) {
      const processedBuffer = await this.processImage(file.buffer, dimensions, options);
      const fileName = `${baseFileName}_${size}.jpg`;
      const filePath = path.join(uploadPath, fileName);
      const relativePath = path.join(subDir, fileName).replace(/\\/g, '/');

      await fs.promises.writeFile(filePath, processedBuffer);
      const metadata = await this.extractMetadata(processedBuffer);

      results.push({
        path: relativePath,
        url: this.getFileUrl(relativePath),
        metadata,
      });
    }

    return results;
  }

  async deleteFile(filePath: string): Promise<void> {
    const fullPath = path.join(this.uploadDir, filePath);
    if (fs.existsSync(fullPath)) {
      await fs.promises.unlink(fullPath);
    }
  }

  getFileUrl(filePath: string): string {
    const baseUrl = this.configService.get<string>('BASE_URL', 'http://localhost:3000');
    return `${baseUrl}/${filePath}`;
  }
}
