import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;
  private readonly OTP_EXPIRY_MINUTES = 10;

  constructor(private readonly configService: ConfigService) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    try {
      this.transporter = nodemailer.createTransport({
        host: this.configService.get('SMTP_HOST'),
        port: this.configService.get('SMTP_PORT'),
        secure: this.configService.get('SMTP_SECURE') === 'true',
        auth: {
          user: this.configService.get('SMTP_USER'),
          pass: this.configService.get('SMTP_PASS'),
        },
      });

      // Verify transporter configuration
      this.transporter.verify((error) => {
        if (error) {
          this.logger.error('SMTP configuration error:', error);
        } else {
          this.logger.log('SMTP server is ready to send emails');
        }
      });
    } catch (error) {
      this.logger.error('Failed to initialize email transporter:', error);
      throw error;
    }
  }

  private generateEmailTemplate(otp: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Your Login OTP</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
            }
            .container {
              background-color: #f9f9f9;
              border-radius: 5px;
              padding: 20px;
              margin: 20px 0;
            }
            .otp-code {
              font-size: 32px;
              font-weight: bold;
              color: #007bff;
              text-align: center;
              padding: 20px;
              background-color: #e9ecef;
              border-radius: 5px;
              margin: 20px 0;
            }
            .footer {
              font-size: 12px;
              color: #666;
              text-align: center;
              margin-top: 20px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>Your Login OTP</h2>
            <p>Please use the following OTP to complete your login:</p>
            <div class="otp-code">${otp}</div>
            <p>This OTP will expire in ${this.OTP_EXPIRY_MINUTES} minutes.</p>
            <p>If you didn't request this OTP, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </body>
      </html>
    `;
  }

  private generateWelcomeEmailTemplate(name: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Job Dalal!</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
            }
            .container {
              background-color: #f9f9f9;
              border-radius: 5px;
              padding: 20px;
              margin: 20px 0;
            }
            .welcome-header {
              font-size: 24px;
              font-weight: bold;
              color: #007bff;
              text-align: center;
              margin: 20px 0;
            }
            .footer {
              font-size: 12px;
              color: #666;
              text-align: center;
              margin-top: 20px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="welcome-header">Welcome to Job Dalal!</div>
            <p>Dear ${name},</p>
            <p>Thank you for joining Job Dalal! We're excited to have you on board.</p>
            <p>With Job Dalal, you can:</p>
            <ul>
              <li>Find job opportunities that match your skills</li>
              <li>Connect with employers directly</li>
              <li>Build your professional profile</li>
              <li>Get verified for better job prospects</li>
            </ul>
            <p>Get started by completing your profile and exploring available jobs.</p>
            <p>If you have any questions, our support team is here to help!</p>
          </div>
          <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </body>
      </html>
    `;
  }

  async sendOtpEmail(email: string, otp: string): Promise<void> {
    try {
      if (!this.transporter) {
        this.logger.error('SMTP transporter not initialized');
        throw new Error('Email service is not properly configured.');
      }

      const mailOptions = {
        from: `"Job Dalal" <${this.configService.get('SMTP_USER')}>`,
        to: email,
        subject: 'Your Login OTP - Job Dalal',
        html: this.generateEmailTemplate(otp),
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`OTP email sent to ${email}. Message ID: ${info.messageId}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${email}:`, {
        error: error.message,
        stack: error.stack,
        smtpConfig: {
          host: this.configService.get('SMTP_HOST'),
          port: this.configService.get('SMTP_PORT'),
          secure: this.configService.get('SMTP_SECURE'),
          user: this.configService.get('SMTP_USER'),
        },
      });
      throw new Error('Failed to send OTP email. Please try again later.');
    }
  }

  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    try {
      if (!this.transporter) {
        this.logger.error('SMTP transporter not initialized');
        throw new Error('Email service is not properly configured.');
      }

      const mailOptions = {
        from: `"Job Dalal" <${this.configService.get('SMTP_USER')}>`,
        to: email,
        subject: 'Welcome to Job Dalal!',
        html: this.generateWelcomeEmailTemplate(name),
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Welcome email sent to ${email}. Message ID: ${info.messageId}`);
    } catch (error) {
      this.logger.error(`Failed to send welcome email to ${email}:`, {
        error: error.message,
        stack: error.stack,
        smtpConfig: {
          host: this.configService.get('SMTP_HOST'),
          port: this.configService.get('SMTP_PORT'),
          secure: this.configService.get('SMTP_SECURE'),
          user: this.configService.get('SMTP_USER'),
        },
      });
      throw new Error('Failed to send welcome email. Please try again later.');
    }
  }
}
