<script setup>
import { defineProps, defineEmits } from 'vue';
import Drawer from 'primevue/drawer';
import Tag from 'primevue/tag';
import Button from 'primevue/button';

const props = defineProps({
  feedback: Object,
  visible: Boole<PERSON>
});
const emit = defineEmits(['close']);

function formatDate(date) {
  if (!date) return '';
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
}
</script>

<template>
  <Drawer :visible="visible" @update:visible="emit('close')" position="right" style="width: 400px">
    <template #header>
      <h3>Feedback Details</h3>
    </template>
    <div v-if="feedback" class="p-4 flex flex-col gap-4">
      <div>
        <label class="font-semibold">Type</label>
        <div class="mt-1"><Tag :value="feedback.type" /></div>
      </div>
      <div>
        <label class="font-semibold">Title</label>
        <div class="mt-1">{{ feedback.title }}</div>
      </div>
      <div>
        <label class="font-semibold">Description</label>
        <div class="mt-1">{{ feedback.description }}</div>
      </div>
      <div>
        <label class="font-semibold">Status</label>
        <div class="mt-1"><Tag :value="feedback.status" /></div>
      </div>
      <div>
        <label class="font-semibold">Submitted By</label>
        <div class="mt-1">{{ feedback.submittedBy }}</div>
      </div>
      <div>
        <label class="font-semibold">Created At</label>
        <div class="mt-1">{{ formatDate(feedback.createdAt) }}</div>
      </div>
      <div>
        <label class="font-semibold">Updated At</label>
        <div class="mt-1">{{ formatDate(feedback.updatedAt) }}</div>
      </div>
      <div>
        <label class="font-semibold">Metadata</label>
        <pre class="bg-gray-100 p-2 rounded mt-1 overflow-auto"><code>{{ JSON.stringify(feedback.metadata, null, 2) }}</code></pre>
      </div>
    </div>
    <div v-else class="p-4">No feedback selected.</div>
    <template #footer>
      <Button label="Close" severity="secondary" @click="emit('close')" />
    </template>
  </Drawer>
</template> 