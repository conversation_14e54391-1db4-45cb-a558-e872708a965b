import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const authService = {
  // Send OTP to email - using separate mock file
  async login(params) {
    try {
      const response = await httpClient.post(ENDPOINTS.AUTH.LOGIN, params)
      // handle failure response
      return response;
    } catch (error) {
      return error
    }
  },

  async getProfile() {
    try {
      const res = await httpClient.get(ENDPOINTS.AUTH.PROFILE);

      return res;
    } catch (error) {
      return error
    }
  },

  // Logout (placeholder for future implementation)
  async logout() {
    try {
      // TODO: Implement with separate mock file
      const res = await httpClient.post(ENDPOINTS.AUTH.LOGOUT);

      return res
    } catch (error) {
      console.warn('Logout API call failed:', error)
      
    }
  },

  // Refresh token (placeholder for future implementation)
  async refreshToken(token) {
    try {
      // TODO: Implement with separate mock file
      throw new Error('Token refresh not implemented yet')
    } catch (error) {
      throw new Error(error.message || 'Token refresh failed')
    }
  },
}
