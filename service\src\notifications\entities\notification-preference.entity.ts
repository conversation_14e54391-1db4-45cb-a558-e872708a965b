import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, Column, OneTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>umn } from 'typeorm';
import { UserEntity } from '../../users/entities/user.entity';
import { NotificationType } from './notification.entity';

@Entity('notification_preferences')
export class NotificationPreference {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn()
  user: UserEntity;

  @Column()
  userId: string;

  @Column('jsonb', { default: {} })
  emailPreferences: {
    [key in NotificationType]: boolean;
  };

  @Column('jsonb', { default: {} })
  pushPreferences: {
    [key in NotificationType]: boolean;
  };

  @Column('jsonb', { default: {} })
  inAppPreferences: {
    [key in NotificationType]: boolean;
  };

  @Column({ default: true })
  receiveJobAlerts: boolean;

  @Column({ default: true })
  receiveApplicationUpdates: boolean;

  @Column({ default: true })
  receiveSystemNotifications: boolean;

  @Column({ default: false })
  emailDigest: boolean;

  @Column({ default: 'DAILY' })
  emailDigestFrequency: 'DAILY' | 'WEEKLY' | 'NEVER';
}
