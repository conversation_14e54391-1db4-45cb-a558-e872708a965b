import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DescriptionEntity } from '../entities/description.entity';
import { CreateDescriptionDto } from '../dto/description.dto';
import { UpdateDescriptionDto } from '../dto/description.dto';
import { WelcomeEntity } from '../entities/welcome.entity';

@Injectable()
export class DescriptionService {
  constructor(
    @InjectRepository(DescriptionEntity)
    private readonly descriptionRepository: Repository<DescriptionEntity>,
    @InjectRepository(WelcomeEntity)
    private readonly welcomeRepository: Repository<WelcomeEntity>,
  ) {}

  async create(welcomeId: string, createDto: CreateDescriptionDto): Promise<DescriptionEntity> {
    const welcome = await this.welcomeRepository.findOne({ where: { id: welcomeId } });
    if (!welcome) {
      throw new NotFoundException('Welcome not found');
    }

    const description = this.descriptionRepository.create({
      ...createDto,
      welcome,
    });

    return this.descriptionRepository.save(description);
  }

  async findAll(welcomeId: string): Promise<DescriptionEntity[]> {
    return this.descriptionRepository.find({
      where: { welcome: { id: welcomeId } },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<DescriptionEntity> {
    const description = await this.descriptionRepository.findOne({ where: { id } });
    if (!description) {
      throw new NotFoundException('Description not found');
    }
    return description;
  }

  async update(id: string, updateDto: UpdateDescriptionDto): Promise<DescriptionEntity> {
    const description = await this.findOne(id);

    Object.assign(description, updateDto);
    return this.descriptionRepository.save(description);
  }

  async delete(id: string): Promise<void> {
    const description = await this.findOne(id);
    await this.descriptionRepository.remove(description);
  }

  async deleteAll(welcomeId: string): Promise<void> {
    await this.descriptionRepository.delete({ welcome: { id: welcomeId } });
  }
}
