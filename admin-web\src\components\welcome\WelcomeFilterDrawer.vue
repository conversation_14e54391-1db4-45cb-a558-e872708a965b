<template>
  <Drawer 
    :visible="modelValue" 
    position="right" 
    :modal="true"
    :dismissable="true"
    :showCloseIcon="true"
    :style="{ width: '400px' }" 
    @update:visible="onDrawerVisible" 
    @hide="onClose"
  >
    <template #header>
      <span>Filter Welcome Pages</span>
    </template>
    <form @submit.prevent="onApply">
      <div class="form-group">
        <label>Company Name</label>
        <InputText v-model="filters.cmp_name" class="w-full" placeholder="Company name" />
      </div>
      <div class="form-group">
        <label>Show Signup</label>
        <Select v-model="filters.show_signup" :options="yesNoOptions" option-label="label" option-value="value" class="w-full" placeholder="Any" />
      </div>
      <div class="form-group">
        <label>Show Login</label>
        <Select v-model="filters.show_login" :options="yesNoOptions" option-label="label" option-value="value" class="w-full" placeholder="Any" />
      </div>
      <div class="form-group">
        <label>Base URL</label>
        <InputText v-model="filters.base_url" class="w-full" placeholder="Base URL" />
      </div>
      <div class="form-group">
        <label>Message</label>
        <InputText v-model="filters.message" class="w-full" placeholder="Welcome message" />
      </div>
      <!-- Add more fields as needed -->
      <div class="form-actions">
        <Button type="submit" label="Apply" icon="pi pi-filter" class="mr-2" />
        <Button type="button" label="Reset" icon="pi pi-refresh" severity="secondary" @click="onReset" />
        <Button type="button" label="Close" icon="pi pi-times" severity="secondary" @click="onClose" />
      </div>
    </form>
  </Drawer>
</template>

<script setup>
import Drawer from 'primevue/drawer'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'
import Button from 'primevue/button'
import { reactive, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  initialFilters: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:modelValue', 'apply', 'reset', 'close'])

const yesNoOptions = [
  { label: 'Any', value: null },
  { label: 'Yes', value: true },
  { label: 'No', value: false }
]

const filters = reactive({
  cmp_name: '',
  show_signup: null,
  show_login: null,
  base_url: '',
  message: ''
})

watch(() => props.initialFilters, (val) => {
  if (val) Object.assign(filters, val)
}, { immediate: true })

const onApply = () => {
  emit('apply', { ...filters })
  emit('update:modelValue', false)
}
const onReset = () => {
  Object.assign(filters, {
    cmp_name: '',
    show_signup: null,
    show_login: null,
    base_url: '',
    message: ''
  })
  emit('reset')
}
const onClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
const onDrawerVisible = (val) => {
  emit('update:modelValue', val)
}
</script>

<style scoped>
.form-group {
  margin-bottom: 1rem;
}
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}
</style> 