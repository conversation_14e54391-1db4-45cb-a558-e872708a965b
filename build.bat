#@echo off
setlocal enabledelayedexpansion

echo Switching to Node-22
nvm use 22

echo ============================================
echo Cleaning dist folders...
echo ============================================

cd service\public

for %%D in (admin employer welcome jobseeker) do (
    echo Removing dist from %%D...
    if exist %%D\dist (
        rmdir /s /q %%D\dist
    )
)

cd ..\..

echo ============================================
echo Building admin-web...
echo ============================================
cd admin-web
call npm i
call npm run build || echo Failed to build admin-web

echo Removing old dist from service\public\admin...
rmdir /s /q ..\service\public\admin\dist

echo Copying dist to service\public\admin...
xcopy /E /I /Y dist ..\service\public\admin\dist
cd ..

echo ============================================
echo Building employer-web...
echo ============================================
cd employer-web
call npm i
call npm run build-only || echo Failed to build employer-web

echo Removing old dist from service\public\employer...
rmdir /s /q ..\service\public\employer\dist

echo Copying dist to service\public\employer...
xcopy /E /I /Y dist ..\service\public\employer\dist
cd ..

echo ============================================
echo Building jobseeker-web...
echo ============================================
cd jobseeker-web
call npm i
call npm run build-only || echo Failed to build jobseeker-web

echo Removing old dist from service\public\jobseeker...
rmdir /s /q ..\service\public\jobseeker\dist

echo Copying dist to service\public\jobseeker...
xcopy /E /I /Y dist ..\service\public\jobseeker\dist
cd ..

echo ============================================
echo Building Landing-Page...
echo ============================================
cd welcome
call npm i
call npm run build-only || echo Failed to build welcome

echo Removing old dist from service\public\welcome...
rmdir /s /q ..\service\public\welcome\dist

echo Copying dist to service\public\welcome...
xcopy /E /I /Y dist ..\service\public\welcome\dist
cd ..

echo ============================================
echo Building service...
echo ============================================
cd service
call npm i
call npm run build || echo Failed to build service
cd ..

echo ============================================
echo ✅ All builds completed.
echo ============================================

endlocal
pause
