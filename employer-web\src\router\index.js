import { nextTick } from 'vue';
import { createRouter, createWeb<PERSON>istory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'


import { useLoader } from '@/composables/useLoader';

const routes = [
  {
    path: '/',
    redirect: '/employer'
  },
  {
    path: '/employer/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      app: 'Employer',
      title: 'Login'
    }
  },
  {
    path: '/employer',
    component: () => import('@/views/RootLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          app: 'Employer',
          requiresAuth: true,
          title: 'Dashboard'
        },
      },
      {
        path: 'jobs',
        name: 'JobSearch',
        component: () => import('@/views/JobsList.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'My Jobs'
        }
      },
      {
        path: 'jobs/create',
        name: 'createJob',
        component: () => import('@/views/CreateJob.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Create Job'
        }
      },
      {
        path: 'jobs/saved',
        name: 'SavedJobs',
        component: () => import('@/views/SavedJobs.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Saved Jobs'
        }
      },
      {
        path: 'jobs/:id',
        name: 'JobDetails',
        component: () => import('@/views/JobDetails.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Job Details'
        }
      },
      {
        path: 'jobs/:id/edit',
        name: 'editJob',
        component:  () => import('@/views/CreateJob.vue'),
        props: route => ({ 
          editMode: true, 
          jobId: route.params.id 
        }),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Edit Job'
        }
      },
      {
        path: 'applications',
        name: 'Applicants',
        component: () => import('@/views/Applicants.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Applicants'
        }
      },
      {
        path: 'profile',
        name: 'employerProfile',
        component: () => import('@/views/Profile.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Profile'
        }
      },
      {
        path: 'notifications',
        name: 'Notifications',
        component: () => import('@/views/Notifications.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Notifications'
        }
      },
      {
        path: 'applicant-details/:id',
        name: 'applicantDetails',
        component: () => import('@/views/ApplicantDetails.vue'),
        meta: { 
          requiresAuth: true, 
          app: 'Employer',
          title: 'Applicant Details'
        }
      },
    ],
    redirect: '/employer/dashboard',
  },
  {
    path: '/employer/:catchAll(.*)',
    redirect: '/employer/dashboard',
  },
  // {
  //   path: '/employer/jobs/saved',
  //   name: 'SavedJobs',
  //   component: SavedJobs,
  //   meta: { requiresAuth: true, app: 'employer' }
  // },
  // {
  //   path: '/employer/jobs/:id',
  //   name: 'JobDetails',
  //   component: JobDetails,
  //   meta: { requiresAuth: true, app: 'employer' }
  // },
  // {
  //   path: '/employer/jobs/:id/edit',
  //   name: 'EditJob',
  //   component: CreateJob,
  //   props: route => ({ editMode: true, jobId: route.params.id }),
  //   meta: { requiresAuth: true, app: 'employer' }
  // },
  // {
  //   path: '/employer/applications',
  //   name: 'MyApplications',
  //   component: Applications,
  //   meta: { requiresAuth: true, app: 'employer' }
  // },
  // {
  //   path: '/employer/profile',
  //   name: 'employerProfile',
  //   component: Profile,
  //   meta: { requiresAuth: true, app: 'employer' }
  // },
  // {
  //   path: '/employer/notifications',
  //   name: 'Notifications',
  //   component: Notifications,
  //   meta: { requiresAuth: true, app: 'employer' }
  // },

  // {
  //   path: '/employer/jobs/create',
  //   name: 'createJob',
  //   component: CreateJob,
  //   meta: { requiresAuth: true, app: 'employer' }
  // },
]

const {startLoading, stopLoading } = useLoader();

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  startLoading();
  
  const authStore = useAuthStore()

  if (!authStore.isInitialised) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.log('Failed to get the profile')
    }
  }

  if (authStore.isAuthenticated) {
    if (!authStore.enums) {
      await authStore.getEnumData()
    }

    if (!authStore.industries) {
      await authStore.getIndustries()
    }
  }

  // Set document title based on route meta or route name
  if (to.meta?.title) {
    document.title = `${to.meta.app} - ${to.meta.title} - Job Dalal`
  } else if (to.name) {
    // Convert route name to title case
    const title = to.name
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .trim()
    document.title = `Employer - ${title} - Job Dalal`
  } else {
    document.title = 'Employer - Job Dalal'
  }

  if (!authStore.isAuthenticated && to?.path !== '/employer/login') {
    next('/employer/login')
  } else {
    if (false && authStore.isAuthenticated && !authStore.isProfileComplete && to?.path !== '/employer/profile') {
      // For Now disabling the checks
      next('/employer/profile')
    } else {
      next()
    }
  }
})

router.afterEach(() => {
  nextTick(() => {
    stopLoading()
  });
});

export default router