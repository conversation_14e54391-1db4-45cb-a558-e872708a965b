import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { TestimonialService } from '../services/testimonial.service';
import {
  CreateTestimonialDto,
  UpdateTestimonialDto,
  ApproveTestimonialDto,
} from '../dto/testimonial.dto';

@ApiTags('Testimonials')
@Controller('testimonials')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class TestimonialController {
  constructor(private readonly testimonialService: TestimonialService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new testimonial' })
  @ApiResponse({ status: 201, description: 'Testimonial created successfully' })
  async create(@Request() req, @Body() createTestimonialDto: CreateTestimonialDto) {
    return this.testimonialService.create(req.user.id, createTestimonialDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all testimonials' })
  @ApiQuery({ name: 'approvedOnly', type: Boolean, required: false })
  @ApiResponse({ status: 200, description: 'List of testimonials' })
  async findAll(@Query('approvedOnly') approvedOnly?: boolean) {
    return this.testimonialService.findAll(approvedOnly);
  }

  @Get('average-rating')
  @ApiOperation({ summary: 'Get average rating of approved testimonials' })
  @ApiResponse({ status: 200, description: 'Average rating' })
  async getAverageRating() {
    return this.testimonialService.getAverageRating();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a testimonial by ID' })
  @ApiResponse({ status: 200, description: 'Testimonial details' })
  async findOne(@Param('id') id: string) {
    return this.testimonialService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a testimonial' })
  @ApiResponse({ status: 200, description: 'Testimonial updated successfully' })
  async update(
    @Param('id') id: string,
    @Request() req,
    @Body() updateTestimonialDto: UpdateTestimonialDto,
  ) {
    return this.testimonialService.update(id, req.user.id, updateTestimonialDto);
  }

  @Patch(':id/approve')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Approve or reject a testimonial (Admin only)' })
  @ApiResponse({ status: 200, description: 'Testimonial approval status updated' })
  async approve(@Param('id') id: string, @Body() approveTestimonialDto: ApproveTestimonialDto) {
    return this.testimonialService.approve(id, approveTestimonialDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a testimonial' })
  @ApiResponse({ status: 200, description: 'Testimonial deleted successfully' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.testimonialService.remove(id, req.user.id);
  }
}
