import { Test, TestingModule } from '@nestjs/testing';
import { WelcomeController } from './welcome.controller';
import { WelcomeService } from '../services/welcome.service';
import { CreateWelcomeDto } from '../dto/create-welcome.dto';
import { UpdateWelcomeDto } from '../dto/update-welcome.dto';
import { UserRole } from '../../users/enums/user.enum';

describe('WelcomeController', () => {
  let controller: WelcomeController;

  const mockWelcomeService = {
    create: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  };

  const mockUser = {
    id: 'user-id',
    role: UserRole.ADMIN,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WelcomeController],
      providers: [
        {
          provide: WelcomeService,
          useValue: mockWelcomeService,
        },
      ],
    }).compile();

    controller = module.get<WelcomeController>(WelcomeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a welcome message', async () => {
      const createDto: CreateWelcomeDto = {
        message: 'Welcome message',
        imageUrl: 'https://example.com/image.jpg',
      };

      const expectedResult = {
        id: 'welcome-id',
        ...createDto,
        updatedBy: mockUser.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockWelcomeService.create.mockResolvedValue(expectedResult);

      const result = await controller.create({ user: mockUser }, createDto);

      expect(result).toEqual(expectedResult);
      expect(mockWelcomeService.create).toHaveBeenCalledWith(mockUser.id, createDto);
    });
  });

  describe('findOne', () => {
    it('should return a welcome message', async () => {
      const expectedResult = {
        id: 'welcome-id',
        message: 'Welcome message',
        imageUrl: 'https://example.com/image.jpg',
        updatedBy: mockUser.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockWelcomeService.findOne.mockResolvedValue(expectedResult);

      const result = await controller.findOne();

      expect(result).toEqual(expectedResult);
      expect(mockWelcomeService.findOne).toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should update a welcome message', async () => {
      const updateDto: UpdateWelcomeDto = {
        message: 'Updated welcome message',
      };

      const expectedResult = {
        id: 'welcome-id',
        ...updateDto,
        updatedBy: mockUser.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockWelcomeService.update.mockResolvedValue(expectedResult);

      const result = await controller.update({ user: mockUser }, updateDto);

      expect(result).toEqual(expectedResult);
      expect(mockWelcomeService.update).toHaveBeenCalledWith(mockUser.id, updateDto);
    });
  });
});
