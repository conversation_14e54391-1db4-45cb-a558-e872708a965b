import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UseGuards, Logger } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { DashboardService } from '../services/dashboard.service';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../services/user.service';
import { AdminActivityService } from '../services/admin-activity.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: 'dashboard',
  pingTimeout: 60000,
  pingInterval: 25000,
})
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
export class DashboardGateway implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(DashboardGateway.name);
  private connectedClients: Map<string, Socket> = new Map();
  private updateInterval: NodeJS.Timeout;
  private readonly MAX_RECONNECT_ATTEMPTS = 5;
  private readonly RECONNECT_DELAY = 5000;

  constructor(
    private readonly dashboardService: DashboardService,
    private readonly jwtService: JwtService,
    private readonly userService: UserService,
    private readonly adminActivityService: AdminActivityService,
  ) {
    this.startPeriodicUpdates();
  }

  afterInit() {
    this.logger.log('Dashboard WebSocket Gateway initialized');
  }

  private async validateToken(token: string): Promise<any> {
    try {
      const payload = await this.jwtService.verifyAsync(token);
      const user = await this.userService.findOne(payload.sub);
      if (!user || ![UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(user.role)) {
        throw new WsException('Unauthorized access');
      }
      return user;
    } catch {
      throw new WsException('Invalid token');
    }
  }

  private startPeriodicUpdates() {
    this.updateInterval = setInterval(async () => {
      if (this.connectedClients.size > 0) {
        try {
          const updates = await this.getDashboardUpdates();
          this.server.emit('dashboard-update', updates);
        } catch (error) {
          this.logger.error('Error in periodic update:', error);
          this.server.emit('dashboard-error', {
            message: 'Failed to fetch dashboard updates',
            timestamp: new Date(),
          });
        }
      }
    }, 30000);
  }

  private async getDashboardUpdates() {
    try {
      const [
        userGrowth,
        subscriptionStats,
        roleDistribution,
        adminActivity,
        userEngagement,
        retentionRates,
      ] = await Promise.all([
        this.dashboardService.getUserGrowth(7),
        this.dashboardService.getSubscriptionStats(),
        this.dashboardService.getRoleDistribution(),
        this.dashboardService.getAdminActivity(7),
        this.dashboardService.getUserEngagementMetrics(7),
        this.dashboardService.getRetentionRates(7),
      ]);

      return {
        timestamp: new Date(),
        data: {
          userGrowth,
          subscriptionStats,
          roleDistribution,
          adminActivity,
          userEngagement,
          retentionRates,
        },
      };
    } catch (error) {
      this.logger.error('Error fetching dashboard updates:', error);
      throw new WsException('Failed to fetch dashboard updates');
    }
  }

  async handleConnection(client: Socket) {
    try {
      const token = client.handshake.auth.token;
      if (!token) {
        throw new WsException('Authentication token required');
      }

      const user = await this.validateToken(token);
      client.data.user = user;
      this.connectedClients.set(client.id, client);
      this.logger.log(`Client connected: ${client.id} (User: ${user.email})`);

      // Send initial data
      const updates = await this.getDashboardUpdates();
      client.emit('dashboard-update', updates);
    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`);
      client.emit('error', {
        message: error.message,
        timestamp: new Date(),
      });
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('request-update')
  async handleRequestUpdate(client: Socket) {
    try {
      const updates = await this.getDashboardUpdates();
      client.emit('dashboard-update', updates);
    } catch (error) {
      client.emit('error', {
        message: error.message,
        timestamp: new Date(),
      });
    }
  }

  @SubscribeMessage('reconnect')
  async handleReconnect(client: Socket, attempt: number) {
    if (attempt >= this.MAX_RECONNECT_ATTEMPTS) {
      client.emit('error', {
        message: 'Maximum reconnection attempts reached',
        timestamp: new Date(),
      });
      return;
    }

    setTimeout(async () => {
      try {
        const updates = await this.getDashboardUpdates();
        client.emit('dashboard-update', updates);
        client.emit('reconnected', {
          attempt,
          timestamp: new Date(),
        });
      } catch (error) {
        client.emit('error', {
          message: error.message,
          timestamp: new Date(),
        });
      }
    }, this.RECONNECT_DELAY * attempt);
  }

  @SubscribeMessage('getDashboardOverview')
  async handleGetDashboardOverview() {
    const overview = await this.dashboardService.getDashboardOverview();
    return { event: 'dashboardOverview', data: overview };
  }

  @SubscribeMessage('getUserGrowth')
  async handleGetUserGrowth(client: Socket, days: number) {
    const growth = await this.dashboardService.getUserGrowth(days);
    return { event: 'userGrowth', data: growth };
  }

  @SubscribeMessage('getSubscriptionStats')
  async handleGetSubscriptionStats() {
    const stats = await this.dashboardService.getSubscriptionStats();
    return { event: 'subscriptionStats', data: stats };
  }

  @SubscribeMessage('getRoleDistribution')
  async handleGetRoleDistribution() {
    const distribution = await this.dashboardService.getRoleDistribution();
    return { event: 'roleDistribution', data: distribution };
  }

  @SubscribeMessage('getAdminActivity')
  async handleGetAdminActivity(client: Socket, days: number) {
    const activity = await this.dashboardService.getAdminActivity(days);
    return { event: 'adminActivity', data: activity };
  }

  @SubscribeMessage('getUserEngagement')
  async handleGetUserEngagement(client: Socket, days: number) {
    const engagement = await this.dashboardService.getUserEngagementMetrics(days);
    return { event: 'userEngagement', data: engagement };
  }

  @SubscribeMessage('getRetentionRates')
  async handleGetRetentionRates(client: Socket, days: number) {
    const rates = await this.dashboardService.getRetentionRates(days);
    return { event: 'retentionRates', data: rates };
  }

  onModuleDestroy() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    this.logger.log('Dashboard WebSocket Gateway destroyed');
  }
}
