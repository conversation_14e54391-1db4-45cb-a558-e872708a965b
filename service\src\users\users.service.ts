import {
  Injectable,
  ConflictException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from './entities/user.entity';
import { ProfileEntity } from './entities/profile.entity';
import {
  PhoneSignupDto,
  EmailSignupDto,
  VerifyPhoneOtpDto,
  VerifyEmailOtpDto,
} from './dto/auth.dto';
import { UpdateProfileDto } from './dto/profile.dto';
import { EmailService } from '../email/email.service';
import { SmsService } from '../sms/sms.service';
import { AuthService } from '../auth/auth.service';
import { Throttle } from '@nestjs/throttler';
import { UserRole, SubscriptionType } from './enums/user.enum';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(ProfileEntity)
    private readonly profileRepository: Repository<ProfileEntity>,
    private readonly emailService: EmailService,
    private readonly smsService: SmsService,
    private readonly authService: AuthService,
  ) {}

  @Throttle({ default: { limit: 3, ttl: 60 } })
  async initiatePhoneSignup(phoneSignupDto: PhoneSignupDto) {
    const existingUser = await this.userRepository.findOne({
      where: { phone: phoneSignupDto.phone },
    });

    if (existingUser) {
      throw new ConflictException('Phone number already registered');
    }

    // Create user with name fields
    const user = this.userRepository.create({
      phone: phoneSignupDto.phone,
      firstName: phoneSignupDto.firstName,
      lastName: phoneSignupDto.lastName,
      middleName: phoneSignupDto.middleName,
    });
    await this.userRepository.save(user);

    // Send verification code
    await this.smsService.sendOtpSms(phoneSignupDto.phone, '');

    return { message: 'Verification code sent successfully. Please check your phone.' };
  }

  @Throttle({ default: { limit: 3, ttl: 60 } })
  async resendPhoneVerification(phone: string) {
    const user = await this.userRepository.findOne({
      where: { phone },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.isPhoneVerified) {
      throw new BadRequestException('Phone number is already verified');
    }

    await this.smsService.resendOtp(phone);

    return { message: 'Verification code resent successfully. Please check your phone.' };
  }

  @Throttle({ default: { limit: 3, ttl: 60 } })
  async initiateEmailSignup(emailSignupDto: EmailSignupDto) {
    const existingUser = await this.userRepository.findOne({
      where: { email: emailSignupDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already registered');
    }

    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const user = this.userRepository.create({
      email: emailSignupDto.email,
      firstName: emailSignupDto.firstName,
      lastName: emailSignupDto.lastName,
      middleName: emailSignupDto.middleName,
      emailOtp: otp,
      emailOtpExpiry: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
    });

    await this.userRepository.save(user);
    await this.emailService.sendOtpEmail(emailSignupDto.email, otp);

    return { message: 'OTP sent successfully. Please check your email.' };
  }

  async verifyPhoneOtp(verifyDto: VerifyPhoneOtpDto) {
    const user = await this.userRepository.findOne({
      where: { phone: verifyDto.phone },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.isPhoneVerified) {
      throw new BadRequestException('Phone number is already verified');
    }

    // Verify the code using Twilio Verify
    const isValid = await this.smsService.verifyOtp(verifyDto.phone, verifyDto.otp);

    if (!isValid) {
      throw new BadRequestException('Invalid verification code');
    }

    user.isPhoneVerified = true;
    await this.userRepository.save(user);

    const token = this.authService.generateToken(user);
    return {
      message: 'Phone number verified successfully',
      token,
    };
  }

  async verifyEmailOtp(verifyDto: VerifyEmailOtpDto) {
    const user = await this.userRepository.findOne({
      where: { email: verifyDto.email },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.emailOtp || !user.emailOtpExpiry) {
      throw new BadRequestException('No OTP requested');
    }

    if (user.emailOtp !== verifyDto.otp) {
      throw new BadRequestException('Invalid OTP');
    }

    if (user.emailOtpExpiry < new Date()) {
      throw new BadRequestException('OTP expired');
    }

    user.isEmailVerified = true;
    user.emailOtp = null;
    user.emailOtpExpiry = null;
    await this.userRepository.save(user);

    const token = this.authService.generateToken(user);
    return {
      message: 'Email verified successfully',
      token,
    };
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    let profile = user.profile;
    if (!profile) {
      profile = this.profileRepository.create({ user });
    }

    // Update profile fields
    Object.assign(profile, updateProfileDto);
    await this.profileRepository.save(profile);

    // Check if profile is complete
    const isProfileComplete = this.isProfileComplete(profile);
    if (isProfileComplete !== user.isProfileComplete) {
      user.isProfileComplete = isProfileComplete;
      await this.userRepository.save(user);
    }

    return {
      message: 'Profile updated successfully',
      profile,
      isProfileComplete,
    };
  }

  private isProfileComplete(profile: ProfileEntity): boolean {
    return !!(
      profile.address &&
      profile.city &&
      profile.state &&
      profile.country &&
      profile.postalCode &&
      profile.profileImage
    );
  }

  async verifyAadhar(userId: string, aadharNumber: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // TODO: Implement actual Aadhar verification logic
    // This is a placeholder for the actual verification process
    const isValid = await this.validateAadharNumber(aadharNumber);

    if (!isValid) {
      throw new BadRequestException('Invalid Aadhar number');
    }

    user.isAadharVerified = true;
    await this.userRepository.save(user);

    return {
      message: 'Aadhar verification successful',
      isAadharVerified: true,
    };
  }

  private async validateAadharNumber(aadharNumber: string): Promise<boolean> {
    // Basic validation: 12 digits
    if (!/^\d{12}$/.test(aadharNumber)) {
      return false;
    }

    // TODO: Add actual Aadhar verification logic here
    // This could involve calling an external API or service
    return true;
  }

  async getUsers(role?: UserRole, page = 1, limit = 10) {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile');

    if (role) {
      queryBuilder.where('user.role = :role', { role });
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      users,
      total,
      page,
      limit,
    };
  }

  async getUser(id: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Update user fields
    if (updateUserDto.firstName) user.firstName = updateUserDto.firstName;
    if (updateUserDto.lastName) user.lastName = updateUserDto.lastName;
    if (updateUserDto.role) user.role = updateUserDto.role;
    if (updateUserDto.subscriptionType) user.subscriptionType = updateUserDto.subscriptionType;

    // Update profile if provided
    if (
      Object.keys(updateUserDto).some((key) =>
        ['address', 'city', 'state', 'country', 'postalCode', 'profileImage'].includes(key),
      )
    ) {
      let profile = user.profile;
      if (!profile) {
        profile = this.profileRepository.create({ user });
      }
      Object.assign(profile, updateUserDto);
      await this.profileRepository.save(profile);
      user.profile = profile;
    }

    // Check if profile is complete
    const isProfileComplete = this.isProfileComplete(user.profile);
    if (isProfileComplete !== user.isProfileComplete) {
      user.isProfileComplete = isProfileComplete;
    }

    await this.userRepository.save(user);

    return {
      message: 'User updated successfully',
      user,
    };
  }
}
