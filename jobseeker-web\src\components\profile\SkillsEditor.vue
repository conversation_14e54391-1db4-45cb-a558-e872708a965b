<template>
  <Dialog 
    :visible="localVisible" 
    @update:visible="localVisible = $event"
    header="Edit Skills"
    :modal="true"
    :closable="true"
    class="skills-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <div class="skills-form">
      <div class="form-group">
        <label for="skillInput">Add Skills</label>
        <div class="skill-input-wrapper">
          <AutoComplete
            id="skillInput"
            v-model="newSkill"
            :suggestions="filteredSkills"
            @complete="searchSkills"
            @keydown.enter="addSkill"
            placeholder="Type a skill and press Enter"
            class="skill-input"
          />
          <Button 
            @click="addSkill"
            icon="pi pi-plus"
            :disabled="!newSkill?.trim()"
            class="add-skill-btn"
          />
        </div>
        <small class="form-help">Type skills and press Enter or click + to add them</small>
      </div>

      <div class="current-skills">
        <h4>Your Skills</h4>
        <div class="skills-list" v-if="formData.skills.length > 0">
          <div 
            v-for="(skill, index) in formData.skills" 
            :key="index"
            class="skill-item"
          >
            <span class="skill-name">{{ skill }}</span>
            <Button 
              @click="removeSkill(index)"
              icon="pi pi-times"
              text
              size="small"
              class="remove-skill-btn"
            />
          </div>
        </div>
        <div v-else class="no-skills">
          <p>No skills added yet. Start typing to add your first skill!</p>
        </div>
      </div>

      <div class="suggested-skills">
        <h4>Suggested Skills</h4>
        <div class="suggestions-grid">
          <Button 
            v-for="suggestion in suggestedSkills" 
            :key="suggestion"
            @click="addSuggestedSkill(suggestion)"
            :label="suggestion"
            outlined
            size="small"
            class="suggestion-btn"
            :disabled="formData.skills.includes(suggestion)"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          label="Save Skills"
          :loading="isSaving"
          class="save-btn"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import AutoComplete from 'primevue/autocomplete'
import Button from 'primevue/button'
import alertManager from '@/utils/alertManager'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  profileData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)
const newSkill = ref('')
const filteredSkills = ref([])

const formData = ref({
  skills: []
})

// Computed property for handling the visible prop
const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

// Common skills database
const allSkills = [
  // Construction
  'Construction', 'Safety Protocols', 'Blueprint Reading', 'Heavy Machinery', 'Team Leadership',
  'Concrete Work', 'Electrical Wiring', 'Plumbing', 'Carpentry', 'Welding', 'Roofing',
  'Drywall Installation', 'Painting', 'OSHA Compliance', 'Power Tools', 'Measuring & Layout',
  
  // Manufacturing
  'Quality Control', 'Assembly Line Work', 'Machine Operation', 'Inventory Management',
  'Lean Manufacturing', 'Six Sigma', 'Forklift Operation', 'CNC Operation', 'Packaging',
  'Production Planning', 'Equipment Maintenance',
  
  // Logistics
  'Warehouse Operations', 'Shipping & Receiving', 'Order Fulfillment', 'Supply Chain',
  'Transportation', 'Loading & Unloading', 'Package Handling', 'Route Planning',
  'Customer Service', 'Documentation',
  
  // Maintenance
  'Preventive Maintenance', 'Troubleshooting', 'Electrical Systems', 'HVAC Systems',
  'Mechanical Repair', 'Equipment Calibration', 'Hand Tools', 'Facility Management',
  
  // Security
  'Surveillance', 'Access Control', 'Emergency Response', 'Report Writing',
  'Conflict Resolution', 'First Aid/CPR', 'Security Systems', 'Patrol Procedures',
  'Communication Skills', 'Attention to Detail', 'Physical Fitness',
  
  // General
  'Time Management', 'Problem Solving', 'Teamwork', 'Leadership', 'Communication',
  'Computer Skills', 'Microsoft Office', 'Data Entry', 'Customer Service',
  'Multi-tasking', 'Adaptability', 'Reliability', 'Work Ethic'
]

const suggestedSkills = computed(() => {
  return allSkills
    .filter(skill => !formData.value.skills.includes(skill))
    .slice(0, 12)
})

// Watch for prop changes
watch(() => props.profileData, (newData) => {
  if (newData) {
    formData.value = {
      skills: [...(newData.skills || [])]
    }
  }
}, { immediate: true, deep: true })

const searchSkills = (event) => {
  const query = event.query.toLowerCase()
  filteredSkills.value = allSkills.filter(skill => 
    skill.toLowerCase().includes(query) && 
    !formData.value.skills.includes(skill)
  )
}

const addSkill = () => {
  const skill = newSkill.value?.trim()
  if (skill && !formData.value.skills.includes(skill)) {
    formData.value.skills.push(skill)
    newSkill.value = ''
  }
}

const addSuggestedSkill = (skill) => {
  if (!formData.value.skills.includes(skill)) {
    formData.value.skills.push(skill)
  }
}

const removeSkill = (index) => {
  formData.value.skills.splice(index, 1)
}

const handleSubmit = async () => {
  isSaving.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('save', formData.value)
    emit('update:visible', false)
    
  } catch (error) {
    alertManager.showError('Error', 'Failed to update skills. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.skills-dialog {
  border-radius: 12px !important;
}

.skills-form {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.skill-input-wrapper {
  display: flex;
  gap: 0.5rem;
}

.skill-input {
  flex: 1;
}

.add-skill-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.form-help {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.current-skills h4,
.suggested-skills h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.skill-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.skill-name {
  font-weight: 500;
}

.remove-skill-btn {
  color: white !important;
  width: 20px !important;
  height: 20px !important;
}

.no-skills {
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
  color: var(--text-color-secondary);
  font-style: italic;
}

.suggestions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.suggestion-btn {
  border-radius: 20px !important;
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .dialog-footer {
    flex-direction: column;
  }
}
</style>