import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { ImageMetadata } from '../interfaces/image-processing.interface';

@Entity('profiles')
export class ProfileEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ nullable: true })
  address: string;

  @Column({ type: 'date', nullable: true })
  dateOfBirth?: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  location?: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  state: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  postalCode: string;

  @Column({ nullable: true })
  profileImage: string;

  @Column({ nullable: true })
  profileImageThumbnail: string;

  @Column('jsonb', { nullable: true })
  imageMetadata: ImageMetadata;

  @Column({ nullable: true })
  jobTitle: string;

  @Column({ type: 'text', nullable: true, default: 'en' })
  language: string;

  @Column({ nullable: true })
  experienceYears: number;

  @Column({ nullable: true })
  experienceMonths: number;

  @Column({ nullable: true })
  industry: string;

  @Column({ nullable: true })
  desiredSalary: number;

  @Column({ type: 'text', nullable: true })
  bio: string;

  @Column('simple-array', { nullable: true })
  skills: string[];

  @Column({ default: 0 })
  profileViews: number;

  @Column({ default: true })
  isPublic: boolean;

  @Column({ default: true })
  emailNotifications: boolean;

  @Column({ nullable: true })
  avatarColor: string;

  @Column('jsonb', { nullable: true })
  workExperience: {
    id: number;
    jobTitle: string;
    company: string;
    startDate: string;
    endDate: string | null;
    description: string;
  }[];

  @Column('jsonb', { nullable: true })
  education: {
    id: number;
    degree: string;
    school: string;
    startYear: string;
    endYear: string | null;
    fieldOfStudy: string;
  }[];

  @Column({ default: false })
  isComplete: boolean;

  @Column({ default: true })
  isProfileVisible: boolean;

  @Column({ default: true })
  emailNotification: boolean;

  @Column({ default: true })
  phoneNotification: boolean;

  @Column({ nullable: true })
  updatedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(() => UserEntity, (user) => user.profile)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
