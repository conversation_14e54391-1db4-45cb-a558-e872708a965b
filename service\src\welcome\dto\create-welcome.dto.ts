import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsBoolean } from 'class-validator';

export class DescriptionDto {
  @ApiProperty({
    description: 'Description text',
    example: 'Your trusted platform for professional services',
  })
  @IsString()
  description: string;
}

export class AudienceMessageDto {
  @ApiProperty({
    description: 'Message for the audience',
    example: 'Join our community of professionals',
  })
  @IsString()
  message: string;
}

export class TestimonialDto {
  @ApiProperty({
    description: 'Name of the testimonial author',
    example: '<PERSON>',
  })
  @IsString()
  author: string;

  @ApiProperty({
    description: 'Content of the testimonial',
    example: 'Great platform for finding professional services!',
  })
  @IsString()
  content: string;
}

export class CreateWelcomeDto {
  @ApiProperty({
    description: 'Company name',
    example: 'Blu-Collar',
  })
  @IsString()
  cmp_name: string;

  @ApiProperty({
    description: 'Path to the company logo',
    required: false,
    example: '/assets/images/logo.png',
  })
  @IsOptional()
  @IsString()
  logo_path?: string;

  @ApiProperty({
    description: 'Whether to show signup button',
    default: false,
    example: true,
  })
  @IsBoolean()
  show_signup: boolean;

  @ApiProperty({
    description: 'Whether to show login button',
    default: false,
    example: true,
  })
  @IsBoolean()
  show_login: boolean;

  @ApiProperty({
    description: 'Welcome popup message',
    required: false,
    example: 'Welcome to Blu-Collar',
  })
  @IsOptional()
  @IsString()
  welcome_pop_msg?: string;

  @ApiProperty({
    description: 'Base URL of the application',
    required: false,
    example: 'http://localhost:3000',
  })
  @IsOptional()
  @IsUrl()
  base_url?: string;

  @ApiProperty({
    description: 'Notification service URL',
    required: false,
    example: 'http://localhost:3000/notifications',
  })
  @IsOptional()
  @IsUrl()
  notification_url?: string;

  @ApiProperty({
    description: 'User service URL',
    required: false,
    example: 'http://localhost:3000/users',
  })
  @IsOptional()
  @IsUrl()
  user_url?: string;

  @ApiProperty({
    description: 'The welcome message content',
    example: 'Welcome to Blu-Collar - Your trusted platform for professional services',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'URL of the welcome image',
    required: false,
    example: '/assets/images/welcome-banner.png',
  })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiProperty({
    description: 'List of descriptions',
    type: [DescriptionDto],
    required: false,
  })
  @IsOptional()
  descriptions?: DescriptionDto[];

  @ApiProperty({
    description: 'List of audience messages',
    type: [AudienceMessageDto],
    required: false,
  })
  @IsOptional()
  audience_messages?: AudienceMessageDto[];

  @ApiProperty({
    description: 'List of testimonials',
    type: [TestimonialDto],
    required: false,
  })
  @IsOptional()
  testimonials?: TestimonialDto[];
}
