import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { UserEntity } from '../../users/entities/user.entity';

export enum FeedbackType {
  SUGGESTION = 'SUGGESTION',
  FEEDBACK = 'FEEDBACK',
  BUG_REPORT = 'BUG_REPORT',
  FEATURE_REQUEST = 'FEATURE_REQUEST',
}

export enum FeedbackStatus {
  PENDING = 'PENDING',
  IN_REVIEW = 'IN_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  IMPLEMENTED = 'IMPLEMENTED',
}

@Entity('feedbacks')
export class FeedbackEntity {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Unique identifier for the feedback' })
  id: string;

  @Column({
    type: 'enum',
    enum: FeedbackType,
    default: FeedbackType.FEEDBACK,
  })
  @ApiProperty({
    description: 'Type of feedback',
    enum: FeedbackType,
    example: FeedbackType.SUGGESTION,
  })
  type: FeedbackType;

  @Column({ type: 'text' })
  @ApiProperty({
    description: 'Title or subject of the feedback',
    example: 'Add dark mode support',
  })
  title: string;

  @Column({ type: 'text' })
  @ApiProperty({
    description: 'Detailed description of the feedback',
    example:
      'It would be great to have a dark mode option for better visibility in low-light conditions.',
  })
  description: string;

  @Column({
    type: 'enum',
    enum: FeedbackStatus,
    default: FeedbackStatus.PENDING,
  })
  @ApiProperty({
    description: 'Current status of the feedback',
    enum: FeedbackStatus,
    example: FeedbackStatus.PENDING,
  })
  status: FeedbackStatus;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Admin response or notes about the feedback',
    example: 'This feature is planned for the next release.',
    required: false,
  })
  adminResponse: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'ID of the admin who last updated the feedback' })
  updatedBy: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'ID of the user who submitted the feedback' })
  userId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @Column({ type: 'json', nullable: true })
  @ApiProperty({
    description: 'Additional metadata or attachments',
    example: { screenshots: ['url1', 'url2'] },
    required: false,
  })
  metadata: Record<string, any>;

  @CreateDateColumn()
  @ApiProperty({ description: 'Timestamp when the feedback was created' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Timestamp when the feedback was last updated' })
  updatedAt: Date;
}
