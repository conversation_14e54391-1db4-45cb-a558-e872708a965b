<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="./src/assets/favicon_io/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Basic Meta Tags -->
  <title>Job Dalal - Find Jobs & Hire Skilled Workers in India</title>
  <meta name="description"
    content="JobDalal is India's trusted platform to find local jobs and hire skilled blue-collar workers. Quick sign-up for job seekers and employers.">

  <meta name="keywords"
    content="JobDalal, job portal India, blue collar jobs, hire workers, find work, local jobs, skilled labor, job seekers, employers, work in India, hire locally">
  <meta name="author" content="JobDalal Team">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://www.jobdalal.com" />

  <!-- Open Graph (OG) Meta Tags for Social Sharing -->
  <meta property="og:title" content="JobDalal - Connecting Job Seekers & Employers in India">
  <meta property="og:description"
    content="Explore thousands of jobs or hire trusted workers on JobDalal. Built for India's workforce.">
  <meta property="og:image" content="https://www.jobdalal.com/assets/jd-logo.png"> <!-- Replace with your image URL -->
  <meta property="og:url" content="https://www.jobdalal.com">
  <meta property="og:type" content="website">

  <style>
    #app-loader {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 96vh;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: #333;
      text-align: center;
      overflow: hidden;
    }

    .loader-message {
      margin-top: 1rem;
      font-size: 1.2rem;
      font-weight: 500;
    }

    .loader-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #cbd5e0;
      border-top: 4px solid #0ea5e9;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  </style>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-LK50T133FR"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-LK50T133FR');
  </script>
</head>

<body>
  <div id="app">
    <div id="app-loader">
      <div class="loader-spinner"></div>
      <p class="loader-message">Please wait, we're setting up your application...</p>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>