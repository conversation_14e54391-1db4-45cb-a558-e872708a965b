<script setup>
import { defineProps, defineEmits, ref, watch } from 'vue';
import Drawer from 'primevue/drawer';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import MultiSelect from 'primevue/multiselect';
import Select from 'primevue/select';
import DatePicker from 'primevue/datepicker';

const props = defineProps({
  visible: Boolean,
  filters: Object,
  severityOptions: Array,
  screenOptions: Array,
});

const emit = defineEmits(['close', 'apply-filters', 'reset-filters']);

const localFilters = ref({});

watch(() => props.filters, (newFilters) => {
    localFilters.value = JSON.parse(JSON.stringify(newFilters));
}, { immediate: true, deep: true });

const resolvedOptions = [
  { label: 'All', value: null },
  { label: 'Resolved', value: true },
  { label: 'Unresolved', value: false }
];

function apply() {
  emit('apply-filters', localFilters.value);
  emit('close');
}

function reset() {
  emit('reset-filters');
  emit('close');
}
</script>

<template>
  <Drawer
    :visible="visible"
    @update:visible="emit('close')"
    position="right"
    class="filter-drawer"
    style="width: 350px"
  >
    <template #header>
      <h3 class="font-bold">Filters</h3>
    </template>

    <div class="p-4">
      <div class="form-group mb-4">
        <label for="search-term" class="font-semibold">Search Term</label>
        <InputText id="search-term" v-model="localFilters.searchTerm" class="w-full mt-1" placeholder="Search by message, screen..." />
      </div>
      <div class="form-group mb-4">
        <label for="severity-filter" class="font-semibold">Severity</label>
        <MultiSelect id="severity-filter" v-model="localFilters.severities" :options="severityOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select severities" />
      </div>
       <div class="form-group mb-4">
        <label for="screen-filter" class="font-semibold">Screen</label>
        <MultiSelect id="screen-filter" v-model="localFilters.screens" :options="screenOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select screens" />
      </div>
       <div class="form-group mb-4">
        <label for="status-filter" class="font-semibold">Status</label>
        <Select id="status-filter" v-model="localFilters.resolvedStatus" :options="resolvedOptions" optionLabel="label" optionValue="value" class="w-full mt-1" placeholder="Select status" />
      </div>
      <div class="form-group mb-4">
        <label for="date-range" class="font-semibold">Date Range</label>
        <DatePicker id="date-range" v-model="localFilters.dateRange" selectionMode="range" :manualInput="false" class="w-full mt-1" />
      </div>
    </div>
    
    <template #footer>
        <Button label="Reset" severity="secondary" @click="reset" />
        <Button label="Apply Filters" @click="apply" class="ml-2" />
    </template>
  </Drawer>
</template> 