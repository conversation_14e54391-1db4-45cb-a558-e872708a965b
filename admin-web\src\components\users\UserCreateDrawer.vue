<script setup>
import { ref, computed  } from 'vue';
import Drawer from 'primevue/drawer';
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
// import Password from 'primevue/password';
import Checkbox from 'primevue/checkbox';
import Button from 'primevue/button';
import { usersService } from '@/api/services/usersService';
import { useGlobalToast } from '@/composables/useGlobalToast';



defineProps({
  visible: Boolean
});

const emit = defineEmits(['close', 'success']);

const loading = ref(false);
const error = ref('');
const { showToast } = useGlobalToast();

const form = ref({
  email: '',
  password: '',
  firstName: '',
  middleName: '',
  lastName: '',
  role: 'job_seeker',
  subscriptionType: 'default',
  isEmailVerified: false,
  isPhoneVerified: false,
  isAadharVerified: false,
  isProfileComplete: false,
  isBlocked: false
});

const roleOptions = [
  { label: 'Super Admin', id: 'super_admin' },
  { label: 'Admin', id: 'admin' },
  { label: 'Job Seeker', id: 'job_seeker' },
  { label: 'Employer', id: 'employer' },
  { label: 'Agent', id: 'agent' }
];

const subscriptionOptions = [
  { label: 'Default', id: 'default' },
  { label: 'Pro', id: 'pro' },
  { label: 'Premium', id: 'premium' }
];

const fieldErrors = ref({});

function resetForm() {
    form.value = {
        email: '',
        password: '',
        firstName: '',
        middleName: '',
        lastName: '',
        role: 'job_seeker',
        subscriptionType: 'default',
        isEmailVerified: false,
        isPhoneVerified: false,
        isAadharVerified: false,
        isProfileComplete: false,
        isBlocked: false
    };
    error.value = '';
}
const isEmailValid = () => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email);

const validateForm = () => {
  const errors = {};
  if (!form.value.email.trim()) {
    errors.email = 'This field is required';
  } else if (!isEmailValid()) {
    errors.email = 'Invalid email address';
  }
  if (!form.value.firstName.trim()) {
    errors.firstName = 'This field is required';
  }
  if (!form.value.lastName.trim()) {
    errors.lastName = 'This field is required';
  }
  fieldErrors.value = errors;
  return Object.keys(errors).length === 0;
};

async function handleSubmit() {
  error.value = '';
  if (!validateForm()) {
    return;
  }
  loading.value = true;
  try {
    const response = await usersService.createUser(form.value);

    if (response.success) {
      resetForm();
      emit('success');
      showToast({ severity: 'success', summary: 'Success', detail: 'User created successfully.', life: 3000 });
    }
    else{
      showToast({ severity: 'error', summary: response.errorCode, detail: response.errorMessage || 'Failed to create user.', life: 3000 });
    }
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Failed to create user.';
    showToast({ severity: 'error', summary: 'Error', detail: error.value, life: 3000 });
  } finally {
    loading.value = false;
  }
}

function handleClose() {
    resetForm();
    emit('close');
}


</script>
<template>
  <Drawer :visible="visible" position="right" class="user-drawer" :style="{ width: '500px' }" @update:visible="handleClose">
    <template #header>
      <h3>Create User</h3>
    </template>
    <div class="drawer-content">
      <form @submit.prevent="handleSubmit">
        <div class="form-group">
          <label>Email *</label>
          <InputText v-model="form.email" type="email" class="w-full" required :inputProps="{ autocomplete: 'off' }" />
          <div v-if="fieldErrors.email" class="field-error">{{ fieldErrors.email }}</div>
        </div>
        <!-- <div class="form-group">
          <label>Password *</label>
          <Password v-model="form.password" class="w-full" required toggle-mask />
          <div v-if="fieldErrors.password" class="field-error">{{ fieldErrors.password }}</div>
        </div> -->
        <div class="form-group">
          <label>First Name *</label>
          <InputText v-model="form.firstName" class="w-full" required />
          <div v-if="fieldErrors.firstName" class="field-error">{{ fieldErrors.firstName }}</div>
        </div>
        <div class="form-group">
          <label>Middle Name</label>
          <InputText v-model="form.middleName" class="w-full" />
        </div>
        <div class="form-group">
          <label>Last Name *</label>
          <InputText v-model="form.lastName" class="w-full" required />
          <div v-if="fieldErrors.lastName" class="field-error">{{ fieldErrors.lastName }}</div>
        </div>
        <div class="form-group">
          <label>Role</label>
          <Select v-model="form.role" :options="roleOptions" option-label="label" option-value="id" class="w-full" />
        </div>
        <div class="form-group">
          <label>Subscription Type</label>
          <Select v-model="form.subscriptionType" :options="subscriptionOptions" option-label="label" option-value="id" class="w-full" />
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isEmailVerified" :binary="true" /> Email Verified
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isPhoneVerified" :binary="true" /> Phone Verified
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isAadharVerified" :binary="true" /> Aadhar Verified
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isProfileComplete" :binary="true" /> Profile Complete
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isBlocked" :binary="true" /> Block User
        </div>
        <div v-if="error" class="error-message p-error">{{ error }}</div>
        <div class="drawer-footer">
          <Button label="Cancel" severity="secondary" @click="handleClose" :disabled="loading" />
          <Button label="Create" type="submit" :loading="loading" />
        </div>
      </form>
    </div>
  </Drawer>
</template>
<style scoped>
.form-group { margin-bottom: 1rem; }
.drawer-footer { display: flex; justify-content: flex-end; gap: 1rem; margin-top: 1.5rem; }
.error-message { margin-top: 1rem; color: red; }
.field-error { color: #e24c4b; font-size: 0.92em; margin-top: 0.25rem; }
</style> 