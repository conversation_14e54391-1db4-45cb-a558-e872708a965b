<template>
  <div class="landing-page">
    <!-- Header -->
    <header class="landing-header">
      <div class="header-container">
        <div class="logo">
          <img src="../assets/jd-logo.png" alt="Logo" width="160" loading="lazy" />
        </div>
        <!-- <nav class="header-nav">
          <Button 
            @click="navigateToJobSeeker"
            :label="t('navigation.findJobs')"
            outlined
            class="nav-button"
          />
          <Button 
            @click="navigateToEmployer"
            :label="t('navigation.postJobs')"
            class="nav-button primary"
          />
        </nav> -->
        <ThemeToggle />
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ t('landing.heroTitle') }}
            <span class="highlight">{{ t('landing.heroHighlight') }}</span>
            {{ t('landing.heroTitleEnd') }}
          </h1>
          <p class="hero-subtitle">
            {{ t('landing.heroSubtitle') }}
          </p>
          <div class="hero-actions">
            <Button 
              @click="navigateToJobSeeker"
              :label="t('landing.startJobSearch')"
              size="large"
              class="hero-button primary"
              icon="pi pi-search"
            />
            <Button 
              @click="navigateToEmployer"
              :label="t('navigation.postJobs')"
              size="large"
              icon="pi pi-search"
              class="hero-button primary"
            />
            <!-- <Button 
              @click="scrollToFeatures"
              :label="t('landing.learnMore')"
              size="large"
              outlined
              class="hero-button secondary"
              icon="pi pi-info-circle"
            /> -->
          </div>
          <div class="hero-stats">
            <div class="stat">
              <span class="stat-number">10,000+</span>
              <span class="stat-label">{{ t('landing.activeJobs') }}</span>
            </div>
            <div class="stat">
              <span class="stat-number">5,000+</span>
              <span class="stat-label">{{ t('landing.companies') }}</span>
            </div>
            <div class="stat">
              <span class="stat-number">50,000+</span>
              <span class="stat-label">{{ t('landing.jobSeekers') }}</span>
            </div>
          </div>
        </div>
        <!-- <div class="hero-image">
          <img 
            src="../assets/banner-bg.jpg" 
            alt="Construction workers"
            class="hero-img"
          />
        </div> --> 

        <div class="hero-image hero-image-carousel">
          <div class="carousel">
            <img src="../assets/banner-bg.jpg" alt="Image 1" class="carousel-img" loading="lazy" />
            <img src="../assets/banner-bg1.jpg" alt="Image 2" class="carousel-img" loading="lazy" />
            <img src="../assets/banner-bg2.jpg" alt="Image 3" class="carousel-img" loading="lazy" />
            <img src="../assets/banner-bg3.jpg" alt="Image 4" class="carousel-img" loading="lazy" />
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" ref="featuresSection">
      <div class="features-container">
        <h2 class="section-title">{{ t('landing.whyChoose') }}</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="pi pi-search"></i>
            </div>
            <h3>{{ t('landing.easyJobSearch') }}</h3>
            <p>{{ t('landing.easyJobSearchDesc') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="pi pi-users"></i>
            </div>
            <h3>{{ t('landing.directEmployerContact') }}</h3>
            <p>{{ t('landing.directEmployerContactDesc') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="pi pi-mobile"></i>
            </div>
            <h3>{{ t('landing.mobileFriendly') }}</h3>
            <p>{{ t('landing.mobileFriendlyDesc') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <i class="pi pi-shield"></i>
            </div>
            <h3>{{ t('landing.verifiedEmployers') }}</h3>
            <p>{{ t('landing.verifiedEmployersDesc') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Job Categories Section -->
    <section class="categories-section">
      <div class="categories-container">
        <h2 class="section-title">{{ t('landing.popularJobCategories') }}</h2>
        <div class="categories-grid">
          <div class="category-card" @click="searchByCategory('Restaurants')">
            <div class="category-icon">
              <i class="pi pi-shop"></i>
            </div>
            <h3>{{ t('landing.restaurants') }}</h3>
            <p>2,500+ {{ t('navigation.jobs') }}</p>
          </div>
          <div class="category-card" @click="searchByCategory('hotels')">
            <div class="category-icon">
              <i class="pi pi-building"></i>
            </div>
            <h3>{{ t('landing.hotels') }}</h3>
            <p>1,800+ {{ t('navigation.jobs') }}</p>
          </div>
          <div class="category-card" @click="searchByCategory('mart')">
            <div class="category-icon">
              <i class="pi pi-warehouse"></i>
            </div>
            <h3>{{ t('landing.mart') }}</h3>
            <p>1,200+ {{ t('navigation.jobs') }}</p>
          </div>
          <div class="category-card" @click="searchByCategory('maintenance')">
            <div class="category-icon">
              <i class="pi pi-wrench"></i>
            </div>
            <h3>{{ t('landing.maintenance') }}</h3>
            <p>900+ {{ t('navigation.jobs') }}</p>
          </div>
          <div class="category-card" @click="searchByCategory('security')">
            <div class="category-icon">
              <i class="pi pi-shield"></i>
            </div>
            <h3>{{ t('landing.security') }}</h3>
            <p>750+ {{ t('navigation.jobs') }}</p>
          </div>
          <div class="category-card" @click="searchByCategory('hospitality')">
            <div class="category-icon">
              <i class="pi pi-building"></i>
            </div>
            <h3>{{ t('landing.hospitality') }}</h3>
            <p>650+ {{ t('navigation.jobs') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="cta-container">
        <h2>{{ t('landing.readyToFind') }}</h2>
        <p>{{ t('landing.readyToFindDesc') }}</p>
        <div class="cta-buttons">
          <Button 
            @click="navigateToJobSeeker"
            :label="t('landing.getStartedNow')"
            size="large"
            class="cta-button"
            icon="pi pi-arrow-right"
          />
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="landing-footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-section">
            <div class="footer-logo">
              <img src="../assets/jd-logo.png" alt="Logo" width="200" loading="lazy" />
            </div>
            <p>{{ t('landing.connectingWorkers') }}</p>
          </div>
          <!-- commented footer links -->
          <!-- <div class="footer-section">
            <h4>{{ t('landing.forJobSeekers') }}</h4>
            <ul>
              <li><a href="#" @click="navigateToJobSeeker">{{ t('landing.browseJobs') }}</a></li>
              <li><a href="#" @click="navigateToJobSeeker">{{ t('landing.createProfile') }}</a></li>
              <li><a href="#">{{ t('landing.careerResources') }}</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>{{ t('landing.forEmployers') }}</h4>
            <ul>
              <li><a href="#" @click="navigateToEmployer">{{ t('landing.postJob') }}</a></li>
              <li><a href="#" @click="navigateToEmployer">{{ t('landing.findCandidates') }}</a></li>
              <li><a href="#">{{ t('landing.pricing') }}</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>{{ t('landing.support') }}</h4>
            <ul>
              <li><a href="#">{{ t('landing.helpCenter') }}</a></li>
              <li><a href="#">{{ t('landing.contactUs') }}</a></li>
              <li><a href="#">{{ t('landing.privacyPolicy') }}</a></li>
            </ul>
          </div> -->
        </div>
        <div class="footer-bottom">
          <p>{{ t('landing.footerPrefix') }} {{ year }} {{ t('landing.footerSuffix') }}</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import ThemeToggle from '@/components/ThemeToggle.vue'

const router = useRouter()
const { t } = useI18n()
const featuresSection = ref(null)

const navigateToJobSeeker = () => {
  router.push('/jobseeker/dashboard')
}

const navigateToEmployer = () => {
  window.location.href = '/employer'
}

const scrollToFeatures = () => {
  featuresSection.value?.scrollIntoView({ behavior: 'smooth' })
}

const searchByCategory = (category) => {
  router.push(`/jobseeker/jobs?category=${category}`)
}

const year = new Date().getFullYear();
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background: var(--surface-0);
}

/* Header */
.landing-header {
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-container {
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
}

.logo-icon {
  font-size: 2rem;
  color: var(--primary-color);
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-button {
  height: 40px !important;
}

.nav-button.primary {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--surface-0) 0%, var(--surface-50) 100%);
}

.hero-container {
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.highlight {
  color: var(--primary-color);
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-color-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.hero-button {
  height: 56px !important;
  padding: 0 2rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
}

.hero-button.primary {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.hero-stats {
  display: flex;
  gap: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.hero-image {
  display: flex;
  justify-content: center;
}

.hero-img {
  width: 100%;
  max-width: 600px;
  height: 400px;
  object-fit: cover;
  border-radius: 16px;
}

/* Hero Image Carousel */
.hero-image-carousel {
  width: 100%;
  overflow: hidden;
  position: relative;
  max-width: 600px;
  height: 400px;
  object-fit: cover;
  border-radius: 16px;
}

.carousel {
  display: flex;
  animation: slide 15s infinite;
  width: 300%;
}

.carousel-img {
  width: 100%;
  flex-shrink: 0;
  transition: transform 1s ease;
}

@keyframes slide {
  0% {
    transform: translateX(0%);
  }
  20% {
    transform: translateX(0%);
  }
  25% {
    transform: translateX(-100%);
  }
  45% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(-200%);
  }
  70% {
    transform: translateX(-200%);
  }
  75% {
    transform: translateX(-300%);
  }
  95% {
    transform: translateX(-300%);
  }
  100% {
    transform: translateX(0%);
  }
}

/* Features Section */
.features-section {
  padding: 5rem 0;
  background: var(--surface-card);
}

.features-container {
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  text-align: center;
  padding: 2rem;
  border-radius: 16px;
  background: var(--surface-0);
  border: 1px solid var(--surface-border);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: var(--primary-color);
  font-size: 2rem;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.feature-card p {
  color: var(--text-color-secondary);
  line-height: 1.6;
}

/* Categories Section */
.categories-section {
  padding: 5rem 0;
  background: var(--surface-0);
}

.categories-container {
  margin: 0 auto;
  padding: 0 2rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.category-card {
  text-align: center;
  padding: 2rem 1rem;
  border-radius: 12px;
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-2px);
  border-color: var(--primary-color);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.category-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.category-card p {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
  padding: 4rem 0;
  background: var(--primary-color);
  color: white;
  text-align: center;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.cta-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.cta-section p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  color: white;
}

.cta-button {
  background: white !important;
  color: var(--primary-color) !important;
  border-color: white !important;
  height: 56px !important;
  padding: 0 2rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
}

/* Footer */
.landing-footer {
  background: var(--surface-300);
  color: var(--surface-0);
  padding: 3rem 0 1rem;
}

.footer-container {
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.footer-logo i {
  font-size: 2rem;
  color: var(--primary-color);
}

.footer-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--surface-0);
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: var(--surface-300);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  border-top: 1px solid var(--surface-700);
  padding-top: 1rem;
  text-align: center;
  color: var(--surface-400);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
  }
  
  .header-nav {
    gap: 0.5rem;
  }
  
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .features-grid,
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .cta-section h2 {
    font-size: 2rem;
  }
  
  .cta-section p {
    font-size: 1.1rem;
  }
}
</style>