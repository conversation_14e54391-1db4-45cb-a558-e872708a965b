import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IndustryEntity } from '../entities/industry.entity';
import { CreateIndustryDto } from '../dto/create-industry.dto';
import { UpdateIndustryDto } from '../dto/update-industry.dto';

@Injectable()
export class IndustryService {
  constructor(
    @InjectRepository(IndustryEntity)
    private readonly industryRepository: Repository<IndustryEntity>,
  ) {}

  async create(createIndustryDto: CreateIndustryDto): Promise<IndustryEntity> {
    const existingIndustry = await this.industryRepository
      .createQueryBuilder('industry')
      .where('LOWER(industry.name) = LOWER(:name)', { name: createIndustryDto.name })
      .getOne();

    if (existingIndustry) {
      throw new BadRequestException(
        `Industry with name "${createIndustryDto.name}" already exists`,
      );
    }

    const industry = this.industryRepository.create(createIndustryDto);
    return this.industryRepository.save(industry);
  }

  async findAll(): Promise<IndustryEntity[]> {
    return this.industryRepository
      .createQueryBuilder('industry')
      .leftJoinAndSelect(
        'industry.subIndustries',
        'subIndustry',
        'subIndustry.isActive = :isActive',
        { isActive: true },
      )
      .where('industry.isActive = :isActive', { isActive: true })
      .orderBy('industry.name', 'ASC')
      .getMany();
  }

  async findOne(id: string): Promise<IndustryEntity> {
    const industry = await this.industryRepository
      .createQueryBuilder('industry')
      .leftJoinAndSelect(
        'industry.subIndustries',
        'subIndustry',
        'subIndustry.isActive = :isActive',
        { isActive: true },
      )
      .where('industry.id = :id', { id })
      .andWhere('industry.isActive = :isActive', { isActive: true })
      .getOne();

    if (!industry) {
      throw new NotFoundException('Industry not found');
    }

    return industry;
  }

  async update(id: string, updateIndustryDto: UpdateIndustryDto): Promise<IndustryEntity> {
    const industry = await this.findOne(id);

    if (updateIndustryDto.name && updateIndustryDto.name !== industry.name) {
      const existingIndustry = await this.industryRepository
        .createQueryBuilder('industry')
        .where('LOWER(industry.name) = LOWER(:name)', { name: updateIndustryDto.name })
        .getOne();

      if (existingIndustry) {
        throw new BadRequestException(
          `Industry with name "${updateIndustryDto.name}" already exists`,
        );
      }
    }

    Object.assign(industry, updateIndustryDto);
    return this.industryRepository.save(industry);
  }

  async remove(id: string): Promise<void> {
    const industry = await this.findOne(id);

    // Check if there are any active jobs associated with this industry
    const hasActiveJobs = await this.industryRepository
      .createQueryBuilder('industry')
      .leftJoin('industry.jobs', 'job')
      .where('industry.id = :id', { id })
      .andWhere('job.isActive = :isActive', { isActive: true })
      .getCount();

    if (hasActiveJobs > 0) {
      throw new BadRequestException(
        'Cannot delete industry because it has active jobs associated with it',
      );
    }

    // Check if there are any active sub-industries
    const hasActiveSubIndustries = await this.industryRepository
      .createQueryBuilder('industry')
      .leftJoin('industry.subIndustries', 'subIndustry')
      .where('industry.id = :id', { id })
      .andWhere('subIndustry.isActive = :isActive', { isActive: true })
      .getCount();

    if (hasActiveSubIndustries > 0) {
      throw new BadRequestException(
        'Cannot delete industry because it has active sub-industries. Please delete or deactivate all sub-industries first.',
      );
    }

    // Perform soft delete
    industry.isActive = false;
    await this.industryRepository.save(industry);
  }
}
