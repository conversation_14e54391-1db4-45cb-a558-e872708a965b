<script setup>
import { defineProps, defineEmits } from 'vue'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Tag from 'primevue/tag'

defineProps({
  industries: Array,
  industryStats: Object
})
const emit = defineEmits(['add-industry', 'edit-industry', 'toggle-industry', 'add-category'])

function formatDate(date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric', month: 'short', day: 'numeric'
  }).format(new Date(date))
}
</script>
<template>
  <div class="tab-content">
    <div class="section-header">
      <h3>Industries</h3>
      <Button v-tooltip="'Add Industry'" icon="pi pi-plus" size="small" @click="$emit('add-industry')" raised rounded />
    </div>
    <div class="table-container">
      <DataTable :value="industries" class="industries-table" :paginator="true" :rows="10" stripedRows :rowHover="true" :scrollable="true" scroll-height="300px">
        <Column field="name" header="Industry Name" sortable>
          <template #body="slotProps">
            <div class="industry-name">
              <i class="pi pi-building industry-icon"></i>
              <span>{{ slotProps.data.name }}</span>
            </div>
          </template>
        </Column>
        <Column field="details" header="Details" sortable>
          <template #body="slotProps">
            <span class="industry-details">{{ slotProps.data.description }}</span>
          </template>
        </Column>
        <Column field="categories" header="Categories" sortable>
          <template #body="slotProps">
            <div class="categories-count">
              <span class="count-badge">{{ slotProps.data.subIndustries.length }}</span>
              <span>categories</span>
            </div>
          </template>
        </Column>
        <Column field="createdAt" header="Created" sortable>
          <template #body="slotProps">
            <span>{{ formatDate(slotProps.data.createdAt) }}</span>
          </template>
        </Column>
        <Column field="isActive" header="Is Active" sortable>
          <template #body="slotProps">
            <Tag :value="slotProps.data.isActive ? 'Active' : 'Inactive'" :severity="slotProps.data.isActive ? 'success' : 'danger'" />
          </template>
        </Column>
        <Column header="Actions">
          <template #body="slotProps">
            <div class="action-buttons">
              <Button icon="pi pi-plus" text rounded size="small" @click="$emit('add-category', null, slotProps.data.id)" v-tooltip.left="'Add Category'" />
              <Button icon="pi pi-pencil" text rounded size="small" @click="$emit('edit-industry', slotProps.data)" v-tooltip.left="'Edit Industry'" />
              <Button icon="pi pi-eye-slash" text rounded size="small" :class="{active: slotProps.data.isActive}" @click="$emit('toggle-industry', slotProps.data)" v-tooltip.left="slotProps.data.isActive ? 'De-activate Industry' : 'Activate Industry'" />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template>
<style scoped>
</style> 