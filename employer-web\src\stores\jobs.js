import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api'
import { formatApiError } from '@/utils/apiHelpers'

export const useJobsStore = defineStore('jobs', () => {
  // State
  const jobs = ref([])
  const currentJob = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const pagination = ref({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  
  // Filters state
  const filters = ref({
    search: '',
    department: null,
    status: [],
    jobType: [],
    experienceLevel: null,
    salaryMin: null,
    salaryMax: null,
    dateRange: null,
    isRemote: null,
    location: '',
    sortBy: 'postedDate',
    sortOrder: 'desc'
  })
  
  // Reference data
  const departments = ref([])
  const jobStatuses = ref([])
  const jobTypes = ref([])
  const experienceLevels = ref([])
  const stats = ref({})

  // Getters
  const totalJobs = computed(() => pagination.value.total)
  const hasJobs = computed(() => jobs.value.length > 0)
  const activeJobs = computed(() => jobs.value.filter(job => job.status === 'active'))
  const draftJobs = computed(() => jobs.value.filter(job => job.status === 'draft'))
  const closedJobs = computed(() => jobs.value.filter(job => job.status === 'closed'))

  // Actions
  const fetchJobs = async (params = {}) => {
    isLoading.value = true
    error.value = null

    try {
      const queryParams = {
        ...filters.value,
        ...params,
        page: pagination.value.page,
        limit: pagination.value.limit
      }
      
      const response = await api.jobs.getMyJobs(queryParams)
      
      if (response.success) {
        // If it's a new search (page 1), replace jobs; otherwise append for pagination
        if (queryParams.page === 1) {
          jobs.value = response.data.items
        } else {
          jobs.value = [...jobs.value, ...response.data.items]
        }
        
        pagination.value = response.data
      } else {
        throw new Error(response.message || 'Failed to fetch jobs')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Fetch jobs error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchJobById = async (id) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.jobs.getById(id)
      
      if (response.success) {
        currentJob.value = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Job not found')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Fetch job error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const searchJobs = async (query, searchFilters = {}) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.jobs.search(query, searchFilters)
      
      if (response.success) {
        jobs.value = response.data
        pagination.value = response.pagination || pagination.value
      } else {
        throw new Error(response.message || 'Search failed')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Search jobs error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const getFeaturedJobs = async (limit = 10) => {
    try {
      const response = await api.jobs.getFeatured(limit)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || 'Failed to fetch featured jobs')
      }
    } catch (err) {
      console.error('Fetch featured jobs error:', err)
      return []
    }
  }

  const getRecentJobs = async (limit = 10) => {
    try {
      const response = await api.jobs.getRecent(limit)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || 'Failed to fetch recent jobs')
      }
    } catch (err) {
      console.error('Fetch recent jobs error:', err)
      return []
    }
  }

  const createJob = async (jobData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.jobs.create(jobData)
      
      if (response.success) {
        jobs.value.unshift(response.data)
        return response.data
      } else {
        throw new Error(response.message || 'Failed to create job')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Create job error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateJob = async (id, jobData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.jobs.update(id, jobData)
      
      if (response.success) {
        const index = jobs.value.findIndex(job => job.id === id)
        if (index !== -1) {
          jobs.value[index] = response.data
        }
        
        if (currentJob.value && currentJob.value.id === id) {
          currentJob.value = response.data
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to update job')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Update job error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteJob = async (id) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.jobs.update(id, {
        isDeleted: true
      })
      
      if (response.success) {
        jobs.value = jobs.value.filter(job => job.id !== id)
        
        if (currentJob.value && currentJob.value.id === id) {
          currentJob.value = null
        }
        
        return true
      } else {
        throw new Error(response.message || 'Failed to delete job')
      }
    } catch (err) {
      error.value = formatApiError(err)
      console.error('Delete job error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchJobStats = async () => {
    try {
      const response = await api.jobs.getStats()
      
      if (response.success) {
        stats.value = response.data
      }
    } catch (err) {
      console.error('Fetch job stats error:', err)
    }
  }

  const fetchReferenceData = async () => {
    return;
    try {
      // const [deptResponse, statusResponse, typeResponse, expResponse] = await Promise.all([
      //   api.reference.getDepartments(),
      //   api.reference.getJobStatuses(),
      //   api.reference.getJobTypes(),
      //   api.reference.getExperienceLevels()
      // ])
      
      // if (deptResponse.success) departments.value = deptResponse.data
      // if (statusResponse.success) jobStatuses.value = statusResponse.data
      // if (typeResponse.success) jobTypes.value = typeResponse.data
      // if (expResponse.success) experienceLevels.value = expResponse.data
    } catch (err) {
      console.error('Fetch reference data error:', err)
    }
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      department: null,
      status: [],
      jobType: [],
      experienceLevel: null,
      salaryMin: null,
      salaryMax: null,
      dateRange: null,
      isRemote: null,
      location: '',
      sortBy: 'postedDate',
      sortOrder: 'desc'
    }
  }

  const setPage = (page) => {
    pagination.value.page = page
  }

  const setPageSize = (limit) => {
    pagination.value.limit = limit
    pagination.value.page = 1 // Reset to first page
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentJob = () => {
    currentJob.value = null
  }

  // Initialize reference data
  fetchReferenceData()

  return {
    // State
    jobs,
    currentJob,
    isLoading,
    error,
    pagination,
    filters,
    departments,
    jobStatuses,
    jobTypes,
    experienceLevels,
    stats,
    // Getters
    totalJobs,
    hasJobs,
    activeJobs,
    draftJobs,
    closedJobs,
    // Actions
    fetchJobs,
    fetchJobById,
    searchJobs,
    getFeaturedJobs,
    getRecentJobs,
    createJob,
    updateJob,
    deleteJob,
    fetchJobStats,
    fetchReferenceData,
    setFilters,
    clearFilters,
    setPage,
    setPageSize,
    clearError,
    clearCurrentJob
  }
})