<template>
  <Drawer 
    :visible="modelValue" 
    position="right" 
    :modal="true"
    :dismissable="true"
    :showCloseIcon="true"
    :style="{ width: '400px' }" 
    @update:visible="onDrawerVisible" 
    @hide="onClose"
  >
    <template #header>
      <span>Filter Industries</span>
    </template>
    <form @submit.prevent="onApply">
      <div class="form-group">
        <label class="filter-label">
          <i class="pi pi-search"></i>
          Search
        </label>
        <InputText v-model="filters.searchTerm" placeholder="Search industries..." class="w-full" />
      </div>
      <div class="form-group">
        <label class="filter-label">
          <i class="pi pi-building"></i>
          Filter Categories by Industry
        </label>
        <Select
          v-model="filters.selectedIndustryFilter"
          :options="industryOptions"
          option-label="label"
          option-value="value"
          placeholder="Select industry"
          :showClear="true"
          class="w-full"
        />
      </div>
      <div class="filter-stats">
        <h4>Statistics</h4>
        <div class="stats-list">
          <div class="stat-item">
            <span class="stat-label">Total Industries</span>
            <span class="stat-value">{{ stats.totalIndustries }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Total Categories</span>
            <span class="stat-value">{{ stats.totalCategories }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Avg Categories</span>
            <span class="stat-value">{{ stats.avgCategoriesPerIndustry }}</span>
          </div>
        </div>
      </div>
      <div class="form-actions">
        <Button type="submit" label="Apply" icon="pi pi-filter" class="mr-2" />
        <Button type="button" label="Reset" icon="pi pi-refresh" severity="secondary" @click="onReset" />
        <Button type="button" label="Close" icon="pi pi-times" severity="secondary" @click="onClose" />
      </div>
    </form>
  </Drawer>
</template>

<script setup>
import Drawer from 'primevue/drawer'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'
import Button from 'primevue/button'
import { reactive, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  initialFilters: {
    type: Object,
    default: () => ({})
  },
  industryOptions: {
    type: Array,
    default: () => []
  },
  stats: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:modelValue', 'apply', 'reset', 'close'])

const filters = reactive({
  searchTerm: '',
  selectedIndustryFilter: null
})

watch(() => props.initialFilters, (val) => {
  if (val) Object.assign(filters, val)
}, { immediate: true })

const onApply = () => {
  emit('apply', { ...filters })
  emit('update:modelValue', false)
}
const onReset = () => {
  filters.searchTerm = ''
  filters.selectedIndustryFilter = null
  emit('reset')
}
const onClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
const onDrawerVisible = (val) => {
  emit('update:modelValue', val)
}
</script>

<style scoped>
.form-group {
  margin-bottom: 1rem;
}
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}
.filter-stats {
  margin: 1.5rem 0;
}
.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.stat-item {
  display: flex;
  justify-content: space-between;
}
</style> 