<script setup>
import { ref, reactive, computed } from 'vue'
import ConfirmDialog from 'primevue/confirmdialog'
import SpeedDial from 'primevue/speeddial';
import Button from 'primevue/button';
import { useConfirm } from 'primevue/useconfirm'
import { useAuthStore } from '@/stores/auth'
import JobsTable from './JobsTable.vue'
import JobsFilterDrawer from './JobsFilterDrawer.vue'
import CreateJobDrawer from './CreateJobDrawer.vue'

// Enums based on the DTO
const JobType = {
  FULL_TIME: 'FULL_TIME',
  PART_TIME: 'PART_TIME',
  CONTRACT: 'CONTRACT',
  DAILY_WAGE: 'DAILY_WAGE',
  SENIOR_LEVEL: 'SENIOR_LEVEL',
  EXPERT: 'EXPERT'
}

const PaymentType = {
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  BI_WEEKLY: 'BI_WEEKLY',
  MONTHLY: 'MONTHLY'
}

const JobUrgency = {
  FLEXIBLE: 'FLEXIBLE',
  URGENT: 'URGENT',
  IMMEDIATE: 'IMMEDIATE'
}

const ExperienceLevel = {
  FRESHER: 'FRESHER',
  ENTRY_LEVEL: 'ENTRY_LEVEL',
  MID_LEVEL: 'MID_LEVEL',
  SENIOR_LEVEL: 'SENIOR_LEVEL',
  EXPERT: 'EXPERT'
}

const ContactDisplayType = {
  NONE: 'NONE',
  PHONE: 'PHONE',
  EMAIL: 'EMAIL',
  BOTH: 'BOTH'
}

// Sample industries data (would come from the industries store/API)
const industries = ref([
  { id: '1', name: 'Technology', label: 'Technology' },
  { id: '2', name: 'Healthcare', label: 'Healthcare' },
  { id: '3', name: 'Finance', label: 'Finance' },
  { id: '4', name: 'Construction', label: 'Construction' },
  { id: '5', name: 'Manufacturing', label: 'Manufacturing' }
])

const confirm = useConfirm()
// const applicationsStore = useApplicationsStore()


// Dialog states
const showJobDialog = ref(false)
const isEditMode = ref(false)
const selectedJob = ref(null)

// Filter states
const showFilters = ref(false)
const filtersCollapsed = ref(false)

// Form data
const jobForm = reactive({
  title: '',
  description: '',
  industryId: '',
  salary: null,
  jobType: JobType.FULL_TIME,
  paymentType: PaymentType.MONTHLY,
  location: '',
  urgency: JobUrgency.FLEXIBLE,
  experienceLevel: ExperienceLevel.FRESHER,
  benefits: [],
  requirements: [],
  responsibilities: [],
  skills: [],
  thumbnail: '',
  images: [],
  showContact: true,
  contactDisplayType: ContactDisplayType.NONE,
  contactPhone: '',
  contactEmail: '',
  contactPerson: '',
  vacancies: 1,
  workingHours: '',
  accommodation: '',
  transportation: '',
  foodProvided: '',
  safetyEquipment: '',
  trainingProvided: ''
})

// Search and filters
const searchTerm = ref('')
const selectedIndustryFilter = ref(null)
const selectedJobTypeFilter = ref(null)
const selectedUrgencyFilter = ref(null)
const selectedExperienceLevelFilter = ref(null)
const selectedPaymentTypeFilter = ref(null)
const selectedJobStatusFilter = ref(null)
const minSalaryFilter = ref(null)
const maxSalaryFilter = ref(null)

// Options for dropdowns
const jobTypeOptions = Object.values(JobType).map(type => ({
  label: type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

const paymentTypeOptions = Object.values(PaymentType).map(type => ({
  label: type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

const urgencyOptions = Object.values(JobUrgency).map(urgency => ({
  label: urgency.toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: urgency
}))

const experienceLevelOptions = Object.values(ExperienceLevel).map(level => ({
  label: level.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: level
}))


const jobStatusOptions = computed(() => {
  const enums = useAuthStore().enums;
  if (enums && enums.JobStatus) {
    return Object.values(enums.JobStatus).map((status) => ({
      label: status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
      value: status
    }));
  }
  return [];
});

const industryFilterOptions = computed(() =>
  industries.value.map(industry => ({
    label: industry.name,
    value: industry.id
  }))
)


const resetJobForm = () => {
  Object.assign(jobForm, {
    title: '',
    description: '',
    industryId: '',
    salary: null,
    jobType: JobType.FULL_TIME,
    paymentType: PaymentType.MONTHLY,
    location: '',
    urgency: JobUrgency.FLEXIBLE,
    experienceLevel: ExperienceLevel.FRESHER,
    benefits: [],
    requirements: [],
    responsibilities: [],
    skills: [],
    thumbnail: '',
    images: [],
    showContact: true,
    contactDisplayType: ContactDisplayType.NONE,
    contactPhone: '',
    contactEmail: '',
    contactPerson: '',
    vacancies: 1,
    workingHours: '',
    accommodation: '',
    transportation: '',
    foodProvided: '',
    safetyEquipment: '',
    trainingProvided: ''
  })
  selectedJob.value = null
}


const deleteJob = (job) => {
  confirm.require({
    message: `Are you sure you want to delete the job "${job.title}"?`,
    header: 'Delete Job',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: 'Delete',
    accept: () => {
      const index = jobs.value.findIndex(j => j.id === job.id)
      if (index !== -1) {
        jobs.value.splice(index, 1)
      }
    }
  })
}

const toggleJobStatus = (job) => {
  const index = jobs.value.findIndex(j => j.id === job.id)
  if (index !== -1) {
    jobs.value[index].isActive = !jobs.value[index].isActive
  }
}



const clearFilters = () => {
  selectedIndustryFilter.value = null;
  selectedJobTypeFilter.value = null;
  selectedUrgencyFilter.value = null;
  selectedExperienceLevelFilter.value = null;
  selectedPaymentTypeFilter.value = null;
  selectedJobStatusFilter.value = null;
  minSalaryFilter.value = null;
  maxSalaryFilter.value = null;
};

const showCreateJobDrawer = ref(false)
const editingJob = ref(null)

const handleEditJob = (job) => {
  editingJob.value = job
  showCreateJobDrawer.value = true
}

const handleCreateJobClick = () => {
  editingJob.value = null
  showCreateJobDrawer.value = true
}

// const showFilters = ref(false)
const showFiltersClick = () => {
  showFilters.value = !showFilters.value
  console.log('Show filter drawer')
}

const loadJobs = () => {
  debugger
  jobsTableRef?.value?.loadJobs()
}

const jobsTableRef = ref(null)
</script>

<template>
  <div class="jobs-view flex-1">

    <div class="jobs-container flex-1">
      <JobsTable ref="jobsTableRef" @edit-job="handleEditJob" />
    </div>

    <SpeedDial direction="up" :style="{ position: 'absolute', right: '1.5rem', bottom: '1.2rem' }"
      :tooltipOptions="{ position: 'left' }" @click='handleCreateJobClick'></SpeedDial>

    <SpeedDial direction="up" :style="{ position: 'absolute', right: '5.5rem', bottom: '1.2rem' }"
      :tooltipOptions="{ position: 'left' }" @click="showFiltersClick">
    
      <template #button="{ toggleCallback }">
        <Button icon="pi pi-filter" @click="toggleCallback" rounded raised />
    </template>
    </SpeedDial>

    <ConfirmDialog />
    <JobsFilterDrawer :visible="showFilters" @update:visible="showFilters = $event"
      :industries="industryFilterOptions" :job-types="jobTypeOptions" :urgencies="urgencyOptions"
      :experience-levels="experienceLevelOptions" :payment-types="paymentTypeOptions"
      :job-statuses="jobStatusOptions" v-model:selectedIndustry="selectedIndustryFilter"
      v-model:selectedJobType="selectedJobTypeFilter" v-model:selectedUrgency="selectedUrgencyFilter"
      v-model:selectedExperienceLevel="selectedExperienceLevelFilter"
      v-model:selectedPaymentType="selectedPaymentTypeFilter"
      v-model:selectedJobStatus="selectedJobStatusFilter" v-model:minSalary="minSalaryFilter"
      v-model:maxSalary="maxSalaryFilter" @clear-filters="clearFilters" />
    <CreateJobDrawer :visible="showCreateJobDrawer" :job="editingJob" @update:visible="showCreateJobDrawer = $event" 
    @refresh:table="loadJobs"/>
  </div>
</template>

<style scoped>
.jobs-view {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Page Header */
.page-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--p-surface-border);
  background: var(--p-surface-card);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-toggle {
  display: none;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* Content Layout */
.content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Left Filters Sidebar (Desktop) */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease;
}

.filters-sidebar.collapsed {
  width: 60px;
}

.filters-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.collapse-btn {
  flex-shrink: 0;
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.stat-value.success {
  color: var(--p-green-600);
}

.stat-value.warning {
  color: var(--p-yellow-600);
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Table Container */
.table-container {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.jobs-table {
  flex: 1;
  height: 100%;
}

.job-title-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.job-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.job-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.job-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.job-title {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.job-industry {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.location-text,
.salary-text,
.vacancies-count,
.posted-date {
  font-size: 0.875rem;
  color: var(--p-text-color);
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

/* Mobile Filter Drawer */
.filter-drawer {
  z-index: 1000;
}

.drawer-header {
  padding: 1rem 0;
}

.drawer-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.drawer-content {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Dialog Styling */
.job-dialog {
  max-height: 90vh;
}

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 1rem 0;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-section h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  border-bottom: 1px solid var(--p-surface-border);
  padding-bottom: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Responsive Design */
.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }

  .filters-sidebar.collapsed {
    width: 60px;
  }
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: flex;
  }

  .filter-toggle {
    display: flex;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: stretch;
  }

  .table-container {
    padding: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .job-title-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar,
.drawer-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track,
.drawer-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb,
.drawer-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover,
.drawer-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track,
:global(.dark) .drawer-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb,
:global(.dark) .drawer-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover,
:global(.dark) .drawer-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>