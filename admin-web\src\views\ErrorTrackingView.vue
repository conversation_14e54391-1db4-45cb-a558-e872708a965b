<script setup>
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import ErrorTrackingViewComponent from '@/components/error-tracking/ErrorTrackingView.vue'

const appStore = useAppStore();

onMounted(() => {
  appStore.pageInfo = {
    title: 'Error Tracking',
    subTitle: 'Monitor and manage application errors and exceptions'
  }
})
</script>

<template>
  <ErrorTrackingViewComponent />
</template>