// API Endpoints Configuration
export const ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh-token',
    PROFILE: '/auth/profile'
  },

  INDUSTRIES: {
    GET: '/industries',
    POST: '/industries',
    UPDATE: (id) => `/industries/${id}`,
    POST_CATEGORY: '/sub-industries',
    UPDATE_CATEGORY: (id) => `/sub-industries/${id}`,
  },

  // Jobs
  JOBS: {
    LIST: (page, limit = 100) => `/jobs?page=${page}&limit=${limit}`,
    GET_ORG_USER: '/users/getorguser',
    DETAILS: (id) => `/jobs/${id}`,
    CREATE: '/jobs',
    UPDATE: (id) => `/jobs/${id}`,
    UPDATE_STATUS: (id) => `/jobs/${id}`,
    INDUSTRIES: '/industries',
    STATS: '/jobs/stats',
    SEARCH: '/jobs/search',
    FEATURED: '/jobs/featured',
    RECENT: '/jobs/recent',
    ENUMS: '/jobs/job-enums' // New job enums endpoint
  },

  USERS: {
    GET_ALL: (page, limit, params = {}) => {
      let url = `/users?page=${page}&limit=${limit}`;
      if (params.role) url += `&role=${params.role}`;
      if (params.isBlocked !== undefined) url += `&isBlocked=${params.isBlocked}`;
      if (params.searchTerm) url += `&searchTerm=${encodeURIComponent(params.searchTerm)}`;
      return url;
    },
    CREATE: '/users',
    UPDATE: (id) => `/users/${id}`,
    DELETE: (id) => `/users/${id}`,
    BLOCK: (id) => `/users/${id}/block`,
    UNBLOCK: (id) => `/users/${id}/unblock`,
    UPDATE_PROFILE: (userId) => `/profile/${userId}`,
  },
  // Applications
  APPLICATIONS: {
    LIST: '/applications',
    DETAILS: (id) => `/applications/${id}`,
    CREATE: '/applications',
    UPDATE: (id) => `/applications/${id}`,
    DELETE: (id) => `/applications/${id}`,
    BY_JOB: (jobId) => `/jobs/${jobId}/applications`,
    BY_USER: '/applications/user',
    WITHDRAW: (id) => `/applications/${id}/withdraw`
  },

  // Profile (for employer-web, not admin)
  PROFILE: {
    GET_PROFILE: '/profile',
    UPDATE_PROFILE: '/profile',
    UPLOAD_AVATAR: '/profile/avatar',
    DELETE_AVATAR: '/profile/avatar',
    PRIVACY_SETTINGS: '/profile/privacy'
  },

  // Reference Data
  REFERENCE: {
    DEPARTMENTS: '/reference/departments',
    JOB_STATUSES: '/reference/job-statuses',
    JOB_TYPES: '/reference/job-types',
    EXPERIENCE_LEVELS: '/reference/experience-levels',
    SKILLS: '/reference/skills',
    LOCATIONS: '/reference/locations'
  },

  // Dashboard - Job Seeker specific endpoint
  DASHBOARD: {
    STATS: '/jobs/employer/dashboard', // Main dashboard endpoint
    ADMIN_STATS: '/dashboard/admin/dashboard', // Admin dashboard endpoint
    RECENT_ACTIVITY: '/dashboard/activity',
    RECOMMENDATIONS: '/dashboard/recommendations'
  },

  // Companies
  COMPANIES: {
    LIST: '/companies',
    DETAILS: (id) => `/companies/${id}`,
    JOBS: (id) => `/companies/${id}/jobs`,
    GET_BY_USER_ID: (userId) => `/companies/by-user/${userId}`,
    UPDATE_BY_USER_ID: (userId) => `/companies/by-user/${userId}`,
  },

  // Notifications
  NOTIFICATIONS: {
    LIST: '/notifications',
    MARK_READ: (id) => `/notifications/${id}/read`,
    MARK_ALL_READ: '/notifications/mark-all-read',
    SETTINGS: '/notifications/settings'
  },

  // File Upload
  UPLOAD: {
    RESUME: '/upload/resume',
    AVATAR: '/upload/avatar',
    DOCUMENTS: '/upload/documents'
  },

  // Error Logs
  ERROR_LOGS: {
    LIST: (page = 1, limit = 20, params = {}) => {
      let url = `/error-logs?page=${page}&limit=${limit}`;
      if (params.severity) url += `&severity=${params.severity}`;
      if (params.status) url += `&status=${params.status}`;
      if (params.startDate) url += `&startDate=${encodeURIComponent(params.startDate)}`;
      if (params.endDate) url += `&endDate=${encodeURIComponent(params.endDate)}`;
      return url;
    },
    CREATE: '/error-logs',
    DETAIL: (id) => `/error-logs/${id}`,
    UPDATE: (id) => `/error-logs/${id}`,
    DELETE: (id) => `/error-logs/${id}`,
  },

  // Feedback
  FEEDBACK: {
    LIST: (page = 1, limit = 20, params = {}) => {
      let url = `/feedback?page=${page}&limit=${limit}`;
      if (params.type) url += `&type=${params.type}`;
      if (params.status) url += `&status=${params.status}`;
      if (params.userId) url += `&userId=${params.userId}`;
      if (params.startDate) url += `&startDate=${encodeURIComponent(params.startDate)}`;
      if (params.endDate) url += `&endDate=${encodeURIComponent(params.endDate)}`;
      if (params.searchTerm) url += `&searchTerm=${encodeURIComponent(params.searchTerm)}`;
      return url;
    },
    CREATE: '/feedback',
    DETAIL: (id) => `/feedback/${id}`,
    UPDATE: (id) => `/feedback/${id}`,
    DELETE: (id) => `/feedback/${id}`,
  },
}