import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminActivityEntity } from '../entities/admin-activity.entity';
import { AdminActionType } from '../enums/admin.enum';

@Injectable()
export class AdminActivityService {
  constructor(
    @InjectRepository(AdminActivityEntity)
    private adminActivityRepository: Repository<AdminActivityEntity>,
  ) {}

  async logActivity(
    adminId: string,
    actionType: AdminActionType,
    targetId: string,
    details?: Record<string, any>,
  ): Promise<AdminActivityEntity> {
    const activity = this.adminActivityRepository.create({
      adminId,
      actionType,
      targetId,
      details,
    });
    return this.adminActivityRepository.save(activity);
  }

  async getActivities(
    adminId?: string,
    targetId?: string,
    actionType?: AdminActionType,
  ): Promise<AdminActivityEntity[]> {
    const query = this.adminActivityRepository
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.admin', 'admin')
      .leftJoinAndSelect('activity.user', 'user');

    if (adminId) {
      query.andWhere('activity.adminId = :adminId', { adminId });
    }

    if (targetId) {
      query.andWhere('activity.targetId = :targetId', { targetId });
    }

    if (actionType) {
      query.andWhere('activity.actionType = :actionType', { actionType });
    }

    return query.orderBy('activity.createdAt', 'DESC').getMany();
  }

  async getActivityStats(startDate: Date, endDate: Date) {
    const queryBuilder = this.adminActivityRepository.createQueryBuilder('activity');

    // Get total activities
    const totalActivities = await queryBuilder
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getCount();

    // Get activities by type
    const activitiesByType = await queryBuilder
      .select('activity.actionType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('activity.actionType')
      .getRawMany();

    // Get activities by admin
    const activitiesByAdmin = await queryBuilder
      .select("activity.actionDetails->>'adminId'", 'adminId')
      .addSelect('COUNT(*)', 'count')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy("activity.actionDetails->>'adminId'")
      .getRawMany();

    // Get activities by target user
    const activitiesByTargetUser = await queryBuilder
      .select('targetUser.email', 'userEmail')
      .addSelect('COUNT(*)', 'count')
      .leftJoin('activity.targetUser', 'targetUser')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('targetUser.email')
      .getRawMany();

    return {
      totalActivities,
      activitiesByType,
      activitiesByAdmin,
      activitiesByTargetUser,
    };
  }

  async exportActivities(
    startDate: Date,
    endDate: Date,
    actionType?: AdminActionType,
    adminId?: string,
    targetUserId?: string,
  ) {
    const queryBuilder = this.adminActivityRepository
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.admin', 'admin')
      .leftJoinAndSelect('activity.targetUser', 'targetUser')
      .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate });

    if (actionType) {
      queryBuilder.andWhere('activity.actionType = :actionType', { actionType });
    }

    if (adminId) {
      queryBuilder.andWhere("activity.actionDetails->>'adminId' = :adminId", { adminId });
    }

    if (targetUserId) {
      queryBuilder.andWhere('activity.targetUserId = :targetUserId', { targetUserId });
    }

    return queryBuilder.orderBy('activity.createdAt', 'DESC').getMany();
  }

  async getRecentActivities(limit: number = 10) {
    return this.adminActivityRepository.find({
      order: { createdAt: 'DESC' },
      take: limit,
      relations: ['user'],
    });
  }

  async getActivityByType(actionType: AdminActionType, days: number = 7) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return this.adminActivityRepository
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.user', 'user')
      .where('activity.actionType = :actionType', { actionType })
      .andWhere('activity.createdAt >= :startDate', { startDate })
      .orderBy('activity.createdAt', 'DESC')
      .getMany();
  }
}
