import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const referenceService = {
  // Get departments
  async getDepartments() {
    try {
      const response = await httpClient.get(ENDPOINTS.REFERENCE.DEPARTMENTS)
      return {
        success: true,
        data: response.departments || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch departments')
    }
  },

  // Get job statuses
  async getJobStatuses() {
    try {
      const response = await httpClient.get(ENDPOINTS.REFERENCE.JOB_STATUSES)
      return {
        success: true,
        data: response.statuses || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch job statuses')
    }
  },

  // Get job types
  async getJobTypes() {
    try {
      const response = await httpClient.get(ENDPOINTS.REFERENCE.JOB_TYPES)
      return {
        success: true,
        data: response.types || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch job types')
    }
  },

  // Get experience levels
  async getExperienceLevels() {
    try {
      const response = await httpClient.get(ENDPOINTS.REFERENCE.EXPERIENCE_LEVELS)
      return {
        success: true,
        data: response.levels || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch experience levels')
    }
  },

  // Get skills
  async getSkills() {
    try {
      const response = await httpClient.get(ENDPOINTS.REFERENCE.SKILLS)
      return {
        success: true,
        data: response.skills || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch skills')
    }
  },

  // Get locations
  async getLocations() {
    try {
      const response = await httpClient.get(ENDPOINTS.REFERENCE.LOCATIONS)
      return {
        success: true,
        data: response.locations || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch locations')
    }
  }
}