import { Controller, Get, Post, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AdminService } from '../services/admin.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';

@ApiTags('Admin')
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@ApiBearerAuth()
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('stats')
  @ApiOperation({ summary: 'Get admin statistics' })
  @ApiResponse({ status: 200, description: 'Admin statistics' })
  async getStats() {
    const endDate = new Date();
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    return this.adminService.getActivityStats(startDate, endDate);
  }

  @Get('activities')
  @ApiOperation({ summary: 'Get admin activities' })
  @ApiResponse({ status: 200, description: 'List of admin activities' })
  async getActivities(@Request() req) {
    return this.adminService.getAdminActivities(req.user.id);
  }

  @Get('users')
  @ApiOperation({ summary: 'Get users list' })
  @ApiResponse({ status: 200, description: 'List of users' })
  async getUsers(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('role') role?: UserRole,
    @Query('isBlocked') isBlocked?: string,
    @Query('searchTerm') searchTerm?: string,
  ) {
    return this.adminService.getUsers(
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined,
      role,
      isBlocked ? isBlocked === 'true' : undefined,
      searchTerm,
    );
  }

  @Get('users/:id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User details' })
  async getUserById(@Param('id') id: string) {
    return this.adminService.getUserById(id);
  }

  @Post('users/:id/block')
  @ApiOperation({ summary: 'Block a user' })
  @ApiResponse({ status: 200, description: 'User blocked successfully' })
  async blockUser(@Param('id') id: string, @Body('adminId') adminId: string) {
    return this.adminService.toggleUserBlock(adminId, id, 'block');
  }

  @Post('users/:id/unblock')
  @ApiOperation({ summary: 'Unblock a user' })
  @ApiResponse({ status: 200, description: 'User unblocked successfully' })
  async unblockUser(@Param('id') id: string, @Body('adminId') adminId: string) {
    return this.adminService.toggleUserBlock(adminId, id, 'unblock');
  }
}
