import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const uploadService = {
  // Upload resume
  async uploadResume(file) {
    try {
      const formData = new FormData()
      formData.append('resume', file)
      
      const response = await httpClient.post(ENDPOINTS.UPLOAD.RESUME, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return {
        success: true,
        data: response.file || response.data,
        message: response.message || 'Resume uploaded successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to upload resume')
    }
  },

  // Upload avatar
  async uploadAvatar(file) {
    try {
      const formData = new FormData()
      formData.append('avatar', file)
      
      const response = await httpClient.post(ENDPOINTS.UPLOAD.AVATAR, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return {
        success: true,
        data: response.file || response.data,
        message: response.message || 'Avatar uploaded successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to upload avatar')
    }
  },

  // Upload documents
  async uploadDocuments(files) {
    try {
      const formData = new FormData()
      
      if (Array.isArray(files)) {
        files.forEach((file, index) => {
          formData.append(`documents[${index}]`, file)
        })
      } else {
        formData.append('documents', files)
      }
      
      const response = await httpClient.post(ENDPOINTS.UPLOAD.DOCUMENTS, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return {
        success: true,
        data: response.files || response.data,
        message: response.message || 'Documents uploaded successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to upload documents')
    }
  }
}