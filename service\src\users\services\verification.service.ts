import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VerificationEntity } from '../entities/verification.entity';
import { UserEntity } from '../entities/user.entity';
import { AdminActionType } from '../enums/admin.enum';

@Injectable()
export class VerificationService {
  constructor(
    @InjectRepository(VerificationEntity)
    private readonly verificationRepository: Repository<VerificationEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  async create(
    userId: string,
    verificationData: Partial<VerificationEntity>,
  ): Promise<VerificationEntity> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const existingVerification = await this.verificationRepository.findOne({
      where: { user: { id: userId } },
    });
    if (existingVerification) {
      throw new BadRequestException('Verification details already exist for this user');
    }

    const verification = this.verificationRepository.create({
      ...verificationData,
      user: { id: userId },
    });

    return this.verificationRepository.save(verification);
  }

  async update(
    userId: string,
    verificationData: Partial<VerificationEntity>,
  ): Promise<VerificationEntity> {
    const verification = await this.verificationRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!verification) {
      throw new NotFoundException('Verification details not found');
    }

    Object.assign(verification, verificationData);
    return this.verificationRepository.save(verification);
  }

  async verifyAadhar(userId: string, adminId: string): Promise<VerificationEntity> {
    const verification = await this.verificationRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!verification) {
      throw new NotFoundException('Verification details not found');
    }

    verification.isAadharVerified = true;
    verification.verifiedAt = new Date();
    verification.verifiedBy = adminId;

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (user) {
      user.isAadharVerified = true;
      await this.userRepository.save(user);
    }

    return this.verificationRepository.save(verification);
  }

  async verifyPan(userId: string, adminId: string): Promise<VerificationEntity> {
    const verification = await this.verificationRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!verification) {
      throw new NotFoundException('Verification details not found');
    }

    verification.isPanVerified = true;
    verification.verifiedAt = new Date();
    verification.verifiedBy = adminId;

    return this.verificationRepository.save(verification);
  }

  async getByUserId(userId: string): Promise<VerificationEntity> {
    const verification = await this.verificationRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!verification) {
      throw new NotFoundException('Verification details not found');
    }
    return verification;
  }

  async searchVerifications(
    page: number = 1,
    limit: number = 10,
    searchTerm?: string,
    isVerified?: boolean,
  ) {
    const queryBuilder = this.verificationRepository
      .createQueryBuilder('verification')
      .leftJoinAndSelect('verification.user', 'user');

    if (searchTerm) {
      queryBuilder.andWhere(
        '(verification.aadharNumber ILIKE :searchTerm OR verification.panNumber ILIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` },
      );
    }

    if (isVerified !== undefined) {
      queryBuilder.andWhere(
        '(verification.isAadharVerified = :isVerified OR verification.isPanVerified = :isVerified)',
        { isVerified },
      );
    }

    const [verifications, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      verifications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
