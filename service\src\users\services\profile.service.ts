import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProfileEntity } from '../entities/profile.entity';
import { UserEntity } from '../entities/user.entity';
import { FileUploadService } from '../../common/services/file-upload.service';
import { ImageMetadata } from '../interfaces/image-processing.interface';

interface ProcessedImages {
  thumbnail: string;
  medium: string;
  large: string;
  metadata: ImageMetadata;
}

@Injectable()
export class ProfileService {
  constructor(
    @InjectRepository(ProfileEntity)
    private readonly profileRepository: Repository<ProfileEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    private readonly fileUploadService: FileUploadService,
  ) {}

  async getByUserId(userId: string): Promise<ProfileEntity> {
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });
    if (!profile) {
      throw new NotFoundException('Profile not found');
    }
    return profile;
  }

  async update(
    userId: string,
    profileData: Partial<ProfileEntity>,
    updatedBy?: string,
  ): Promise<ProfileEntity> {
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    Object.assign(profile, profileData);
    if (updatedBy) {
      profile.updatedBy = updatedBy;
    }
    return this.profileRepository.save(profile);
  }

  async markAsComplete(userId: string): Promise<ProfileEntity> {
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (user) {
      user.isProfileComplete = true;
      await this.userRepository.save(user);
    }

    return profile;
  }

  async uploadProfileImage(
    userId: string,
    file: Express.Multer.File,
    options: { crop?: boolean } = {},
  ): Promise<ProfileEntity> {
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    // Delete old images if they exist
    if (profile.profileImage) {
      await this.fileUploadService.deleteFile(profile.profileImage);
    }
    if (profile.profileImageThumbnail) {
      await this.fileUploadService.deleteFile(profile.profileImageThumbnail);
    }

    // Upload new images with different sizes
    const processedImages = await this.fileUploadService.uploadFile(
      file,
      'profile-images',
      options,
    );

    const images: ProcessedImages = {
      thumbnail: processedImages.find((img) => img.path.includes('_thumbnail'))?.url || '',
      medium: processedImages.find((img) => img.path.includes('_medium'))?.url || '',
      large: processedImages.find((img) => img.path.includes('_large'))?.url || '',
      metadata: processedImages[0].metadata,
    };

    // Update profile with new image URLs
    profile.profileImage = images.large;
    profile.profileImageThumbnail = images.thumbnail;
    profile.imageMetadata = images.metadata;

    return this.profileRepository.save(profile);
  }

  async deleteProfileImage(userId: string): Promise<ProfileEntity> {
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    if (profile.profileImage) {
      await this.fileUploadService.deleteFile(profile.profileImage);
      if (profile.profileImageThumbnail) {
        await this.fileUploadService.deleteFile(profile.profileImageThumbnail);
      }
      profile.profileImage = null;
      profile.profileImageThumbnail = null;
      profile.imageMetadata = null;
      return this.profileRepository.save(profile);
    }

    return profile;
  }

  async debugProfileData(firstName: string, lastName: string): Promise<any> {
    // Find user and profile data for debugging
    const result = await this.profileRepository
      .createQueryBuilder('profile')
      .leftJoinAndSelect('profile.user', 'user')
      .where('LOWER(user.firstName) = LOWER(:firstName)', { firstName })
      .andWhere('LOWER(user.lastName) = LOWER(:lastName)', { lastName })
      .select([
        'profile.id',
        'profile.isPublic',
        'profile.isProfileVisible',
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
      ])
      .getRawAndEntities();

    return {
      query: {
        firstName,
        lastName,
      },
      rawResults: result.raw,
      entities: result.entities,
      // Removed result.query as it does not exist on the returned object
    };
  }

  async getPublicProfileBySlug(slug: string): Promise<ProfileEntity> {
    try {
      // Split the slug into first name and last name
      const [firstName, lastName] = slug.split('-').map((part) => part.toLowerCase());

      console.log(`Searching for profile with firstName: ${firstName}, lastName: ${lastName}`);

      // Find the profile by joining with user table
      const profile = await this.profileRepository
        .createQueryBuilder('profile')
        .innerJoinAndSelect('profile.user', 'user')
        .where('LOWER(profile.firstName) = LOWER(:firstName)', { firstName })
        .andWhere('LOWER(profile.lastName) = LOWER(:lastName)', { lastName })
        .andWhere('(profile.isPublic = true OR profile.isProfileVisible = true)')
        .select([
          'profile.id',
          'profile.firstName',
          'profile.lastName',
          'profile.location',
          'profile.bio',
          'profile.industry',
          'profile.experienceYears',
          'profile.experienceMonths',
          'profile.skills',
          'profile.workExperience',
          'profile.education',
          'profile.profileImage',
          'profile.profileImageThumbnail',
          'profile.city',
          'profile.state',
          'profile.country',
          'profile.isPublic',
          'profile.isProfileVisible',
          'user.id',
        ])
        .getOne();

      console.log('Found profile:', profile);

      if (!profile) {
        throw new NotFoundException('Profile not found or is not public');
      }

      // Check if the profile is actually public
      if (!profile.isPublic && !profile.isProfileVisible) {
        throw new NotFoundException('Profile is not public');
      }

      return profile;
    } catch (error) {
      console.error('Error in getPublicProfileBySlug:', error);
      throw error;
    }
  }
}
