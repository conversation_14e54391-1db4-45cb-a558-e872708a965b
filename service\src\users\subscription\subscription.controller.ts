import { Controller, Get, Post, Body, Param, Put, UseGuards, Query, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { SubscriptionService } from './subscription.service';
import { SubscriptionEntity } from './subscription.entity';
import { SubscriptionStatus } from './subscription.enum';

@Controller('subscriptions')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async getSubscriptions(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: SubscriptionStatus,
    @Query('searchTerm') searchTerm?: string,
  ) {
    return this.subscriptionService.findAll(page, limit, status, searchTerm);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async getSubscriptionById(@Param('id') id: string) {
    return this.subscriptionService.findOne(id);
  }

  @Post()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async createSubscription(@Request() req, @Body() subscriptionData: Partial<SubscriptionEntity>) {
    return this.subscriptionService.create(subscriptionData, req.user.id);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async updateSubscription(
    @Request() req,
    @Param('id') id: string,
    @Body() subscriptionData: Partial<SubscriptionEntity>,
  ) {
    return this.subscriptionService.update(id, subscriptionData, req.user.id);
  }

  @Post(':id/cancel')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async cancelSubscription(@Request() req, @Param('id') id: string) {
    return this.subscriptionService.cancel(id, req.user.id);
  }

  @Post(':id/renew')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async renewSubscription(@Request() req, @Param('id') id: string) {
    return this.subscriptionService.renew(id, req.user.id);
  }

  @Get('user/me')
  async getMySubscription(@Request() req) {
    return this.subscriptionService.findByUserId(req.user.id);
  }
}
