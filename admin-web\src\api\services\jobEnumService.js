import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const jobEnumService = {
  // Get all job enums - using mock for now
  async getJobEnums() {
    try {
     return await httpClient.get(ENDPOINTS.JOBS.ENUMS)
      
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch job enums')
    }
  },

  async getIndustries() {
    try {
     return await httpClient.get(ENDPOINTS.JOBS.INDUSTRIES)
      
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch job enums')
    }
  },

  // Get specific enum category
  async getEnumCategory(category) {
    try {
      const response = await this.getJobEnums()
      
      if (response.success && response.data[category]) {
        return {
          success: true,
          data: response.data[category],
          message: `${category} retrieved successfully`
        }
      } else {
        throw new Error(`Category '${category}' not found`)
      }
    } catch (error) {
      throw new Error(error.message || `Failed to fetch ${category}`)
    }
  }
}