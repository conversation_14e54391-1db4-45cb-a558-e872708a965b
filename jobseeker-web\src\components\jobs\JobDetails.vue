<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useJobsStore } from '@/stores/jobs'
import { useApplicationsStore } from '@/stores/applications'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import alertManager from '@/utils/alertManager'
import { formatBooleanValue } from '@/utils/formatters'
import { applicationsService } from '@/api/services/applicationsService'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const jobsStore = useJobsStore()
const applicationsStore = useApplicationsStore()

// Local state
const isLoading = ref(false)
const error = ref(null)
const isJobSaved = computed(() => {
  if (!currentJob.value) {
    return false
  }

  return currentJob.value.isFavorite;
})

// Get current job from store
const currentJob = computed(() => jobsStore.currentJob)

// Helper functions
const getCompanyName = () => {
  if (!currentJob.value) return ''

  const employer = currentJob.value.employer
  if (employer?.firstName && employer?.lastName) {
    return `${employer.firstName} ${employer.lastName}`.trim()
  }

  if (employer?.email) {
    return employer.email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  return 'Unknown Company'
}

const getCompanyInitial = () => {
  const companyName = getCompanyName()
  return companyName.charAt(0).toUpperCase()
}

const getJobColor = () => {
  // Generate a consistent color based on job ID
  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
    '#8b5cf6', '#06b6d4', '#ec4899', '#84cc16'
  ]

  if (!currentJob.value?.id) return colors[0]

  // Simple hash function to get consistent color
  let hash = 0
  const id = currentJob.value.id.toString()
  for (let i = 0; i < id.length; i++) {
    hash = id.charCodeAt(i) + ((hash << 5) - hash)
  }

  return colors[Math.abs(hash) % colors.length]
}

const hasAdditionalInfo = () => {
  if (!currentJob.value) return false

  return currentJob.value.workingHours ||
    currentJob.value.accommodation ||
    currentJob.value.transportation ||
    currentJob.value.foodProvided ||
    currentJob.value.safetyEquipment ||
    currentJob.value.trainingProvided
}

const formatJobType = (type) => {
  const typeMap = {
    'FULL_TIME': 'Full-time',
    'PART_TIME': 'Part-time',
    'CONTRACT': 'Contract',
    'TEMPORARY': 'Temporary',
    'FREELANCE': 'Freelance'
  }
  return typeMap[type] || type
}

const formatExperienceLevel = (level) => {
  const levelMap = {
    'FRESHER': 'Entry Level',
    'EXPERIENCED': 'Experienced',
    'EXPERT': 'Expert Level',
    'SENIOR': 'Senior Level'
  }
  return levelMap[level] || level
}

const getStatusSeverity = (status) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'DRAFT': return 'warning'
    case 'CLOSED': return 'danger'
    case 'PAUSED': return 'secondary'
    default: return 'info'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'just now';
 
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
 
  const diffSeconds = Math.ceil(diffTime / 1000);
  const diffMinutes = Math.ceil(diffTime / (1000 * 60));
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
 
  if (diffSeconds < 60) return 'just now';
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

const formatFullDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Actions
const goBack = () => {
  router.back()
}

const saveJob = async () => {
  if (!currentJob.value) {
    return
  }

  const response = await jobsStore.saveJob(currentJob.value.id)
    currentJob.value.isFavorite = response.isFavorite;
    alertManager.showSuccess('Success', 'Job removed from saved list')
}

const applyToJob = async () => {
  if (currentJob.value.hasApplied) {
    const confirmed = await alertManager.showConfirm(
        t('Withdrawn Application') || 'Withdraw Application',
        t('applications.withdrawConfirm', { job: currentJob.value.title }),
        { confirmText: 'Yes', cancelText: t('common.cancel') || 'No' }
      );
      if (confirmed) {
        try {
          await applicationsService.withdraw(currentJob.value.applicationId)
          // await applicationsStore.fetchApplications()
          alertManager.showSuccess('Application Withdrawn', 'Your application has been withdrawn successfully')
          console.log('Applications in store:', applicationsStore.applications)
          currentJob.value.hasApplied = false
        } catch (error) {
          alert(error.message || 'Failed to withdraw application')
        }
      }
    return;
  }
  try {
    // Create a pending application instead of saving immediately
    const pendingApp = applicationsStore.createPendingApplication(currentJob.value);
    currentJob.value.hasApplied = true
    alertManager.showSuccess('Added to Applications', `"${currentJob.value.title}" has been added to your applications. Click "Save" to submit it.`);
  } catch (err) {
    alertManager.showError('Error', 'Failed to add application');
  }
}

const shareJob = (method) => {
  const jobUrl = window.location.href

  if (method === 'copy') {
    navigator.clipboard.writeText(jobUrl).then(() => {
      alertManager.showSuccess('Link Copied', 'Job URL copied to clipboard')
    }).catch(() => {
      alertManager.showError('Copy Failed', 'Failed to copy link to clipboard')
    })
  } else if (method === 'email') {
    const subject = `Check out this job: ${currentJob.value?.title}`
    const body = `I found this job that might interest you:\n\n${currentJob.value?.title} at ${getCompanyName()}\n\n${jobUrl}`
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
  }
}

const fetchJobDetails = async (jobId) => {
  isLoading.value = true
  error.value = null

  try {
    // Check if job is already in store
    if (currentJob.value && currentJob.value.id === jobId) {
      isLoading.value = false
      return
    }

    // Fetch job details from API
    await jobsStore.fetchJobById(jobId)

  } catch (err) {
    console.error('Failed to fetch job:', err)
    error.value = err.message || t('jobs.jobNotFoundDesc')
  } finally {
    isLoading.value = false
  }
}

const retryFetch = () => {
  const jobId = route.params.id
  fetchJobDetails(jobId)
}

const goToCompanyProfile = () => {
  const companyId = currentJob.value?.company?.id || currentJob.value?.employer?.id;
  if (companyId) {
    router.push(`/companies/${companyId}`);
  } else {
    alertManager.showError('Error', 'Company profile not available');
  }
}

onMounted(async () => {
  fetchJobDetails(route.params.id);
});
</script>

<template>
  <div class="job-details-page flex-1 overflow-x-hidden overflow-y-auto">
    <div v-if="currentJob" class="content-header">
        <Button @click="goBack" :icon="'pi pi-arrow-left'"
              :label="'Back to Jobs'" :severity="'secondary'"
              outlined class="save-button" />
              <div class="right-action-btn">
                <Button @click="saveJob" :icon="isJobSaved ? 'pi pi-heart-fill' : 'pi pi-heart'"
                      :label="isJobSaved ? 'Saved' : t('dashboard.saveJob')" :severity="isJobSaved ? 'danger' : 'info'"
                      outlined class="save-button" />
                    <Button
                      @click="applyToJob"
                      :label="currentJob.hasApplied ? 'Withdraw' : 'Apply'"
                      class="apply-button"
                    />
              </div>
      </div>
    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-container">
        <i class="pi pi-spin pi-spinner loading-icon"></i>
        <h3>{{ t('common.loading') }}</h3>
        <p>Loading job details...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-state flex">
      <div class="error-container">
        <i class="pi pi-exclamation-triangle error-icon"></i>
        <h3>{{ t('jobs.jobNotFound') }}</h3>
        <p>{{ error }}</p>
        <div class="error-actions">
          <Button @click="retryFetch" :label="t('auth.tryAgain')" icon="pi pi-refresh" class="retry-button" />
          <Button @click="goBack" :label="t('jobs.backToJobs')" icon="pi pi-arrow-left" outlined />
        </div>
      </div>
    </div>

    <!-- Job Details Content -->
    <div v-else-if="currentJob" class="job-details-content flex">
      
      <!-- Page Header -->
      <!-- <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <div class="header-text">
              <h1>{{ t('navigation.jobs') }}</h1>
              <Breadcrumb :home="home" :model="items" />
            </div>
          </div>
        </div>
      </div> -->

      <div class="job-main">
        <!-- Job Header -->
        <div class="job-header-card">
          <div class="job-header-content">
            <div class="job-avatar">
              <Avatar :label="getCompanyInitial()" :style="{ backgroundColor: getJobColor() }" shape="circle"
                size="xlarge" />
            </div>
            <div class="job-header-info">
              <div class="job-badges">
                <Tag v-if="currentJob.isFeatured" :value="t('jobs.featured')" severity="success" />
                <Tag v-if="currentJob.urgency === 'URGENT'" :value="t('jobs.urgent')" severity="danger" />
                <Tag :value="currentJob.status" :severity="getStatusSeverity(currentJob.status)" />
                <Tag :value="currentJob.paymentType" severity="info" />
              </div>
              <h1 class="job-title">{{ currentJob.title }}</h1>
              <h2 class="job-company">{{ getCompanyName() }}</h2>
              <div class="job-meta">
                <span class="job-location">
                  <i class="pi pi-map-marker"></i>
                  {{ currentJob.location }}
                </span>
                <span class="job-type">
                  <i class="pi pi-briefcase"></i>
                  {{ formatJobType(currentJob.jobType) }}
                </span>
                <span class="job-experience">
                  <i class="pi pi-user"></i>
                  {{ formatExperienceLevel(currentJob.experienceLevel) }}
                </span>
                <span class="job-posted">
                  <i class="pi pi-calendar"></i>
                  {{ t('jobs.postedAgo', { time: formatDate(currentJob.createdAt) }) }}
                </span>
                <span class="job-vacancies">
                  <i class="pi pi-users"></i>
                  {{ currentJob.vacancies }} {{ currentJob.vacancies === 1 ? 'position' : 'positions' }}
                </span>
              </div>
              <div class="job-salary">
                <i class="pi pi-indian-rupee"></i>
                {{ parseFloat(currentJob.salary).toLocaleString() }} / {{ currentJob.paymentType.toLowerCase() }}
              </div>
            </div>
          </div>
          
          
        </div>

        <!-- Job Description -->
        <div class="job-section">
          <h3 class="section-title">{{ t('jobs.jobDetails') }}</h3>
          <div class="section-content">
            <div class="job-description" v-html="currentJob.description"></div>
          </div>
        </div>

        <!-- Working Hours & Additional Info -->
        <div class="job-section" v-if="hasAdditionalInfo()">
          <h3 class="section-title">Additional Information</h3>
          <div class="section-content">
            <div class="additional-info-grid">
              <div v-if="currentJob.workingHours" class="info-item">
                <i class="pi pi-clock"></i>
                <div>
                  <strong>Working Hours</strong>
                  <p>{{ currentJob.workingHours }}</p>
                </div>
              </div>
              <div v-if="currentJob.accommodation" class="info-item">
                <i class="pi pi-home"></i>
                <div>
                  <strong>Accommodation</strong>
                  <p>{{ formatBooleanValue(currentJob.accommodation) }}</p>
                </div>
              </div>
              <div v-if="currentJob.transportation" class="info-item">
                <i class="pi pi-car"></i>
                <div>
                  <strong>Transportation</strong>
                  <p>{{ formatBooleanValue(currentJob.transportation) }}</p>
                </div>
              </div>
              <div v-if="currentJob.foodProvided" class="info-item">
                <i class="pi pi-shopping-cart"></i>
                <div>
                  <strong>Food Provided</strong>
                  <p>{{ formatBooleanValue(currentJob.foodProvided) }}</p>
                </div>
              </div>
              <div v-if="currentJob.safetyEquipment" class="info-item">
                <i class="pi pi-shield"></i>
                <div>
                  <strong>Safety Equipment</strong>
                  <p>{{ formatBooleanValue(currentJob.safetyEquipment) }}</p>
                </div>
              </div>
              <div v-if="currentJob.trainingProvided" class="info-item">
                <i class="pi pi-graduation-cap"></i>
                <div>
                  <strong>Training Provided</strong>
                  <p>{{ formatBooleanValue(currentJob.trainingProvided) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Responsibilities -->
        <div class="job-section" v-if="currentJob.responsibilities && currentJob.responsibilities.length > 0">
          <h3 class="section-title">{{ t('jobs.keyResponsibilities') }}</h3>
          <div class="section-content">
            <div class="skills-grid">
              <Tag v-for="item in currentJob.responsibilities" :key="item" :value="item" class="skill-tag" />
            </div>
          </div>
        </div>

        <!-- Requirements -->
        <div class="job-section" v-if="currentJob.requirements && currentJob.requirements.length > 0">
          <h3 class="section-title">{{ t('jobs.requirements') }}</h3>
          <div class="section-content">
            <div class="skills-grid">
              <Tag v-for="item in currentJob.requirements" :key="item" :value="item" class="skill-tag" />
            </div>
          </div>
        </div>

        <!-- Skills -->
        <div class="job-section" v-if="currentJob.skills && currentJob.skills.length > 0">
          <h3 class="section-title">{{ t('jobs.requiredSkills') }}</h3>
          <div class="section-content">
            <div class="skills-grid">
              <Tag v-for="skill in currentJob.skills" :key="skill" :value="skill" class="skill-tag" />
            </div>
          </div>
        </div>

        <!-- Benefits -->
        <div class="job-section" v-if="currentJob.benefits && currentJob.benefits.length > 0">
          <h3 class="section-title">{{ t('jobs.benefitsPerks') }}</h3>
          <div class="section-content">
            <div class="skills-grid">
              <Tag v-for="item in currentJob.benefits" :key="item" :value="item" class="skill-tag" />
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="job-sidebar" v-if="currentJob">
        <!-- Quick Apply Card -->
        <div class="sidebar-card apply-card">
          <h3>{{ t('jobs.quickApply') }}</h3>
          <!-- <p>{{ t('jobs.readyForNextStep') }}</p> -->
          <!-- <Button 
              @click="applyToJob"
              :label="hasApplied ? t('jobs.applied') : t('dashboard.applyNow')"
              class="apply-button-sidebar"
              :disabled="hasApplied"
              size="large"
            >
              <template v-if="hasApplied">
                <i class="pi pi-check"></i>
                {{ t('jobs.applied') }}
              </template>
            </Button> -->
          <div class="apply-stats">
            <div class="stat">
              <span class="stat-number">{{ currentJob.applicationsCount || 0 }}</span>
              <span class="stat-label">{{ t('jobs.applicants', { count: '' }) }}</span>
            </div>
            <div class="stat">
              <span class="stat-number">{{ currentJob.views }}</span>
              <span class="stat-label">{{ t('jobs.views', { count: '' }) }}</span>
            </div>
          </div>
        </div>

        <!-- Company Info Card -->
        <div class="sidebar-card company-card">
          <h3>{{ t('jobs.aboutCompany', { company: getCompanyName() }) }}</h3>
          <div class="company-info">
            <div class="company-avatar">
              <Avatar :label="getCompanyInitial()" :style="{ backgroundColor: getJobColor() }" shape="circle"
                size="large" />
            </div>
            <div class="company-details">
              <h4>{{ getCompanyName() }}</h4>
              <p>{{ currentJob.industry?.name || 'Industry' }}</p>
            </div>
          </div>
          <Button :label="t('jobs.viewCompanyProfile')" outlined class="company-button" icon="pi pi-external-link" @click="goToCompanyProfile" />
        </div>

        <!-- Job Details Card -->
        <div class="sidebar-card details-card">
          <h3>{{ t('jobs.jobDetails') }}</h3>
          <div class="details-list">
            <div class="detail-item">
              <span class="detail-label">{{ t('jobs.department') }}</span>
              <span class="detail-value">{{ currentJob.industry?.name || 'N/A' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">{{ t('jobs.jobType') }}</span>
              <span class="detail-value">{{ formatJobType(currentJob.jobType) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">{{ t('jobs.experienceLevel') }}</span>
              <span class="detail-value">{{ formatExperienceLevel(currentJob.experienceLevel) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Payment Type</span>
              <span class="detail-value">{{ currentJob.paymentType }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">{{ t('jobs.postedDate') }}</span>
              <span class="detail-value">{{ formatFullDate(currentJob.createdAt) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Last Updated</span>
              <span class="detail-value">{{ formatFullDate(currentJob.updatedAt) }}</span>
            </div>
          </div>
        </div>

        <!-- Share Card -->
        <div class="sidebar-card share-card">
          <h3>{{ t('jobs.shareJob') }}</h3>
          <div class="share-buttons">
            <Button @click="shareJob('copy')" icon="pi pi-copy" :label="t('jobs.copyLink')" outlined
              class="share-button" />
            <Button @click="shareJob('email')" icon="pi pi-envelope" :label="t('common.email')" outlined
              class="share-button" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.job-details-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.loading-state,
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.loading-container,
.error-container {
  text-align: center;
  max-width: 400px;
}

.loading-icon,
.error-icon {
  font-size: 4rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.loading-container h3,
.error-container h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.loading-container p,
.error-container p {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.job-details-content {
  margin: 0 auto;
  padding: 0 1rem;
  gap: 1rem;
}

.job-details-content .page-header {
  grid-column: 1 / -1;
}

.job-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  flex: 1;
}

.job-header-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.job-header-content {
  display: flex;
  gap: 1.5rem;
  flex: 1;
}

.job-header-info {
  flex: 1;
}

.job-badges {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.job-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.job-company {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1rem 0;
}

.job-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.job-location,
.job-type,
.job-experience,
.job-posted,
.job-vacancies {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.job-location i,
.job-type i,
.job-experience i,
.job-posted i,
.job-vacancies i {
  color: var(--primary-color);
}

.job-salary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.job-header-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.apply-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.apply-button:disabled {
  background: var(--green-500) !important;
  border-color: var(--green-500) !important;
  opacity: 1 !important;
}

.job-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--surface-border);
}

.section-content {
  color: var(--text-color-secondary);
  line-height: 1.6;
}

.job-description {
  font-size: 1rem;
  margin: 0;
}

.additional-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.info-item i {
  color: var(--primary-color);
  font-size: 1.25rem;
  margin-top: 0.25rem;
}

.info-item strong {
  display: block;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.info-item p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.responsibilities-list,
.requirements-list {
  margin: 0;
  padding-left: 1.5rem;
}

.responsibilities-list li,
.requirements-list li {
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.skills-grid {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.skill-tag {
  font-size: 0.85rem !important;
  padding: 0.5rem 1rem !important;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.benefit-icon {
  color: var(--green-500);
  font-weight: 600;
}

.job-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 0.5;
}

.sidebar-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.sidebar-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.apply-card p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

.apply-button-sidebar {
  width: 100%;
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
  margin-bottom: 1rem;
}

.apply-button-sidebar:disabled {
  background: var(--green-500) !important;
  border-color: var(--green-500) !important;
  opacity: 1 !important;
}

.apply-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.company-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.company-details h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 600;
}

.company-details p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.company-button {
  width: 100%;
}

.details-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--surface-border);
}

.detail-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.detail-value {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.share-button {
  width: 100%;
  justify-content: flex-start !important;
}

@media (max-width: 768px) {
  .job-details-content {
    flex-direction: column;
  }
  .job-header-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .job-header-actions {
    flex-direction: row;
    align-items: center;
    width: 100%;
  }

  .job-title {
    font-size: 2rem;
  }

  .job-company {
    font-size: 1.25rem;
  }

  .job-meta {
    flex-direction: column;
    gap: 0.75rem;
  }

  .additional-info-grid,
  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .error-actions {
    flex-direction: column;
  }

  .p-tag, .skill-tag, .job-badges .p-tag {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    margin-bottom: 0.25rem !important;
  }
}

.content-header {
  position: sticky;
  top: 0;
  z-index: 1;
  background: var(--surface-card);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
}

.right-action-btn {
    display: flex;
    gap: 0.5rem;
}
</style>