<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import { api } from '@/api'
import { useAuthStore } from '@/stores/auth'
import DataView from 'primevue/dataview';
import Card from 'primevue/card';
import NotificationsSkelton from './NotificationsSkelton.vue'
import { useGlobalToast } from '@/composables/useGlobalToast'

const router = useRouter()
const { t } = useI18n()
const { showToast } = useGlobalToast()

const isLoading = ref(false)
const authStore = useAuthStore ? useAuthStore() : null
const backendNotifications = ref([])

const markAllAsRead = async () => {
  try {
    notifications.value.forEach(n => n.isRead = true)
    showToast({
      severity: 'success',
      summary: 'Success',
      detail: 'All notifications marked as read'
    })
  } catch (error) {
    showToast({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to mark notifications as read'
    })
  }
}

const markAsRead = async (notification) => {
  try {
    await api.notifications.markAsRead(notification.id)
    notification.isRead = true
    showToast({
      severity: 'success',
      summary: 'Success',
      detail: 'Notification marked as read'
    })
  } catch (error) {
    showToast({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to mark notification as read'
    })
  }
}

const handleNotificationClick = (notification) => {
  if (!notification.isRead) {
    markAsRead(notification)
  }

  router.push('/jobseeker/applications')
}

const fetchNotifications = async () => {
  isLoading.value = true
  try {
    const userId = authStore?.user?.id
    if (!userId) return
    const response = await api.notifications.getNotifications({ userId, limit: 100 })
    const items = response?.data?.items;

    if (items) {
      items.forEach((item) => {
        item.data = JSON.parse(item.data)
      })
      backendNotifications.value = items
    }
  } catch (err) {
    showToast({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to fetch notifications'
    })
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchNotifications()
  // Load notifications data
  // Request browser notification permission if needed
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }
})

const getCompanyDisplayName = (job) => {
  if (job.company && job.company.name) return job.company.name;
  if (job.employer) {
    return job.employer.firstName || job.employer.contactPerson || job.employer.email || 'No Company Name';
  }
  return 'No Company Name';
};
</script>

<template>
  <div class="notifications-page-wrapper flex flex-col flex-1 overflow-hidden">
    <div class="notifications-page flex-1 flex overflow-hidden">
      <NotificationsSkelton v-if="isLoading" />

      <DataView
        v-else
        :value="backendNotifications"
        paginator
        :rows="5"
        class="notifications-dataview flex-col flex flex-1 overflow-hidden"
      >
        <template #list="slotProps">
          <div class="notifications-list-scroll overflow-y-auto p-4 space-y-3">
            <Card
              v-for="(item, index) in slotProps.items"
              :key="index"
              class="rounded-xl shadow-sm border border-gray-200 transition hover:shadow-md cursor-auto"
              :class="item.isRead ? 'bg-white' : 'bg-red-50 border-red-200'"
              @click="handleNotificationClick(item)"
            >
              <template #title>
                <div class="flex justify-between items-center">
                  <div class="flex items-center gap-2 text-lg font-semibold text-gray-800">
                    <i class="pi pi-bell text-primary-500" />
                    {{ item.title }}
                    <Tag v-if="!item.isRead" severity="danger" class="text-xs font-medium">New</Tag>
                  </div>

                  <Button
                    icon="pi pi-external-link"
                    variant="text"
                    rounded
                    aria-label="Open"
                    class="text-green-500 hover:text-green-700 transition"
                  />
                </div>
              </template>

              <template #content>
                <div class="text-sm text-gray-600 space-y-1">
                  <p>
                    <span class="font-medium text-gray-500">Job Title:</span>
                    {{ item.data?.jobTitle || '—' }}
                  </p>
                  <p>
                    <span class="font-medium text-gray-500">Current Status:</span>
                    <span class="font-bold text-gray-800">
                      {{ item.data?.status || '—' }}
                    </span>
                  </p>
                </div>
              </template>
            </Card>
          </div>
        </template>
      </DataView>
    </div>
  </div>
</template>

<style scoped>
.notifications-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.not-read {
    font-weight: 600;
}

:deep(.p-dataview-content) {
  flex: 1;
  overflow: hidden;
  background-color: transparent;
}

.notifications-list-scroll {
  height: 100%;
}

:deep(.p-paginator) {
  position: sticky;
  bottom: 0;
  background: var(--surface-card);
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    gap: 0.5rem;
  }

  .p-tag {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    margin-bottom: 0.25rem !important;
  }
}
</style>