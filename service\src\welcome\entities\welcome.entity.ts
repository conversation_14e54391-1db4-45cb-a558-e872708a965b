import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { OneToMany } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { DescriptionEntity } from './description.entity';
import { AudienceMessageEntity } from './audience-message.entity';
import { TestimonialEntity } from './testimonial.entity';

@Entity('welcome')
export class WelcomeEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Unique identifier for the welcome message' })
  id: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: 'The welcome message content' })
  message: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'URL of the welcome image', required: false })
  imageUrl: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'ID of the admin who created/updated the message', required: false })
  updatedBy: string;

  @Column({ type: 'varchar', length: 50, default: '1.0.0' })
  @ApiProperty({ description: 'Current app version', example: '1.0.0' })
  app_version: string;

  @Column({
    type: 'text',
    nullable: true,
    default: 'A new version of the app is available. Please update to continue using the app.',
  })
  @ApiProperty({
    description: 'Message to display when app is updated',
    example: 'A new version of the app is available. Please update to continue using the app.',
    required: false,
  })
  app_update_message: string;

  @CreateDateColumn()
  @ApiProperty({ description: 'Timestamp when the welcome message was created' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Timestamp when the welcome message was last updated' })
  updatedAt: Date;

  @Column()
  cmp_name: string;

  @Column({ nullable: true })
  logo_path: string;

  @Column({ default: false })
  show_signup: boolean;

  @Column({ default: false })
  show_login: boolean;

  @Column({ nullable: true })
  welcome_pop_msg: string;

  @Column({ nullable: true })
  base_url: string;

  @Column({ nullable: true })
  notification_url: string;

  @Column({ nullable: true })
  user_url: string;

  @OneToMany(() => DescriptionEntity, (desc) => desc.welcome, { cascade: true })
  descriptions: DescriptionEntity[];

  @OneToMany(() => AudienceMessageEntity, (msg) => msg.welcome, { cascade: true })
  audience_messages: AudienceMessageEntity[];

  @OneToMany(() => TestimonialEntity, (test) => test.welcome, { cascade: true })
  testimonials: TestimonialEntity[];
}
