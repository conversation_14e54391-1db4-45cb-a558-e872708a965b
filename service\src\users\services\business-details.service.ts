import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessDetailsEntity } from '../entities/business-details.entity';
import { UserEntity } from '../entities/user.entity';
import { UserRole } from '../enums/user.enum';
import { AdminActionType } from '../enums/admin.enum';

@Injectable()
export class BusinessDetailsService {
  constructor(
    @InjectRepository(BusinessDetailsEntity)
    private readonly businessDetailsRepository: Repository<BusinessDetailsEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  async create(
    userId: string,
    businessData: Partial<BusinessDetailsEntity>,
  ): Promise<BusinessDetailsEntity> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.role !== UserRole.EMPLOYER) {
      throw new BadRequestException('Only employers can have business details');
    }

    const existingDetails = await this.businessDetailsRepository.findOne({
      where: { user: { id: userId } },
    });
    if (existingDetails) {
      throw new BadRequestException('Business details already exist for this user');
    }

    const businessDetails = this.businessDetailsRepository.create({
      ...businessData,
      user: { id: userId },
    });

    return this.businessDetailsRepository.save(businessDetails);
  }

  async update(
    userId: string,
    businessData: Partial<BusinessDetailsEntity>,
  ): Promise<BusinessDetailsEntity> {
    const businessDetails = await this.businessDetailsRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!businessDetails) {
      throw new NotFoundException('Business details not found');
    }

    Object.assign(businessDetails, businessData);
    return this.businessDetailsRepository.save(businessDetails);
  }

  async verify(userId: string, adminId: string): Promise<BusinessDetailsEntity> {
    const businessDetails = await this.businessDetailsRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!businessDetails) {
      throw new NotFoundException('Business details not found');
    }

    businessDetails.isVerified = true;
    businessDetails.verifiedAt = new Date();
    businessDetails.verifiedBy = adminId;

    return this.businessDetailsRepository.save(businessDetails);
  }

  async unverify(userId: string, adminId: string): Promise<BusinessDetailsEntity> {
    const businessDetails = await this.businessDetailsRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!businessDetails) {
      throw new NotFoundException('Business details not found');
    }

    businessDetails.isVerified = false;
    businessDetails.verifiedAt = null;
    businessDetails.verifiedBy = null;

    return this.businessDetailsRepository.save(businessDetails);
  }

  async getByUserId(userId: string): Promise<BusinessDetailsEntity> {
    const businessDetails = await this.businessDetailsRepository.findOne({
      where: { user: { id: userId } },
    });
    if (!businessDetails) {
      throw new NotFoundException('Business details not found');
    }
    return businessDetails;
  }

  async searchBusinesses(
    page: number = 1,
    limit: number = 10,
    searchTerm?: string,
    isVerified?: boolean,
  ) {
    const queryBuilder = this.businessDetailsRepository
      .createQueryBuilder('business')
      .leftJoinAndSelect('business.user', 'user')
      .where('user.role = :role', { role: UserRole.EMPLOYER });

    if (searchTerm) {
      queryBuilder.andWhere(
        '(business.shopName ILIKE :searchTerm OR business.shopAddress ILIKE :searchTerm OR business.gstNumber ILIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` },
      );
    }

    if (isVerified !== undefined) {
      queryBuilder.andWhere('business.isVerified = :isVerified', { isVerified });
    }

    const [businesses, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      businesses,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
