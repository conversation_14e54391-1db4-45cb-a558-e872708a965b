import {
  Entity,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum ErrorStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  IGNORED = 'IGNORED',
}

@Entity('error_logs')
export class ErrorLogEntity {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'Unique identifier for the error log' })
  id: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: 'Error message or stack trace' })
  errorMessage: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Screen or component where error occurred' })
  screen: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'User ID if error occurred for authenticated user' })
  userId: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Browser information' })
  userAgent: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'URL where error occurred' })
  url: string;

  @Column({ type: 'json', nullable: true })
  @ApiProperty({ description: 'Additional error context or metadata' })
  metadata: Record<string, any>;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Error severity level' })
  severity: string;

  @Column({
    type: 'enum',
    enum: ErrorStatus,
    default: ErrorStatus.OPEN,
  })
  @ApiProperty({
    description: 'Current status of the error',
    enum: ErrorStatus,
    example: ErrorStatus.OPEN,
  })
  status: ErrorStatus;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Resolution notes or comments' })
  resolutionNotes: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'ID of the user who last updated the status' })
  updatedBy: string;

  @CreateDateColumn()
  @ApiProperty({ description: 'Timestamp when the error occurred' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Timestamp when the error was last updated' })
  updatedAt: Date;
}
