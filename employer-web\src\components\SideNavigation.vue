<template>
  <div class="side-navigation" :class="{ 'collapsed': isCollapsed }">
    <!-- Navigation Header -->
    <div class="nav-header">
      <div class="logo" v-if="!isCollapsed">
        <img src="../assets/jd-logo.png" alt="Logo" width="160" />
      </div>
      <div class="logo-collapsed" v-else>
        <img src="../assets/jd-icon.png" alt="Logo" width="20" />
      </div>
      <Button 
        @click="toggleCollapse"
        icon="pi pi-bars"
        text
        class="collapse-toggle"
        v-tooltip.right="isCollapsed ? 'Expand Menu' : 'Collapse Menu'"
      />
    </div>

    <!-- User Profile Section -->
    <div class="user-profile" v-if="!isCollapsed">
      <div class="user-avatar">
        <Avatar 
          :label="getUserInitials()"
          size="large"
          class="profile-avatar"
        />
      </div>
      <div class="user-info">
        <h4>{{ getUserName() }}</h4>
        <p>{{ t('profile.employer') }}</p>
      </div>
    </div>
    <div class="user-profile-collapsed" v-else>
      <Avatar 
        :label="getUserInitials()"
        size="normal"
        class="profile-avatar"
        v-tooltip.right="getUserName()"
      />
    </div>

    <!-- Navigation Menu -->
    <nav class="nav-menu">
      <div class="nav-section">
        <h5 v-if="!isCollapsed" class="nav-section-title">{{ t('navigation.main') }}</h5>
        
        <router-link 
          to="/employer/dashboard" 
          class="nav-item"
          :class="{ active: isActiveRoute('/employer/dashboard') }"
        >
          <i class="pi pi-home nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('navigation.dashboard') }}</span>
        </router-link>

        <router-link 
          to="/employer/jobs" 
          class="nav-item"
          :class="{ active: isActiveRoute('/employer/jobs') }"
        >
          <i class="pi pi-briefcase nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('navigation.postedJobs') }}</span>
        </router-link>

        <!-- <router-link 
          to="/employer/jobs/saved" 
          class="nav-item"
          :class="{ active: isActiveRoute('/employer/jobs/saved') }"
        >
          <i class="pi pi-heart nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('dashboard.savedJobs') }}</span>
          <Badge 
            v-if="savedJobsCount > 0" 
            :value="savedJobsCount" 
            severity="info"
            class="nav-badge"
          />
        </router-link> -->

        <router-link 
          to="/employer/applications" 
          class="nav-item"
          :class="{ active: isActiveRoute('/employer/applications') }"
        >
          <i class="pi pi-send nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('navigation.applicants') }}</span>
          <Badge 
            v-if="pendingApplicationsCount > 0" 
            :value="pendingApplicationsCount" 
            severity="warning"
            class="nav-badge"
          />
        </router-link>

        <router-link 
          to="/employer/profile" 
          class="nav-item"
          :class="{ active: isActiveRoute('/employer/profile') }"
        >
          <i class="pi pi-user nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('navigation.profile') }}</span>
        </router-link>
      </div>

      <div class="nav-section">
        <h5 v-if="!isCollapsed" class="nav-section-title">{{ t('navigation.tools') }}</h5>
        
        <!-- <div class="nav-item" @click="refreshData">
          <i class="pi pi-refresh nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('common.refresh') }}</span>
        </div> -->

        <router-link 
          to="/employer/notifications" 
          class="nav-item"
          :class="{ active: isActiveRoute('/employer/notifications') }"
        >
          <i class="pi pi-bell nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">Notifications</span>
          <Badge 
            v-if="notificationsCount > 0" 
            :value="notificationsCount" 
            severity="danger"
            class="nav-badge"
          />
        </router-link>
      </div>

      <div class="nav-section">
        <h5 v-if="!isCollapsed" class="nav-section-title">{{ t('navigation.preferences') }}</h5>
        
        <div class="nav-item theme-toggle-item">
          <i class="pi pi-palette nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('common.theme') }}</span>
          <div v-if="!isCollapsed" class="nav-action">
            <Button
              @click="toggleTheme"
              :icon="isDark ? 'pi pi-sun' : 'pi pi-moon'"
              text
              size="small"
              :aria-label="isDark ? t('common.switchToLight') : t('common.switchToDark')"
              class="theme-toggle-btn"
            />
          </div>
        </div>

        <div class="nav-item language-item">
          <i class="pi pi-globe nav-icon"></i>
          <span v-if="!isCollapsed" class="nav-label">{{ t('common.language') }}</span>
          <div v-if="!isCollapsed" class="nav-action">
            <Select
              v-model="selectedLanguage"
              @change="handleLanguageChange"
              :options="languages"
              optionLabel="nativeName"
              optionValue="code"
              class="language-dropdown"
              :placeholder="t('common.language')"
            >
              <template #value="slotProps">
                <div v-if="slotProps.value" class="language-option">
                  <span class="language-flag">{{ getFlag(slotProps.value) }}</span>
                  <span class="language-name">{{ getLanguageName(slotProps.value) }}</span>
                </div>
              </template>
              <template #option="slotProps">
                <div class="language-option">
                  <span class="language-flag">{{ getFlag(slotProps.option.code) }}</span>
                  <span class="language-name">{{ slotProps.option.nativeName }}</span>
                </div>
              </template>
            </Select>
          </div>
        </div>
      </div>
    </nav>

    <!-- Navigation Footer -->
    <div class="nav-footer">
      <div class="nav-item logout-item" @click="handleLogout">
        <i class="pi pi-sign-out nav-icon"></i>
        <span v-if="!isCollapsed" class="nav-label">{{ t('common.logout') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Badge from 'primevue/badge'
import Select from 'primevue/select'
import { changeLanguage, getCurrentLanguage, getAvailableLanguages } from '@/i18n'

const router = useRouter()
const route = useRoute()
const { t, locale } = useI18n()
const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// Navigation state
const isCollapsed = ref(false)
const isDark = ref(false)
const selectedLanguage = ref('')
const languages = ref(getAvailableLanguages())

// Mock notification counts
const notificationsCount = ref(3)
const savedJobsCount = ref(8) // Mock saved jobs count
const pendingApplicationsCount = computed(() => 
  dashboardStore.stats.pendingApplications || 0
)

const languageFlags = {
  en: '🇺🇸',
  hi: '🇮🇳'
}

// Precise route matching function
const isActiveRoute = (targetPath) => {
  const currentPath = route.path
  
  // Exact match for dashboard
  if (targetPath === '/employer/dashboard') {
    return currentPath === '/employer/dashboard'
  }
  
  // For saved jobs, check exact path
  if (targetPath === '/employer/jobs/saved') {
    return currentPath === '/employer/jobs/saved'
  }
  
  // For jobs, match /employer/jobs but not /employer/jobs/saved and not /employer/jobs/[id]
  if (targetPath === '/employer/jobs') {
    return currentPath === '/employer/jobs' || 
           (currentPath.match(/^\/employer\/jobs\/[^\/]+$/) && !currentPath.includes('/saved'))
  }
  
  // Exact match for other routes
  return currentPath === targetPath
}

const getFlag = (code) => {
  return languageFlags[code] || '🌐'
}

const getLanguageName = (code) => {
  const lang = languages.value.find(l => l.code === code)
  return lang ? lang.nativeName : code
}

const getUserInitials = () => {
  const user = authStore.user
  if (!user) return 'U'
  
  if (user.profile?.firstName || user.profile?.lastName) {
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || 'U'
  }
  
  if (user.email) {
    return user.email.charAt(0).toUpperCase()
  }
  
  return 'U'
}

const getUserName = () => {
  const user = authStore.user
  if (!user) return t('profile.employer')
  
  if (user.profile?.firstName || user.profile?.lastName) {
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim() || t('profile.employer')
  }
  
  if (user.email) {
    return user.email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }
  
  return t('profile.employer')
}

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  localStorage.setItem('sideNavCollapsed', isCollapsed.value.toString())
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  updateTheme()
}

const updateTheme = () => {
  const html = document.documentElement
  
  if (isDark.value) {
    html.classList.add('dark-theme')
    localStorage.setItem('theme', 'dark')
  } else {
    html.classList.remove('dark-theme')
    localStorage.setItem('theme', 'light')
  }
}

const initializeTheme = () => {
  const savedTheme = localStorage.getItem('theme')
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  
  isDark.value = savedTheme === 'dark' || (!savedTheme && prefersDark)
  updateTheme()
}

const handleLanguageChange = (event) => {
  const newLanguage = event.value
  changeLanguage(newLanguage)
  selectedLanguage.value = newLanguage
}

const refreshData = () => {
  // Refresh data based on current page
  if (route.path === '/employer/dashboard') {
    dashboardStore.refreshStats()
  }
  // Add other page-specific refresh logic
}

const handleLogout = async () => {
  await authStore.logout()
  router.push('/employer/login')

  // window.location.href= '/'
}

// Watch for locale changes to keep the selector in sync
watch(locale, (newLocale) => {
  selectedLanguage.value = newLocale
})

onMounted(() => {
  // Initialize collapsed state
  const savedCollapsed = localStorage.getItem('sideNavCollapsed')
  if (savedCollapsed !== null) {
    isCollapsed.value = savedCollapsed === 'true'
  }
  
  // Initialize theme
  initializeTheme()
  
  // Initialize language
  selectedLanguage.value = getCurrentLanguage()
})
</script>

<style scoped>
.side-navigation {
  width: 280px;
  height: 100vh;
  background: var(--surface-card);
  border-right: 1px solid var(--surface-border);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: width 0.3s ease;
  overflow: hidden;
}

.side-navigation.collapsed {
  width: 80px;
}

.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1rem;
  border-bottom: 1px solid var(--surface-border);
  min-height: 80px;
  background: var(--surface-0);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.logo-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-color);
}

.collapse-toggle {
  color: var(--text-color-secondary) !important;
  width: 32px !important;
  height: 32px !important;
}

.user-profile {
  padding: 1rem;
  border-bottom: 1px solid var(--surface-border);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-profile-collapsed {
  padding: 1rem;
  border-bottom: 1px solid var(--surface-border);
  display: flex;
  justify-content: center;
}

.profile-avatar {
  background: var(--primary-color) !important;
}

.user-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.user-info p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
}

.nav-menu {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 1.5rem;
}

.nav-section-title {
  margin: 0 0 0.75rem 0;
  padding: 0 1rem;
  color: var(--text-color-secondary);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  color: var(--text-color-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.nav-item:hover {
  background: var(--surface-hover);
  color: var(--text-color);
}

/* CRITICAL: Only router-link nav-items can have active state */
.nav-item.active {
  background: var(--highlight-bg) !important;
  color: var(--text-color) !important;
  border-right: 3px solid var(--primary-color);
}

.nav-item.active .nav-icon {
  color: var(--text-color) !important;
}

/* Prevent non-router-link items from getting active styles */
.nav-item:not([href]).active {
  background: var(--surface-hover) !important;
  color: var(--text-color) !important;
  border-right: none !important;
}

.nav-item:not([href]).active .nav-icon {
  color: var(--text-color-secondary) !important;
}

.nav-icon {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.nav-label {
  font-size: 1rem;
  font-weight: 500;
  flex: 1;
}

.nav-badge {
  margin-left: auto;
}

.nav-action {
  margin-left: auto;
}

.theme-toggle-item,
.language-item {
  cursor: default;
}

.theme-toggle-item:hover,
.language-item:hover {
  background: var(--surface-hover);
}

.theme-toggle-btn {
  color: var(--text-color-secondary) !important;
  width: 32px !important;
  height: 32px !important;
}

.language-dropdown {
  min-width: 120px;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.language-flag {
  font-size: 1rem;
}

.language-name {
  font-size: 0.85rem;
}

.nav-footer {
  padding: 1rem 0;
  border-top: 1px solid var(--surface-border);
}

.logout-item {
  color: var(--red-500) !important;
}

.logout-item:hover {
  background: rgba(239, 68, 68, 0.1);
  color: var(--red-500) !important;
}

.logout-item .nav-icon {
  color: var(--red-500);
}

/* Collapsed state adjustments */
.collapsed .nav-item {
  justify-content: center;
  padding: 0.75rem;
}

.collapsed .nav-section-title {
  display: none;
}

.collapsed .theme-toggle-item,
.collapsed .language-item {
  display: none;
}

/* Scrollbar styling */
.nav-menu::-webkit-scrollbar {
  width: 4px;
}

.nav-menu::-webkit-scrollbar-track {
  background: transparent;
}

.nav-menu::-webkit-scrollbar-thumb {
  background: var(--surface-400);
  border-radius: 2px;
}

.nav-menu::-webkit-scrollbar-thumb:hover {
  background: var(--surface-500);
}

@media (max-width: 768px) {
  .side-navigation {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .side-navigation.mobile-open {
    transform: translateX(0);
  }
}
</style>