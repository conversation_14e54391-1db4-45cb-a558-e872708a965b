import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class PaginationDto {
  @ApiProperty({
    description: 'Page number (1-based)',
    required: false,
    default: 1,
    minimum: 1,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    default: 10,
    minimum: 1,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @ApiProperty({
    description: 'sortBy option for quick filter',
    required: false,
  })
  @Type(() => String)
  @IsString()
  @IsOptional()
  sortBy?: string = '';

  @ApiProperty({
    description: 'Industry ID',
    required: false,
  })
  @Type(() => String)
  @IsString()
  @IsOptional()
  industry?: string = '';

  @ApiProperty({
    description: 'Job Type',
    required: false,
  })
  @Type(() => String)
  @IsString()
  @IsOptional()
  jobType?: string = '';

  @ApiProperty({
    description: 'Job Title Search Key',
    required: false,
  })
  @Type(() => String)
  @IsString()
  @IsOptional()
  search?: string = '';

  @ApiProperty({
    description: 'Job location',
    required: false,
  })
  @Type(() => String)
  @IsString()
  @IsOptional()
  location?: string = '';

  @ApiProperty({
    description: 'Job experienceLevel',
    required: false,
  })
  @Type(() => String)
  @IsString()
  @IsOptional()
  experienceLevel?: string = '';

  @ApiProperty({
    description: 'Job paymentType',
    required: false,
  })
  @Type(() => String)
  @IsString()
  @IsOptional()
  paymentType?: string = '';

}
