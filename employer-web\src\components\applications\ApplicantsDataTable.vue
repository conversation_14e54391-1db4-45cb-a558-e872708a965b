<template>
  <div class="applicants-data-table">
    <DataTable
      :value="applications"
      :loading="isLoading"
      :paginator="true"
      :rows="pagination.limit"
      :totalRecords="pagination.total"
      :lazy="true"
      :sortField="sortField"
      :sortOrder="sortOrder"
      @page="onPageChange"
      @sort="onSort"
      class="applicants-table"
      stripedRows
      showGridlines
      responsiveLayout="scroll"
    >
      <!-- Applicant Info Column -->
      <Column field="applicant" header="Applicant" sortable="custom" :sortField="'applicant.profile.firstName'">
        <template #body="{ data }">
          <div class="applicant-info-cell">
            <Avatar 
              :label="getApplicantInitials(data.applicant)" 
              :style="{ backgroundColor: '#10b981' }"
              shape="circle"
              size="normal"
            />
            <div class="applicant-details">
              <div class="applicant-name">
                {{ getApplicantName(data.applicant) }}
              </div>
              <div class="applicant-email">{{ data.applicant?.email }}</div>
            </div>
          </div>
        </template>
      </Column>

      <!-- Job Column -->
      <Column field="job.title" header="Job Position" sortable="custom">
        <template #body="{ data }">
          <div class="job-info-cell">
            <div class="job-title">{{ data.job?.title }}</div>
            <div class="job-company">{{ data.job?.employer?.companyName }}</div>
          </div>
        </template>
      </Column>

      <!-- Location Column -->
      <Column field="applicant.profile.location" header="Location" sortable="custom">
        <template #body="{ data }">
          <div class="location-cell">
            <i class="pi pi-map-marker"></i>
            {{ getFormattedLocation(data.applicant?.profile) }}
          </div>
        </template>
      </Column>

     

      <!-- Applied Date Column -->
      <Column field="createdAt" header="Applied Date" sortable="custom">
        <template #body="{ data }">
          <div class="date-cell">
            {{ formatDate(data.createdAt) }}
          </div>
        </template>
      </Column>

      <!-- Resume Column -->
    <!-- <Column header="Resume" :sortable="false">
        <template #body="{ data }">
          <div class="resume-cell">
            <Button 
              v-if="data.resumeUrl"
              @click="downloadResume(data.resumeUrl)"
              icon="pi pi-download"
              text
              size="small"
              :label="t('applicants.downloadResume')"
            />
            <span v-else class="no-resume">{{ t('applicants.noResume') }}</span>
          </div>
        </template>
      </Column>-->

       <!-- Status Column -->
       <Column field="status" header="Status" sortable="custom">
        <template #body="{ data }">
          <div class="status-cell">
            <Dropdown
              v-model="data.status"
              :options="statusOptions"
              optionLabel="label"
              optionValue="value"
              class="status-dropdown"
              @change="e => updateApplicationStatus(data.id, e.value)"
              :disabled="data.status === 'WITHDRAWN'"
            />
            <Tag 
              :value="getStatusLabel(data.status)" 
              :severity="getStatusSeverity(data.status)"
              class="status-tag"
              :class="{'shortlisted': data.status === 'SHORTLISTED', 'interview-scheduled': data.status === 'INTERVIEW_SCHEDULED'}"
            />
          </div>
        </template>
      </Column>

      <!-- Actions Column -->
      <Column header="Actions" :sortable="false" class="actions-column">
        <template #body="{ data }">
          <div class="actions-cell">
            <Button 
              @click="viewApplicantProfile(data)"
              icon="pi pi-user"
              text
              size="small"
              class="action-btn"
              v-tooltip.top="t('tooltipViewProfile')"
            />
            
            <!-- Show Schedule Interview only for REVIEWING and SHORTLISTED -->
            <Button 
              v-if="data.status === 'REVIEWING' || data.status === 'SHORTLISTED'"
              @click="() => openScheduleModal(data)"
              icon="pi pi-calendar-plus"
              text
              size="small"
              severity="info"
              class="action-btn"
              v-tooltip.top="t('tooltipScheduleInterview')"
            />
            
            <!-- Show View Interview Details and Reschedule only for INTERVIEW_SCHEDULED -->
            <Button 
              v-if="data.status === 'INTERVIEW_SCHEDULED'"
              @click="() => openInterviewDetailsModal(data)"
              icon="pi pi-info-circle"
              text
              size="small"
              severity="info"
              class="action-btn"
              v-tooltip.top="t('tooltipViewInterviewDetails')"
            />
            <Button 
              v-if="data.status === 'INTERVIEW_SCHEDULED'"
              @click="() => openScheduleModal(data, true)"
              icon="pi pi-refresh"
              text
              size="small"
              severity="warning"
              class="action-btn"
              v-tooltip.top="t('tooltipRescheduleInterview')"
            />
          </div>
        </template>
      </Column>
    </DataTable>
    <Dialog v-model:visible="showScheduleModal" modal header="Schedule Interview" :style="{ width: '420px', maxWidth: '95vw' }" class="schedule-interview-dialog">
      <form class="schedule-form" @submit.prevent="scheduleInterview">
        <div class="form-row">
          <label for="interview-date" class="form-label">Date <span class="required">*</span></label>
          <Calendar id="interview-date" v-model="interviewForm.date" dateFormat="dd/mm/yy" showIcon class="form-input" />
          <small v-if="interviewFormErrors.date" class="p-error">{{ interviewFormErrors.date }}</small>
        </div>
        <div class="form-row">
          <label for="interview-time" class="form-label">Time <span class="required">*</span></label>
          <Calendar id="interview-time" v-model="interviewForm.time" timeOnly hourFormat="12" showIcon class="form-input" placeholder="Select time" />
          <small v-if="interviewFormErrors.time" class="p-error">{{ interviewFormErrors.time }}</small>
        </div>
        <div class="form-row">
          <label for="interview-location" class="form-label">Location <span class="required">*</span></label>
          <InputText id="interview-location" v-model="interviewForm.location" placeholder="Enter location" class="form-input" />
          <small v-if="interviewFormErrors.location" class="p-error">{{ interviewFormErrors.location }}</small>
        </div>
        <div class="form-row">
          <label for="interview-notes" class="form-label">Note</label>
          <Textarea id="interview-notes" v-model="interviewForm.notes" placeholder="Add notes (optional)" rows="3" class="form-input" />
        </div>
        <div class="form-footer">
          <Button label="Cancel" icon="pi pi-times" text @click="showScheduleModal = false" class="cancel-btn" type="button" />
          <Button label="Schedule" icon="pi pi-check" class="schedule-btn" type="submit" />
        </div>
      </form>
    </Dialog>
    <Dialog v-model:visible="showInterviewDetailsModal" modal header="Interview Details" :style="{ width: '420px', maxWidth: '95vw' }" class="interview-details-dialog">
      <div v-if="selectedApplication">
        <div><b>Date:</b> {{ selectedApplication.interviewDate || 'N/A' }}</div>
        <div><b>Time:</b> {{ selectedApplication.interviewTime || 'N/A' }}</div>
        <div><b>Location:</b> {{ selectedApplication.interviewLocation || 'N/A' }}</div>
        <div v-if="selectedApplication.interviewNotes && selectedApplication.interviewNotes.trim() !== ''"><b>Notes:</b> {{ selectedApplication.interviewNotes }}</div>
      </div>
    </Dialog>
  </div>

  <!-- Card/List view for mobile -->
  <div class="applicants-mobile-list">
    <div v-for="data in applications" :key="data.id" class="applicant-card">
      <div class="applicant-header">
        <Avatar :label="getApplicantInitials(data.applicant)" :style="{ backgroundColor: '#10b981' }" shape="circle" size="large" />
        <div>
          <div class="applicant-name">{{ getApplicantName(data.applicant) }}</div>
          <div class="applicant-email">{{ data.applicant?.email }}</div>
        </div>
      </div>
      <div class="applicant-info-row"><b>Job Position:</b> <span class="info-value">{{ data.job?.title }}</span></div>
      <div class="applicant-info-row"><b>Location:</b> <span class="info-value">{{ getFormattedLocation(data.applicant?.profile) }}</span></div>
      <div class="applicant-info-row"><b>Applied Date:</b> <span class="info-value">{{ formatDate(data.createdAt) }}</span></div>
      <div class="applicant-info-row status-row">
        <b>Status:</b>
        <Dropdown
          v-model="data.status"
          :options="statusOptions"
          optionLabel="label"
          optionValue="value"
          class="status-dropdown-mobile"
          @change="e => updateApplicationStatus(data.id, e.value)"
          :disabled="data.status === 'WITHDRAWN'"
        />
        <Tag :value="getStatusLabel(data.status)" :severity="getStatusSeverity(data.status)" class="status-tag-mobile" />
      </div>
      <div class="applicant-actions">
        <Button 
          @click="viewApplicantProfile(data)"
          icon="pi pi-user"
          text
          size="small"
          class="action-btn-mobile"
          v-if="isDesktop"
          v-tooltip.top="t('tooltipViewProfile')"
        />
        <Button 
          v-if="(data.status === 'REVIEWING' || data.status === 'SHORTLISTED') && isDesktop"
          @click="() => openScheduleModal(data)"
          icon="pi pi-calendar-plus"
          text
          size="small"
          severity="info"
          class="action-btn-mobile"
          v-tooltip.top="t('tooltipScheduleInterview')"
        />
        <Button 
          v-if="data.status === 'INTERVIEW_SCHEDULED' && isDesktop"
          @click="() => openInterviewDetailsModal(data)"
          icon="pi pi-info-circle"
          text
          size="small"
          severity="info"
          class="action-btn-mobile"
          v-tooltip.top="t('tooltipViewInterviewDetails')"
        />
        <Button 
          v-if="data.status === 'INTERVIEW_SCHEDULED' && isDesktop"
          @click="() => openScheduleModal(data, true)"
          icon="pi pi-refresh"
          text
          size="small"
          severity="warning"
          class="action-btn-mobile"
          v-tooltip.top="t('tooltipRescheduleInterview')"
        />
        <!-- For mobile, show icons only, no tooltip -->
        <Button 
          v-else-if="!isDesktop"
          @click="viewApplicantProfile(data)"
          icon="pi pi-user"
          text
          size="small"
          class="action-btn-mobile"
        />
        <Button 
          v-if="(data.status === 'REVIEWING' || data.status === 'SHORTLISTED') && !isDesktop"
          @click="() => openScheduleModal(data)"
          icon="pi pi-calendar-plus"
          text
          size="small"
          severity="info"
          class="action-btn-mobile"
        />
        <Button 
          v-if="data.status === 'INTERVIEW_SCHEDULED' && !isDesktop"
          @click="() => openInterviewDetailsModal(data)"
          icon="pi pi-info-circle"
          text
          size="small"
          severity="info"
          class="action-btn-mobile"
        />
        <Button 
          v-if="data.status === 'INTERVIEW_SCHEDULED' && !isDesktop"
          @click="() => openScheduleModal(data, true)"
          icon="pi pi-refresh"
          text
          size="small"
          severity="warning"
          class="action-btn-mobile"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import Dropdown from 'primevue/dropdown'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Calendar from 'primevue/calendar'
import alertManager from '@/utils/alertManager'
import { applicationsService } from '@/api/services/applicationsService'

const { t } = useI18n()

// Props
const props = defineProps({
  applications: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false
    })
  },
  sortField: {
    type: String,
    default: 'createdAt'
  },
  sortOrder: {
    type: Number,
    default: -1
  }
})

// Emits
const emit = defineEmits([
  'page-change',
  'sort-change',
  'view-profile',
  'update-status',
  'view-details',
  'download-resume'
])

// Methods
const getApplicantInitials = (applicant) => {
  if (!applicant?.profile?.firstName && !applicant?.profile?.lastName) {
    return applicant?.email?.charAt(0)?.toUpperCase() || 'A'
  }
  const first = applicant.profile.firstName?.charAt(0) || ''
  const last = applicant.profile.lastName?.charAt(0) || ''
  return (first + last).toUpperCase()
}

const getApplicantName = (applicant) => {
  if (!applicant?.profile?.firstName && !applicant?.profile?.lastName) {
    return applicant?.email || 'Unknown'
  }
  return `${applicant.profile.firstName || ''} ${applicant.profile.lastName || ''}`.trim()
}

// Update getStatusLabel to return only the label, not with parentheses or extra formatting
const getStatusLabel = (status) => {
  switch (status) {
    case 'PENDING':
      return 'Pending';
    case 'REVIEWING':
      return 'Reviewing';
    case 'SHORTLISTED':
      return 'Shortlisted';
    case 'INTERVIEW_SCHEDULED':
      return 'Interview Scheduled';
    case 'OFFERED':
      return 'Offered';
    case 'REJECTED':
      return 'Rejected';
    default:
      return status;
  }
}

const getStatusSeverity = (status) => {
  switch (status) {
    case 'PENDING':
    case 'UNDER_REVIEW':
      return 'warning'
    case 'INTERVIEW':
      return 'info'
    case 'INTERVIEW_SCHEDULED':
      return 'info'
    case 'OFFER':
    case 'OFFERED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    case 'WITHDRAWN':
      return 'secondary'
    default:
      return 'secondary'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

function formatDateToDDMMYYYY(dateObj) {
  if (!dateObj) return '';
  const d = new Date(dateObj);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
}

function formatTimeToHHMMAMPM(dateObj) {
  if (!dateObj) return '';
  let hours = dateObj.getHours();
  let minutes = dateObj.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  const strTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')} ${ampm}`;
  return strTime;
}

const statusOptions = [
  { label: 'Pending', value: 'PENDING' },
  { label: 'Reviewing', value: 'REVIEWING' },
  { label: 'Shortlisted', value: 'SHORTLISTED' },
  { label: 'Interview Scheduled', value: 'INTERVIEW_SCHEDULED' },
  { label: 'Offered', value: 'OFFERED' },
  { label: 'Rejected', value: 'REJECTED' }
]

const timeOptions = [
  '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
  '12:00 PM', '12:30 PM', '01:00 PM', '01:30 PM', '02:00 PM', '02:30 PM',
  '03:00 PM', '03:30 PM', '04:00 PM', '04:30 PM', '05:00 PM'
].map(t => ({ label: t, value: t }));

// Event handlers
const onPageChange = (event) => {
  emit('page-change', {
    page: event.page + 1,
    limit: event.rows
  })
}

const onSort = (event) => {
  emit('sort-change', {
    sortField: event.sortField,
    sortOrder: event.sortOrder
  })
}

const viewApplicantProfile = (application) => {
  emit('view-profile', application)
}

const updateApplicationStatus = (applicationId, status) => {
  emit('update-status', { applicationId, status })
}

const viewDetails = (application) => {
  emit('view-details', application)
}

const downloadResume = (resumeUrl) => {
  emit('download-resume', resumeUrl)
}

const showScheduleModal = ref(false)
const selectedApplication = ref(null)
const interviewForm = ref({
  date: null,
  time: '',
  location: '',
  notes: ''
})
const interviewFormErrors = ref({})

function openScheduleModal(application, isReschedule = false) {
  selectedApplication.value = application
  showScheduleModal.value = true
  if (isReschedule) {
    interviewForm.value = {
      date: application.interviewDate ? parseInterviewDate(application.interviewDate) : null,
      time: application.interviewTime || '',
      location: application.interviewLocation || '',
      notes: application.interviewNotes || ''
    }
  } else {
    interviewForm.value = { date: null, time: '', location: '', notes: '' }
  }
  interviewFormErrors.value = {}
}

function validateInterviewForm() {
  const errors = {}
  if (!interviewForm.value.date) errors.date = 'Date is required'
  if (!interviewForm.value.time) errors.time = 'Time is required'
  if (!interviewForm.value.location) errors.location = 'Location is required'
  return errors
}

async function scheduleInterview() {
  interviewFormErrors.value = validateInterviewForm()
  if (Object.keys(interviewFormErrors.value).length > 0) return
  if (selectedApplication.value) {
    const payload = {
      interviewDate: formatDateToDDMMYYYY(interviewForm.value.date),
      interviewTime: formatTimeToHHMMAMPM(interviewForm.value.time),
      interviewLocation: interviewForm.value.location,
      interviewNotes: interviewForm.value.notes,
      status: 'INTERVIEW_SCHEDULED' // Always set this status
    }
    try {
      await applicationsService.update(selectedApplication.value.id, payload)
      // Also call updateStatus to ensure status is saved in DB
      await applicationsService.updateStatus(selectedApplication.value.id, 'INTERVIEW_SCHEDULED')
      // Update local data instantly
      selectedApplication.value.status = 'INTERVIEW_SCHEDULED'
      selectedApplication.value.interviewDate = payload.interviewDate
      selectedApplication.value.interviewTime = payload.interviewTime
      selectedApplication.value.interviewLocation = payload.interviewLocation
      selectedApplication.value.interviewNotes = payload.interviewNotes
      // Also update the applications array if present
      const idx = props.applications.findIndex(app => app.id === selectedApplication.value.id)
      if (idx !== -1) {
        props.applications[idx].status = 'INTERVIEW_SCHEDULED'
        props.applications[idx].interviewDate = payload.interviewDate
        props.applications[idx].interviewTime = payload.interviewTime
        props.applications[idx].interviewLocation = payload.interviewLocation
        props.applications[idx].interviewNotes = payload.interviewNotes
      }
      alertManager.showSuccess('Interview Scheduled', 'Interview details have been saved.')
    } catch (error) {
      alertManager.showError('Error', 'Failed to schedule interview')
    }
  }
  showScheduleModal.value = false
}

const showInterviewDetailsModal = ref(false)
function openInterviewDetailsModal(application) {
  selectedApplication.value = application
  showInterviewDetailsModal.value = true
}

function parseInterviewDate(dateStr) {
  // Try ISO first
  const isoDate = new Date(dateStr);
  if (!isNaN(isoDate)) return isoDate;
  // Try dd/mm/yyyy
  if (dateStr && dateStr.includes('/')) {
    const [day, month, year] = dateStr.split('/');
    return new Date(`${year}-${month}-${day}`);
  }
  return null;
}

function getFormattedLocation(profile) {
  if (!profile) return 'Not specified'
  const { city, state } = profile
  let parts = []
  if (city) parts.push(city)
  if (state) parts.push(state)
  return parts.length ? parts.join(', ') : 'Not specified'
}

const isDesktop = computed(() => window.innerWidth > 768)
</script>

<style scoped>
.applicants-data-table {
  background: var(--surface-card);
  border-radius: 12px;
  overflow: hidden;
}

.applicants-table {
  width: 100%;
}

.applicant-info-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.applicant-details {
  display: flex;
  flex-direction: column;
}

.applicant-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.applicant-email {
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.job-info-cell {
  display: flex;
  flex-direction: column;
}

.job-title {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.job-company {
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.location-cell {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.location-cell i {
  color: var(--primary-color);
  font-size: 0.8rem;
}

.status-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 1rem;
  font-weight: 700;
  border-radius: 16px;
  box-sizing: border-box;
  width: 180px;
  min-width: 180px;
  max-width: 180px;
  height: 48px;
  padding: 0 16px;
  background: #f0f7ff;
  color: #1976d2;
  white-space: pre-line;
  word-break: break-word;
}
.status-tag.shortlisted, .status-tag.interview-scheduled {
  background: #eaf6ff;
  color: #22577a;
}

.date-cell {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.resume-cell {
  display: flex;
  align-items: center;
}

.no-resume {
  color: var(--text-color-secondary);
  font-size: 0.8rem;
  font-style: italic;
}

.actions-cell {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.actions-column {
  min-width: 200px;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 260px;
}
.status-tag {
  min-width: 110px;
  text-align: center;
  justify-content: center;
}
.status-dropdown {
  width: 130px;
  min-width: 130px;
  max-width: 130px;
  height: 36px;
  font-size: 1rem;
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* Hide card view on desktop, show on mobile */
.applicants-mobile-list {
  display: none;
}
@media (max-width: 768px) {
  .applicants-table {
    display: none !important; /* Hide the table on mobile */
  }
  .applicants-mobile-list {
    display: block;
  }
  .applicant-card {
    background: var(--surface-card);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    margin-bottom: 1.5rem;
    padding: 1.25rem 1.1rem 1.1rem 1.1rem;
    display: flex;
    flex-direction: column;
    gap: 0.9rem;
    position: relative;
  }
  .applicant-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.2rem;
  }
  .applicant-name {
    font-weight: 700;
    font-size: 1.15rem;
    margin-bottom: 0.1rem;
  }
  .applicant-email {
    color: var(--text-color-secondary);
    font-size: 0.97rem;
  }
  .applicant-info-row {
    font-size: 1.01rem;
    margin-bottom: 0.18rem;
    display: flex;
    align-items: center;
    gap: 0.4rem;
  }
  .info-value {
    font-weight: 500;
    color: var(--text-color-secondary);
    margin-left: 0.2rem;
  }
  .status-row {
    gap: 0.7rem;
    flex-wrap: wrap;
  }
  .status-tag-mobile {
    min-width: 0;
    font-size: 0.98rem;
    padding: 0.18rem 0.7rem;
    border-radius: 12px;
    height: 2rem;
    display: flex;
    align-items: center;
    margin-left: 0.2rem;
  }
  .status-dropdown-mobile {
    min-width: 120px;
    max-width: 180px;
    width: auto;
    font-size: 0.98rem;
    border-radius: 8px;
    margin-left: 0.2rem;
  }
  .applicant-actions {
    position: absolute;
    right: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1.2rem;
    margin: 0;
    padding: 0;
    border-top: none;
    background: transparent;
    z-index: 2;
  }
  .action-btn-mobile {
    min-width: 40px;
    max-width: 40px;
    flex: 0 0 40px;
    font-size: 1.3rem;
    padding: 0.5rem 0.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin: 0;
    background: transparent;
  }
  /* Remove label and wrapper styles */
  .action-btn-mobile-wrapper, .icon-label { display: none !important; }
}
.schedule-interview-dialog .p-dialog-content {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.schedule-form {
  display: flex;
  flex-direction: column;
  gap: 1.1rem;
}
.form-row {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.form-label {
  font-weight: 500;
  color: var(--text-color, #222);
  margin-bottom: 0.1rem;
}
.required {
  color: #e53935;
  font-size: 1em;
}
.form-input {
  width: 100%;
  min-height: 2.2rem;
  font-size: 1rem;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  padding: 0.4rem 0.7rem;
  background: #f9fafb;
  transition: border 0.2s;
}
.form-input:focus {
  border: 1.5px solid var(--primary-color, #10b981);
  outline: none;
  background: #fff;
}
.p-error {
  color: #e53935;
  font-size: 0.85rem;
  margin-top: 0.1rem;
}
.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 0.5rem;
}
.cancel-btn {
  color: #6b7280;
}
.schedule-btn {
  background: var(--primary-color, #10b981);
  border: none;
  color: #fff;
  font-weight: 600;
  border-radius: 6px;
  padding: 0.5rem 1.5rem;
  transition: background 0.2s;
}
.schedule-btn:hover {
  background: #059669;
}
</style> 