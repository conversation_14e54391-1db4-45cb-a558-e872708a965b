<script setup>
import { ref, reactive } from 'vue'
import { useTheme } from '../composables/useTheme'
import { useRouter } from 'vue-router'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import <PERSON><PERSON> from 'primevue/button'
import Message from 'primevue/message'
import Select from 'primevue/select'
import ToggleButton from 'primevue/togglebutton'
import { api } from '@/api'
import Toast from 'primevue/toast';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/stores/auth'
import { useI18n } from 'vue-i18n'

const { isDarkMode, theme, availableThemes, toggleDarkMode, setTheme } = useTheme()
const router = useRouter()
const authStore = useAuthStore()
const { t } = useI18n()
const toast = useToast();
const credentials = reactive({
  username: '',
  password: ''
})



const isLoading = ref(false)
const errorMessage = ref('')

const handleLogin = async () => {
  if (!credentials.username || !credentials.password) {
    errorMessage.value = 'Please fill in all fields'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const result = await api.auth.login(credentials)

    if (result.success) {
      // Check if user role is employer
      const userRole = result.data?.user?.role;
      
      if (['admin', 'super_admin'].indexOf(userRole) === -1) {
        toast.add({ 
          severity: 'error',
          summary: t('common.unAuthError'), 
          detail: 'You are not authorized to use this app. Please contact +91 8888890974 for assistance.',
          life: 6000 
        });

        // Clear auth data since user is not authorized
        authStore.logout()
        isLoading.value = false;
      }
      else {
        authStore.afterLogin(result.data);
      
      // User is authorized, proceed to dashboard
      router.push('/jdadmin/dashboard')
      }
  }
}catch(error) {
    errorMessage.value = error.message
  } finally {
    isLoading.value = false
  }
}

const handleThemeChange = (event) => {
  setTheme(event.value)
}
</script>

<template>
  <Toast />
  <div class="login-page">
    <div class="login-background">
      <div class="background-pattern"></div>
    </div>

    <div class="theme-controls">
      <Select
        :model-value="theme"
        :options="availableThemes"
        option-label="name"
        option-value="value"
        placeholder="Theme"
        @change="handleThemeChange"
        class="theme-dropdown"
      />
      <ToggleButton
        v-model="isDarkMode"
        on-icon="pi pi-moon"
        off-icon="pi pi-sun"
        @change="toggleDarkMode"
        class="theme-toggle"
        aria-label="Toggle dark mode"
      />
    </div>

    <div class="login-container">
      <Card class="login-card">
        <template #header>
          <div class="login-header">
            <div class="logo">
              <i class="pi pi-shield"></i>
            </div>
            <h1>Welcome to Admin</h1>
            <p>Sign in to access your dashboard</p>
          </div>
        </template>

        <template #content>
          <form @submit.prevent="handleLogin" class="login-form">
            <div class="form-group">
              <label for="email">Email</label>
              <InputText
                id="email"
                v-model="credentials.username"
                type="email"
                placeholder="Enter your email"
                :class="{ 'p-invalid': errorMessage }"
                autocomplete="email"
              />
            </div>

            <div class="form-group">
              <label for="password">Password</label>
              <Password
                id="password"
                v-model="credentials.password"
                placeholder="Enter your password"
                :class="{ 'p-invalid': errorMessage }"
                :feedback="false"
                toggle-mask
                autocomplete="current-password"
              />
            </div>

            <Message
              v-if="errorMessage"
              severity="error"
              :closable="false"
              class="error-message"
            >
              {{ errorMessage }}
            </Message>

            <Button
              type="submit"
              label="Sign In"
              :loading="isLoading"
              class="login-button"
              :disabled="!credentials.username || !credentials.password"
            />
          </form>

          <!-- <div class="demo-info">
            <div class="demo-credentials">
              <h4>Demo Credentials</h4>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> admin123</p>
            </div>
          </div> -->
        </template>
      </Card>
    </div>
  </div>
</template>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: var(--p-surface-ground);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--p-primary-color), var(--p-primary-600));
  opacity: 0.1;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.theme-controls {
  position: absolute;
  top: 2rem;
  right: 2rem;
  display: flex;
  gap: 1rem;
  z-index: 10;
}

.theme-dropdown {
  min-width: 120px;
}

.theme-toggle {
  width: 2.5rem;
  height: 2.5rem;
}

.login-container {
  position: relative;
  z-index: 5;
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

.login-card {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--p-surface-border);
}

.login-header {
  text-align: center;
  padding: 2rem 2rem 1rem;
}

.logo {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: var(--p-primary-color);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.login-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.login-header p {
  margin: 0;
  color: var(--p-text-muted-color);
}

.login-form {
  padding: 0 2rem 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.form-group :deep(.p-inputtext),
.form-group :deep(.p-password) {
  width: 100%;
}

.error-message {
  margin-bottom: 1.5rem;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  font-weight: 600;
}

.demo-info {
  border-top: 1px solid var(--p-surface-border);
  padding-top: 1.5rem;
}

.demo-credentials {
  background: var(--p-surface-50);
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid var(--p-primary-color);
}

:global(.dark) .demo-credentials {
  background: var(--p-surface-800);
}

.demo-credentials h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--p-primary-color);
}

.demo-credentials p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }
  
  .theme-controls {
    top: 1rem;
    right: 1rem;
    flex-direction: column;
  }
  
  .login-header {
    padding: 1.5rem 1.5rem 1rem;
  }
  
  .login-form {
    padding: 0 1.5rem 1.5rem;
  }
}
</style>