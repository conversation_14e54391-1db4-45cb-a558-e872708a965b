<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { profileService } from '@/api/services/profileService'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'

const route = useRoute()
const profile = ref(null)
const loading = ref(true)
const error = ref(null)

const getInitials = (firstName, lastName) => {
  const first = firstName?.charAt(0) || ''
  const last = lastName?.charAt(0) || ''
  return (first + last).toUpperCase() || 'U'
}

const formatDate = (dateString) => {
  if (!dateString) return null
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long'
  })
}

onMounted(async () => {
  try {
    const slug = route.params.slug
    const response = await profileService.getPublicProfile(slug)
    profile.value = response.data
    loading.value = false
  } catch (err) {
    error.value = 'Profile not found or is not public'
    loading.value = false
  }
})
</script>

<template>
  <div class="public-profile-page">
    <div v-if="loading" class="loading-state">
      Loading profile...
    </div>
    
    <div v-else-if="error" class="error-state">
      {{ error }}
    </div>

    <div v-else-if="profile" class="profile-content">
      <!-- Profile Header -->
      <div class="profile-header-card">
        <div class="profile-avatar-section">
          <div class="avatar-container">
            <Avatar 
              :label="getInitials(profile.firstName, profile.lastName)" 
              size="xlarge" 
              class="profile-avatar"
              :style="profile.avatarUrl ? {
                backgroundImage: `url(${profile.avatarUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                color: 'transparent'
              } : {
                backgroundColor: profile.avatarColor || '#3b82f6'
              }"
            />
          </div>
          <div class="profile-basic-info">
            <h2>{{ profile.firstName }} {{ profile.lastName }}</h2>
            <p class="profile-title">{{ profile.jobTitle || 'Job Seeker' }}</p>
            <p class="profile-location" v-if="profile.location">
              <i class="pi pi-map-marker"></i>
              {{ profile.location }}
            </p>
          </div>
        </div>
      </div>

      <!-- Professional Information -->
      <div class="profile-section" v-if="profile.bio || profile.industry">
        <div class="section-header">
          <h3>About</h3>
        </div>
        <div class="info-content">
          <p v-if="profile.bio" class="bio">{{ profile.bio }}</p>
          <p v-if="profile.industry" class="industry">
            <strong>Industry:</strong> {{ profile.industry }}
          </p>
          <p v-if="profile.experienceYears || profile.experienceMonths" class="experience">
            <strong>Experience:</strong>
            {{ profile.experienceYears }} years
            {{ profile.experienceMonths ? `${profile.experienceMonths} months` : '' }}
          </p>
        </div>
      </div>

      <!-- Skills -->
      <div class="profile-section" v-if="profile.skills?.length">
        <div class="section-header">
          <h3>Skills</h3>
        </div>
        <div class="skills-container">
          <Tag v-for="skill in profile.skills" 
               :key="skill" 
               :value="skill" 
               class="skill-tag" />
        </div>
      </div>

      <!-- Work Experience -->
      <div class="profile-section" v-if="profile.workExperience?.length">
        <div class="section-header">
          <h3>Work Experience</h3>
        </div>
        <div class="experience-list">
          <div v-for="exp in profile.workExperience" 
               :key="exp.id" 
               class="experience-item">
            <div class="experience-header">
              <div class="experience-info">
                <h4>{{ exp.jobTitle }}</h4>
                <p class="company">{{ exp.company }}</p>
                <p class="duration">
                  {{ formatDate(exp.startDate) }} - 
                  {{ exp.endDate ? formatDate(exp.endDate) : 'Present' }}
                </p>
              </div>
            </div>
            <p class="experience-description">{{ exp.description }}</p>
          </div>
        </div>
      </div>

      <!-- Education -->
      <div class="profile-section" v-if="profile.education?.length">
        <div class="section-header">
          <h3>Education</h3>
        </div>
        <div class="education-list">
          <div v-for="edu in profile.education" 
               :key="edu.id" 
               class="education-item">
            <div class="education-header">
              <div class="education-info">
                <h4>{{ edu.degree }}</h4>
                <p class="school">{{ edu.school }}</p>
                <p class="duration">
                  {{ edu.startYear }} - {{ edu.endYear || 'Present' }}
                </p>
                <p v-if="edu.fieldOfStudy" class="field-of-study">
                  {{ edu.fieldOfStudy }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.public-profile-page {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.loading-state,
.error-state {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: var(--text-color-secondary);
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-header-card,
.profile-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 2rem;
}

.profile-avatar-section {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.profile-avatar {
  width: 120px !important;
  height: 120px !important;
  font-size: 3rem !important;
}

.profile-basic-info h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.profile-title {
  font-size: 1.25rem;
  color: var(--primary-color);
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.profile-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary);
  margin: 0;
}

.section-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.info-content {
  color: var(--text-color);
  line-height: 1.6;
}

.bio {
  margin-bottom: 1.5rem;
}

.skills-container {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.skill-tag {
  font-size: 0.85rem !important;
  padding: 0.5rem 1rem !important;
}

.experience-list,
.education-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.experience-item,
.education-item {
  padding: 1.5rem;
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  background: var(--surface-50);
}

.experience-info h4,
.education-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 600;
}

.company,
.school {
  margin: 0 0 0.25rem 0;
  color: var(--primary-color);
  font-weight: 500;
}

.duration,
.field-of-study {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.experience-description {
  margin: 1rem 0 0 0;
  color: var(--text-color-secondary);
  line-height: 1.5;
}

@media (max-width: 768px) {
  .profile-avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-avatar {
    width: 100px !important;
    height: 100px !important;
    font-size: 2rem !important;
  }

  .profile-basic-info h2 {
    font-size: 1.5rem;
  }

  .profile-title {
    font-size: 1rem;
  }

  .profile-location {
    justify-content: center;
  }
}
</style> 