export enum JobType {
  FULL_TIME = 'Full Time',
  PART_TIME = 'Part Time',
  CONTRACT = 'Contract',
  TEMPORARY = 'Temporary',
  INTERNSHIP = 'Internship'
}

export enum PaymentType {
  MONTHLY = 'Monthly',
  HOURLY = 'Hourly',
  DAILY = 'Daily',
  WEEKLY = 'Weekly',
  PROJECT_BASED = 'Project Based'
}

export enum JobStatus {
  DRAFT = 'Draft',
  PUBLISHED = 'Published',
  CLOSED = 'Closed',
  EXPIRED = 'Expired'
}

export enum JobUrgency {
  URGENT = 'Urgent',
  FLEXIBLE = 'Flexible',
  NORMAL = 'Normal'
}

export enum ExperienceLevel {
  FRESHER = 'Fresher',
  JUNIOR = 'Junior',
  MID_LEVEL = 'Mid Level',
  SENIOR = 'Senior',
  EXPERT = 'Expert'
}

export enum ContactDisplayType {
  NONE = 'None',
  PHONE = 'Phone',
  EMAIL = 'Email',
  BOTH = 'Both'
} 