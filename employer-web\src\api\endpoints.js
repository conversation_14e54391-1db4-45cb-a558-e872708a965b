// API Endpoints Configuration
export const ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    SEND_OTP: '/auth/login/email/otp',
    VERIFY_OTP: '/auth/login/email/verify',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh-token',
    PROFILE: '/auth/profile'
  },

  // Jobs
  JOBS: {
    LIST: '/jobs',
    DETAILS: (id) => `/jobs/${id}`,
    CREATE: '/jobs',
    UPDATE: (id) => `/jobs/${id}`,
    DELETE: (id) => `/jobs/${id}`,
    INDUSTRIES: '/industries',
    STATS: '/jobs/stats',
    SEARCH: '/jobs/search',
    FEATURED: '/jobs/featured',
    RECENT: '/jobs/recent',
    ENUMS: '/jobs/job-enums', // New job enums endpoint
    MY_JOBS: '/jobs/my-jobs' // Endpoint for employer's own jobs
  },

  // Applications
  APPLICATIONS: {
    LIST: '/applications',
    DETAILS: (id) => `/applications/${id}`,
    CREATE: '/applications',
    UPDATE: (id) => `/applications/${id}`,
    DELETE: (id) => `/applications/${id}`,
    BY_JOB: (jobId) => `/jobs/${jobId}/applications`,
    BY_USER: '/applications/user',
    WITHDRAW: (id) => `/applications/${id}/withdraw`,
    JOB_APPLICANTS: '/job-applications/job-applicants' // New endpoint for job applicants
  },

  // User Profile
  PROFILE: {
    GET: '/profile',
    UPDATE: '/profile',
    UPLOAD_AVATAR: '/profile/avatar',
    DELETE_AVATAR: '/profile/avatar',
    PRIVACY_SETTINGS: '/profile/privacy',
    APPLICANT_PROFILE: (id) => `/profile/${id}`
  },

  // Reference Data
  REFERENCE: {
    DEPARTMENTS: '/reference/departments',
    JOB_STATUSES: '/reference/job-statuses',
    JOB_TYPES: '/reference/job-types',
    EXPERIENCE_LEVELS: '/reference/experience-levels',
    SKILLS: '/reference/skills',
    LOCATIONS: '/reference/locations'
  },

  // Dashboard - Job Seeker specific endpoint
  DASHBOARD: {
    STATS: '/jobs/employer/dashboard',
    RECENT_ACTIVITY: '/jobs/employer/recent-activity',
    RECOMMENDATIONS: '/jobs/employer/recommendations'
  },

  // Companies
  COMPANIES: {
    LIST: '/companies',
    DETAILS: (id) => `/companies/${id}`,
    JOBS: (id) => `/companies/${id}/jobs`
  },

  // Notifications
  NOTIFICATIONS: {
    LIST: '/notifications',
    MARK_READ: (id) => `/notifications/${id}/read`,
    MARK_ALL_READ: '/notifications/mark-all-read',
    SETTINGS: '/notifications/settings'
  },

  // File Upload
  UPLOAD: {
    RESUME: '/upload/resume',
    AVATAR: '/upload/avatar',
    DOCUMENTS: '/upload/documents'
  }
}