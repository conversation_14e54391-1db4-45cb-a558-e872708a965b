import { <PERSON><PERSON><PERSON>, OnModuleInit, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UsersController } from './controllers/users.controller';
import { UsersService } from './services/users.service';
import { UserEntity } from './entities/user.entity';
import { ProfileEntity } from './entities/profile.entity';
import { AdminActivityEntity } from './entities/admin-activity.entity';
import { EmailModule } from '../email/email.module';
import { SmsModule } from '../sms/sms.module';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionService } from './subscription/subscription.service';
import { AdminService } from './services/admin.service';
import { AdminController } from './controllers/admin.controller';
import { ActivityService } from './services/activity.service';
import { ActivityController } from './controllers/activity.controller';
import { SubscriptionEntity } from './subscription/subscription.entity';
import { SubscriptionController } from './subscription/subscription.controller';
import { BusinessDetailsEntity } from './entities/business-details.entity';
import { BusinessDetailsService } from './services/business-details.service';
import { BusinessDetailsController } from './controllers/business-details.controller';
import { ProfileController } from './controllers/profile.controller';
import { ProfileService } from './services/profile.service';
import { VerificationEntity } from './entities/verification.entity';
import { VerificationController } from './controllers/verification.controller';
import { VerificationService } from './services/verification.service';
import { AdminActivityService } from './services/admin-activity.service';
import { FileUploadService } from '../common/services/file-upload.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      ProfileEntity,
      VerificationEntity,
      SubscriptionEntity,
      AdminActivityEntity,
      BusinessDetailsEntity,
    ]),
    EmailModule,
    SmsModule,
    forwardRef(() => AuthModule),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1d'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [
    UsersController,
    ProfileController,
    VerificationController,
    SubscriptionController,
    AdminController,
    ActivityController,
    BusinessDetailsController,
  ],
  providers: [
    UsersService,
    ProfileService,
    VerificationService,
    SubscriptionService,
    AdminService,
    ActivityService,
    BusinessDetailsService,
    AdminActivityService,
    FileUploadService,
  ],
  exports: [
    UsersService,
    ProfileService,
    VerificationService,
    SubscriptionService,
    AdminService,
    ActivityService,
    BusinessDetailsService,
    AdminActivityService,
    FileUploadService,
  ],
})
export class UsersModule implements OnModuleInit {
  constructor(private readonly usersService: UsersService) {}

  async onModuleInit() {
    await this.usersService.ensureDefaultUsersExist();
  }
}
