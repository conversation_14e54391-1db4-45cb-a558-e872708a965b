import httpClient from '../httpClient';
import { ENDPOINTS } from '../endpoints';

export const industriesService = {
  // Get all job enums - using mock for now
  async getIndustries() {
    try {
      const response = await httpClient.get(ENDPOINTS.INDUSTRIES.GET)
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch industries')
    }
  },

  async create(reqBody) {
     try {
      const response = await httpClient.post(ENDPOINTS.INDUSTRIES.POST, reqBody)
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch industries')
    }
  },

  async update(id, reqBody) {
     try {
      const response = await httpClient.patch(ENDPOINTS.INDUSTRIES.UPDATE(id), reqBody)
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch industries')
    }
  },

  async createCategory(reqBody) {
     try {
      const response = await httpClient.post(ENDPOINTS.INDUSTRIES.POST_CATEGORY, reqBody)
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch industries')
    }
  },

  async updateCategory(id, reqBody) {
    try {
     const response = await httpClient.patch(ENDPOINTS.INDUSTRIES.UPDATE_CATEGORY(id), reqBody)
     return response;
   } catch (error) {
     throw new Error(error.message || 'Failed to fetch industries')
   }
 },
}