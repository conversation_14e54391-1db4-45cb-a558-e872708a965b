import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const authService = {
  // Send OTP to email - using separate mock file
  async sendOtp(email) {
    try {
      const response = await httpClient.post(ENDPOINTS.AUTH.SEND_OTP, {email})

      // handle failure response
      return response;
    } catch (error) {
      return error
    }
  },

  // Verify OTP - using separate mock file
  async verifyOtp(email, otp) {
    try {
      const response = await httpClient.post(ENDPOINTS.AUTH.VERIFY_OTP, {email, otp})
      
      // Handle failure responses
      // if (!response.success) {
      //   const error = new Error(response.errorMessage || 'OTP verification failed')
      //   error.code = response.errorCode
      //   error.status = response.status
      //   throw error
      // }
      
      // // Handle success response
      // return {
      //   success: true,
      //   message: 'OTP verified successfully',
      //   token: response.data.access_token,
      //   refreshToken: response.data.refresh_token,
      //   user: response.data.user
      // }

      return response;
    } catch (error) {
      throw error
    }
  },

  async getProfile() {
    try {
      const res = await httpClient.get(ENDPOINTS.AUTH.PROFILE);

      return res;
    } catch (error) {
      return error
    }
  },

  // Logout (placeholder for future implementation)
  async logout() {
    try {
      // TODO: Implement with separate mock file
      const res = await httpClient.post(ENDPOINTS.AUTH.LOGOUT);

      return res
    } catch (error) {
      console.warn('Logout API call failed:', error)
      
    }
  },

  // Refresh token (placeholder for future implementation)
  async refreshToken(token) {
    try {
      // TODO: Implement with separate mock file
      throw new Error('Token refresh not implemented yet')
    } catch (error) {
      throw new Error(error.message || 'Token refresh failed')
    }
  },
}
