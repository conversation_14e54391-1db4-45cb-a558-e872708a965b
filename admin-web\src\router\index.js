import { createRouter, createWebHistory } from 'vue-router';

import { useAuthStore } from '@/stores/auth'

// Lazy load components for better performance
const DashboardView = () => import('../views/DashboardView.vue');
const UsersView = () => import('../views/UsersView.vue');
const IndustriesView = () => import('../views/IndustriesView.vue');
const JobsView = () => import('../views/JobsView.vue');
const FeedbackView = () => import('../views/FeedbackView.vue');
const WelcomePageView = () => import('../views/WelcomePageView.vue');
const ErrorTrackingView = () => import('../views/ErrorTrackingView.vue');
const AnalyticsView = () => import('../views/AnalyticsView.vue');
const SettingsView = () => import('../views/SettingsView.vue');
const FilesView = () => import('../views/FilesView.vue');
const MessagesView = () => import('../views/MessagesView.vue');
const LoginPage = () => import('../views/LoginPage.vue')
const RootLayout = () => import('../views/RootLayout.vue')

const routes = [
  {
    path: '/jdadmin/login',
    component: LoginPage,
    meta: {
      title: 'Login',
      app: 'Admin'
    },
  },
  {
    path: '/jdadmin',
    component: RootLayout,
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: DashboardView,
        meta: {
          requiresAuth: true,
          title: 'Dashboard',
          app: 'Admin'
        },
      },
      {
        path: 'users',
        name: 'Users',
        component: UsersView,
        meta: {
          requiresAuth: true,
          title: 'Users',
          app: 'Admin'
        },
      },
      {
        path: 'industries',
        name: 'Industries',
        component: IndustriesView,
        meta: {
          requiresAuth: true,
          title: 'Industries',
          app: 'Admin'
        },
      },
      {
        path: 'jobs',
        name: 'Jobs',
        component: JobsView,
        meta: {
          requiresAuth: true,
          title: 'Jobs',
          app: 'Admin'
        },
      },
      {
        path: 'feedback',
        name: 'Feedback',
        component: FeedbackView,
        meta: {
          requiresAuth: true,
          title: 'Feedback',
          app: 'Admin'
        },
      },
      {
        path: 'welcome-page',
        name: 'WelcomePage',
        component: WelcomePageView,
        meta: {
          requiresAuth: true,
          title: 'Welcome Page',
          app: 'Admin'
        },
      },
      {
        path: 'errors',
        name: 'ErrorTracking',
        component: ErrorTrackingView,
        meta: {
          requiresAuth: true,
          title: 'Error Tracking',
          app: 'Admin'
        },
      },
      {
        path: 'analytics',
        name: 'Analytics',
        component: AnalyticsView,
        meta: {
          requiresAuth: true,
          title: 'Analytics',
          app: 'Admin'
        },
      },
      {
        path: 'settings',
        name: 'Settings',
        component: SettingsView,
        meta: {
          requiresAuth: true,
          title: 'Settings',
          app: 'Admin'
        },
      },
      {
        path: 'files',
        name: 'Files',
        component: FilesView,
        meta: {
          requiresAuth: true,
          title: 'Files',
          app: 'Admin'
        },
      },
      {
        path: 'messages',
        name: 'Messages',
        component: MessagesView,
        meta: {
          requiresAuth: true,
          title: 'Messages',
          app: 'Admin'
        },
      },
    ],
    redirect: '/jdadmin/dashboard',
  },
  {
    path: '/jdadmin/:catchAll(.*)',
    redirect: '/jdadmin/dashboard',
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (!authStore.isInitialised) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.log('Failed to get the profile')
    }
  }

  if (authStore.isAuthenticated) {
    if (!authStore.enums) {
      await authStore.getEnumData()
    }

    if (!authStore.industries) {
      await authStore.getIndustries()
    }
  }

  // Set document title based on route meta or route name
  if (to.meta?.title) {
    document.title = `${to.meta.app} - ${to.meta.title} - Job Dalal`
  } else if (to.name) {
    // Convert route name to title case
    const title = to.name
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .trim()
    document.title = `Admin - ${title} - Job Dalal`
  } else {
    document.title = 'Admin - Job Dalal'
  }

  if (!authStore.isAuthenticated && to?.path !== '/jdadmin/login') {
    next('/jdadmin/login')
  } else {
    next()
  }
})

export default router;