import {
  Controller,
  Get,
  Query,
  UseGuards,
  Post,
  Body,
  Res,
  Header,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { ActivityService, ActivityFilter } from '../services/activity.service';
import { AdminActionType } from '../enums/admin.enum';
import { Response } from 'express';

@ApiTags('Activities')
@Controller('activities')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ActivityController {
  constructor(private readonly activityService: ActivityService) {}

  @Get('user')
  @ApiOperation({ summary: 'Get user activities' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiResponse({ status: 200, description: 'List of user activities' })
  async getUserActivities(@Query('userId') userId: string, @Request() req) {
    return this.activityService.getUserActivities(userId || req.user.id);
  }

  @Get('admin')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get admin activities (Admin only)' })
  @ApiQuery({ name: 'adminId', required: false })
  @ApiResponse({ status: 200, description: 'List of admin activities' })
  async getAdminActivities(@Query('adminId') adminId: string) {
    return this.activityService.getAdminActivities(adminId);
  }

  @Get('types')
  @ApiOperation({ summary: 'Get all activity types' })
  @ApiResponse({ status: 200, description: 'List of activity types' })
  async getActivityTypes() {
    return this.activityService.getActivityTypes();
  }

  @Get('recent')
  async getRecentActivities(@Query('limit') limit?: number) {
    return this.activityService.getRecentActivities(limit ? Number(limit) : undefined);
  }

  @Post('search')
  async searchActivities(
    @Body() filter: ActivityFilter,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.activityService.searchActivities(
      filter,
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined,
    );
  }

  @Get('analytics')
  async getActivityAnalytics(@Query('days') days?: number) {
    return this.activityService.getActivityAnalytics(days ? Number(days) : undefined);
  }

  @Get('trends')
  async getActivityTrends(@Query('days') days?: number) {
    return this.activityService.getActivityTrends(days ? Number(days) : undefined);
  }

  @Post('export')
  @Header('Content-Type', 'application/octet-stream')
  async exportActivities(
    @Body() filter: ActivityFilter,
    @Query('format') format: 'csv' | 'excel' = 'csv',
    @Res() res: Response,
  ) {
    const exportData = await this.activityService.exportActivities(filter, format);

    res.setHeader('Content-Disposition', `attachment; filename=${exportData.filename}`);
    return res.send(exportData.data);
  }

  @Get('visualization')
  async getVisualizationData(@Query('days') days?: number) {
    return this.activityService.getVisualizationData(days ? Number(days) : undefined);
  }
}
