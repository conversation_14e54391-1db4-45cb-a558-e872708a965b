import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WelcomeModule } from './welcome.module';
import { WelcomeEntity } from './entities/welcome.entity';
import { UserRole } from '../users/enums/user.enum';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';

describe('Welcome Integration Tests', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [WelcomeEntity],
          synchronize: true,
        }),
        WelcomeModule,
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: (context) => {
          const request = context.switchToHttp().getRequest();
          request.user = {
            id: 'admin-id',
            role: UserRole.ADMIN,
          };
          return true;
        },
      })
      .overrideGuard(RolesGuard)
      .useValue({
        canActivate: (context) => {
          const request = context.switchToHttp().getRequest();
          return [UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(request.user.role);
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /welcome', () => {
    it('should create a welcome message', () => {
      const createDto = {
        message: 'Welcome message',
        imageUrl: 'https://example.com/image.jpg',
      };

      return request(app.getHttpServer())
        .post('/welcome')
        .send(createDto)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.message).toBe(createDto.message);
          expect(res.body.imageUrl).toBe(createDto.imageUrl);
          expect(res.body.updatedBy).toBe('admin-id');
        });
    });
  });

  describe('GET /welcome', () => {
    it('should return the welcome message', () => {
      return request(app.getHttpServer())
        .get('/welcome')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('message');
          expect(res.body).toHaveProperty('imageUrl');
          expect(res.body).toHaveProperty('updatedBy');
        });
    });
  });

  describe('PATCH /welcome', () => {
    it('should update the welcome message', () => {
      const updateDto = {
        message: 'Updated welcome message',
      };

      return request(app.getHttpServer())
        .patch('/welcome')
        .send(updateDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.message).toBe(updateDto.message);
          expect(res.body.updatedBy).toBe('admin-id');
        });
    });
  });

  describe('Error cases', () => {
    it('should return 403 when non-admin tries to create welcome message', async () => {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [
          TypeOrmModule.forRoot({
            type: 'sqlite',
            database: ':memory:',
            entities: [WelcomeEntity],
            synchronize: true,
          }),
          WelcomeModule,
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({
          canActivate: (context) => {
            const request = context.switchToHttp().getRequest();
            request.user = {
              id: 'user-id',
              role: UserRole.JOB_SEEKER,
            };
            return true;
          },
        })
        .compile();

      const testApp = moduleFixture.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .post('/welcome')
        .send({ message: 'Welcome message' })
        .expect(403);

      await testApp.close();
    });

    it('should return 401 when unauthenticated user tries to access protected endpoints', async () => {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [
          TypeOrmModule.forRoot({
            type: 'sqlite',
            database: ':memory:',
            entities: [WelcomeEntity],
            synchronize: true,
          }),
          WelcomeModule,
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({
          canActivate: () => false,
        })
        .compile();

      const testApp = moduleFixture.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .post('/welcome')
        .send({ message: 'Welcome message' })
        .expect(401);

      await testApp.close();
    });
  });
});
