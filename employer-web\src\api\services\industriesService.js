import { industriesMock } from '../mocks/industriesMock'

export const industriesService = {
  // Get all job enums - using mock for now
  async getIndustries() {
    try {
      const response = await industriesMock.getIndustries()
      return {
        success: response.success,
        data: response.data,
        status: 200,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch industries')
    }
  }
}