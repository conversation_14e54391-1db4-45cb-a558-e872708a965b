<template>
  <div v-for="count in counts" class="p-4 border border-gray-300 rounded-xl shadow-sm w-full mb-4 space-y-3">
    <!-- Header: Title + Tags + Icon -->
    <div class="flex justify-between items-start">
      <div class="space-y-2">
        <Skeleton width="80%" height="1rem" />
        <div class="flex space-x-2">
          <Skeleton width="3rem" height="1rem" borderRadius="9999px" />
          <Skeleton width="3rem" height="1rem" borderRadius="9999px" />
          <Skeleton width="3rem" height="1rem" borderRadius="9999px" />
        </div>
      </div>
      <Skeleton shape="circle" size="1.5rem" />
    </div>

    <!-- User Info -->
    <Skeleton width="6rem" height="0.75rem" />

    <!-- Location, Positions, Salary, Date -->
    <div class="space-y-2">
      <div class="flex items-center space-x-2">
        <Skeleton shape="circle" size="1rem" />
        <Skeleton width="5rem" height="0.75rem" />
      </div>
      <div class="flex items-center space-x-2">
        <Skeleton shape="circle" size="1rem" />
        <Skeleton width="6rem" height="0.75rem" />
      </div>
      <div class="flex items-center space-x-2">
        <Skeleton shape="circle" size="1rem" />
        <Skeleton width="7rem" height="0.75rem" />
      </div>
      <div class="flex items-center space-x-2">
        <Skeleton shape="circle" size="1rem" />
        <Skeleton width="6rem" height="0.75rem" />
      </div>
    </div>

    <!-- Facilities -->
    <div class="flex space-x-4 mt-2">
      <Skeleton width="6rem" height="0.75rem" />
      <Skeleton width="6rem" height="0.75rem" />
    </div>

    <!-- Footer: Share + View Button -->
    <div class="flex justify-between items-center">
      <Skeleton shape="circle" size="1.5rem" />
      <Skeleton width="8rem" height="2rem" borderRadius="0.5rem" />
    </div>
  </div>
</template>

<script setup>
import Skeleton from 'primevue/skeleton';
const counts = new Array(15);
</script>
