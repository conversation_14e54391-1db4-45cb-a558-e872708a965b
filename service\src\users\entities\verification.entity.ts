import { <PERSON>ti<PERSON>, Column, PrimaryGeneratedC<PERSON>umn, OneToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { UserEntity } from './user.entity';

@Entity('verifications')
export class VerificationEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  aadharNumber: string;

  @Column({ nullable: true })
  aadharFrontImage: string;

  @Column({ nullable: true })
  aadharBackImage: string;

  @Column({ nullable: true })
  panNumber: string;

  @Column({ nullable: true })
  panImage: string;

  @Column({ default: false })
  isAadharVerified: boolean;

  @Column({ default: false })
  isPanVerified: boolean;

  @Column({ type: 'timestamp', nullable: true })
  verifiedAt: Date;

  @Column({ nullable: true })
  verifiedBy: string;

  @Column()
  userId: string;

  @OneToOne(() => UserEntity, (user) => user.verification)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}
