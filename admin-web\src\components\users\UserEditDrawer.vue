<script setup>
import { reactive, ref, watch } from 'vue';
import Drawer from 'primevue/drawer';
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
import Password from 'primevue/password';
import Checkbox from 'primevue/checkbox';
import Button from 'primevue/button';
import { usersService } from '@/api/services/usersService';
import { useGlobalToast } from '@/composables/useGlobalToast';

const props = defineProps({
  visible: Boolean,
  user: Object
});

const emit = defineEmits(['close', 'success']);

const loading = ref(false);
const error = ref('');
const fieldErrors = ref({});

// Use `reactive` for the form object for robust reactivity
const form = reactive({
  email: '',
  password: '',
  firstName: '',
  middleName: '',
  lastName: '',
  role: 'job_seeker',
  subscriptionType: 'default',
  isEmailVerified: false,
  isPhoneVerified: false,
  isAadharVerified: false,
  isProfileComplete: false,
  isBlocked: false
});

const roleOptions = [
  { label: 'Super Admin', id: 'super_admin' },
  { label: 'Admin', id: 'admin' },
  { label: 'Job Seeker', id: 'job_seeker' },
  { label: 'Employer', id: 'employer' },
  { label: 'Agent', id: 'agent' }
];

const subscriptionOptions = [
  { label: 'Default', id: 'default' },
  { label: 'Pro', id: 'pro' },
  { label: 'Premium', id: 'premium' }
];

const { showToast } = useGlobalToast();

// Watch for changes in the `user` prop and populate the form
watch(() => props.user, (currentUser) => {
  if (currentUser) {
    // Manually map fields to be explicit and handle any casing issues.
    form.email = currentUser.email || '';
    form.firstName = currentUser.firstName || '';
    form.middleName = currentUser.middleName || '';
    form.lastName = currentUser.lastName || '';
    
    // Ensure incoming role/subscription values are lowercase to match dropdown IDs
    form.role = currentUser.role ? currentUser.role.toLowerCase() : 'job_seeker';
    form.subscriptionType = currentUser.subscriptionType ? currentUser.subscriptionType.toLowerCase() : 'default';
    
    form.isEmailVerified = !!currentUser.isEmailVerified;
    form.isPhoneVerified = !!currentUser.isPhoneVerified;
    form.isAadharVerified = !!currentUser.isAadharVerified;
    form.isProfileComplete = !!currentUser.isProfileComplete;
    form.isBlocked = !!currentUser.isBlocked;

    form.password = ''; // Always clear password field for security
    error.value = '';   // Clear any previous errors
  }
}, {
  immediate: true,
  deep: true
});

const isEmailValid = () => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email);

const validateForm = () => {
  const errors = {};
  if (!form.email.trim()) {
    errors.email = 'This field is required';
  } else if (!isEmailValid()) {
    errors.email = 'Invalid email address';
  }
  if (!form.firstName.trim()) {
    errors.firstName = 'This field is required';
  }
  if (!form.lastName.trim()) {
    errors.lastName = 'This field is required';
  }
  if (form.password && form.password.length < 6) {
    errors.password = 'Password must be at least 6 characters';
  }
  fieldErrors.value = errors;
  return Object.keys(errors).length === 0;
};

// Handle form submission
async function handleSubmit() {
  if (!props.user) {
    error.value = 'No user selected for editing.';
    return;
  }
  error.value = '';
  if (!validateForm()) {
    return;
  }
  loading.value = true;
  try {
    const payload = { ...form };
    if (!payload.password) {
      delete payload.password;
    }
    await usersService.updateUser(props.user.id, payload);
    emit('success');
    showToast({ severity: 'success', summary: 'Success', detail: 'User updated successfully.', life: 3000 });
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Failed to update user.';
    showToast({ severity: 'error', summary: 'Error', detail: error.value, life: 3000 });
  } finally {
    loading.value = false;
  }
}

// Handle closing the drawer
function handleClose() {
    emit('close'); // Emit close event to the parent
}
</script>

<template>
  <Drawer :visible="visible" position="right" class="user-drawer" :style="{ width: '500px' }" @update:visible="handleClose">
    <template #header>
      <h3>Edit User</h3>
    </template>
    <div class="drawer-content">
      <form @submit.prevent="handleSubmit">
        <div class="form-group">
          <label>Email *</label>
          <InputText v-model="form.email" type="email" class="w-full" required />
          <div v-if="fieldErrors.email" class="field-error">{{ fieldErrors.email }}</div>
        </div>
        <div class="form-group">
          <label>Password (leave blank to keep unchanged)</label>
          <Password v-model="form.password" class="w-full" toggle-mask />
          <div v-if="fieldErrors.password" class="field-error">{{ fieldErrors.password }}</div>
        </div>
        <div class="form-group">
          <label>First Name *</label>
          <InputText v-model="form.firstName" class="w-full" required />
          <div v-if="fieldErrors.firstName" class="field-error">{{ fieldErrors.firstName }}</div>
        </div>
        <div class="form-group">
          <label>Middle Name</label>
          <InputText v-model="form.middleName" class="w-full" />
        </div>
        <div class="form-group">
          <label>Last Name *</label>
          <InputText v-model="form.lastName" class="w-full" required />
          <div v-if="fieldErrors.lastName" class="field-error">{{ fieldErrors.lastName }}</div>
        </div>
        <div class="form-group">
          <label>Role</label>
          <Select v-model="form.role" :options="roleOptions" option-label="label" option-value="id" class="w-full" />
        </div>
        <div class="form-group">
          <label>Subscription Type</label>
          <Select v-model="form.subscriptionType" :options="subscriptionOptions" option-label="label" option-value="id" class="w-full" />
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isEmailVerified" :binary="true" /> Email Verified
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isPhoneVerified" :binary="true" /> Phone Verified
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isAadharVerified" :binary="true" /> Aadhar Verified
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isProfileComplete" :binary="true" /> Profile Complete
        </div>
        <div class="form-group">
          <Checkbox v-model="form.isBlocked" :binary="true" /> Block User
        </div>
        <div v-if="error" class="error-message p-error">{{ error }}</div>
        <div class="drawer-footer">
          <Button label="Cancel" severity="secondary" @click="handleClose" :disabled="loading" />
          <Button label="Update" type="submit" :loading="loading" />
        </div>
      </form>
    </div>
  </Drawer>
</template>

<style scoped>
.form-group { margin-bottom: 1rem; }
.drawer-footer { display: flex; justify-content: flex-end; gap: 1rem; margin-top: 1.5rem; }
.error-message { margin-top: 1rem; }
.field-error { color: #e24c4b; font-size: 0.92em; margin-top: 0.25rem; }
</style> 