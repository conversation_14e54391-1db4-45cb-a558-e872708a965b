<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import WelcomeFilterDrawer from './WelcomeFilterDrawer.vue'
import WelcomeEditDrawer from './WelcomeEditDrawer.vue'
import WelcomeContent from './WelcomeContent.vue'
import ConfirmDialog from 'primevue/confirmdialog'
import { useConfirm } from 'primevue/useconfirm'
import SpeedDial from 'primevue/speeddial'
import Dialog from 'primevue/dialog'
import Button from 'primevue/button'
import Toast from 'primevue/toast'
import { useToast } from 'primevue/usetoast'
import { fetchWelcomeList, createWelcome, updateWelcome, deleteWelcome } from '@/api/services/welcomeService'

const confirm = useConfirm()
const toast = useToast()

// State: welcome pages
const welcomePages = ref([])
const loading = ref(false)
const error = ref(null)

// Dialog states
const showWelcomeDialog = ref(false)
const isEditMode = ref(false)
const selectedWelcome = ref(null)
const isFilterDrawerVisible = ref(false)
const isEditDrawerVisible = ref(false)
const isCreateDrawerVisible = ref(false)

// Form data
const welcomeForm = reactive({
  cmp_name: '',
  logo_path: '',
  show_signup: false,
  show_login: false,
  welcome_pop_msg: '',
  base_url: '',
  notification_url: '',
  user_url: '',
  message: '',
  imageUrl: '',
  descriptions: [],
  audience_messages: [],
  testimonials: []
})

// Search and filters
const searchTerm = ref('')
const showActiveOnly = ref(null)

// Filter state
const filterState = reactive({})

// Fetch welcome list
const loadWelcomePages = async (filters = {}) => {
  loading.value = true
  error.value = null
  try {
    const res = await fetchWelcomeList(filters)
    if (res.success) {
      if (Array.isArray(res.data)) {
        welcomePages.value = res.data
      } else if (res.data && typeof res.data === 'object') {
        welcomePages.value = [res.data]
      } else {
        welcomePages.value = []
      }
    } else {
      welcomePages.value = []
      error.value = res.message || 'Failed to load welcome pages.'
      toast.add({ severity: 'error', summary: 'Error', detail: error.value, life: 4000 })
    }
  } catch (err) {
    error.value = err.message || 'Failed to load welcome pages.'
    toast.add({ severity: 'error', summary: 'Error', detail: error.value, life: 4000 })
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadWelcomePages()
})

// Computed properties
const filteredWelcomePages = computed(() => {
  let filtered = welcomePages.value
  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(page => 
      page.cmp_name.toLowerCase().includes(search) ||
      page.message.toLowerCase().includes(search) ||
      page.base_url?.toLowerCase().includes(search)
    )
  }
  if (showActiveOnly.value !== null) {
    filtered = filtered.filter(page => page.isActive === showActiveOnly.value)
  }
  return filtered
})

const welcomeStats = computed(() => {
  const total = welcomePages.value.length
  const active = welcomePages.value.filter(p => p.isActive).length
  const withSignup = welcomePages.value.filter(p => p.show_signup).length
  const withLogin = welcomePages.value.filter(p => p.show_login).length
  return {
    total,
    active,
    inactive: total - active,
    withSignup,
    withLogin
  }
})

// Methods
const openWelcomeDialog = (welcomePage = null) => {
  isEditMode.value = !!welcomePage
  if (welcomePage) {
    Object.keys(welcomeForm).forEach(key => {
      if (welcomePage[key] !== undefined) {
        if (Array.isArray(welcomePage[key])) {
          welcomeForm[key] = [...welcomePage[key]]
        } else {
          welcomeForm[key] = welcomePage[key]
        }
      }
    })
    selectedWelcome.value = welcomePage
  } else {
    resetWelcomeForm()
  }
  showWelcomeDialog.value = true
}

const resetWelcomeForm = () => {
  Object.assign(welcomeForm, {
    cmp_name: '',
    logo_path: '',
    show_signup: false,
    show_login: false,
    welcome_pop_msg: '',
    base_url: '',
    notification_url: '',
    user_url: '',
    message: '',
    imageUrl: '',
    descriptions: [],
    audience_messages: [],
    testimonials: []
  })
  selectedWelcome.value = null
}

const saveWelcomePage = () => {
  if (!welcomeForm.cmp_name.trim() || !welcomeForm.message.trim()) return
  if (isEditMode.value && selectedWelcome.value) {
    // Update existing welcome page
    const index = welcomePages.value.findIndex(p => p.id === selectedWelcome.value.id)
    if (index !== -1) {
      welcomePages.value[index] = {
        ...selectedWelcome.value,
        ...JSON.parse(JSON.stringify(welcomeForm)),
        updatedAt: new Date()
      }
    }
  } else {
    // Add new welcome page
    welcomePages.value.push({
      ...JSON.parse(JSON.stringify(welcomeForm)),
      id: Date.now().toString(),
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    })
  }
  showWelcomeDialog.value = false
  resetWelcomeForm()
}

const deleteWelcomePage = (welcomePage) => {
  confirm.require({
    message: `Are you sure you want to delete ${welcomePage.cmp_name}?`,
    header: 'Delete Confirmation',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      try {
        await deleteWelcome(welcomePage.id)
        toast.add({ severity: 'success', summary: 'Deleted', detail: 'Welcome entry deleted.', life: 3000 })
        loadWelcomePages(filterState)
      } catch (err) {
        toast.add({ severity: 'error', summary: 'Error', detail: err.message || 'Delete failed.', life: 4000 })
      }
    }
  })
}

const toggleWelcomePage = (welcomePage) => {
  const page = welcomePages.value.find(p => p.id === welcomePage.id)
  if (page) {
    page.isActive = !page.isActive
    page.updatedAt = new Date()
  }
}

const clearFilters = () => {
  searchTerm.value = ''
  showActiveOnly.value = null
}

const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString()
}

const exportData = () => {
  // Simple CSV export for demo
  const rows = [
    ['Company', 'Message', 'Active', 'Signup', 'Login', 'Updated'],
    ...welcomePages.value.map(p => [
      p.cmp_name,
      p.message,
      p.isActive ? 'Yes' : 'No',
      p.show_signup ? 'Yes' : 'No',
      p.show_login ? 'Yes' : 'No',
      formatDate(p.updatedAt)
    ])
  ]
  const csv = rows.map(r => r.map(x => '"' + String(x).replace(/"/g, '""') + '"').join(',')).join('\n')
  const blob = new Blob([csv], { type: 'text/csv' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'welcome-pages.csv'
  a.click()
  URL.revokeObjectURL(url)
}

// Table actions
const handleEdit = (record) => {
  selectedWelcome.value = record
  isEditDrawerVisible.value = true
}

// Drawer actions
const handleCreate = async (data) => {
  loading.value = true
  try {
    await createWelcome(data)
    toast.add({ severity: 'success', summary: 'Created', detail: 'Welcome entry created.', life: 3000 })
    isCreateDrawerVisible.value = false
    loadWelcomePages(filterState)
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: err.message || 'Create failed.', life: 4000 })
  } finally {
    loading.value = false
  }
}
const handleUpdate = async (data) => {
  loading.value = true
  try {
    await updateWelcome(data.id, data)
    toast.add({ severity: 'success', summary: 'Updated', detail: 'Welcome entry updated.', life: 3000 })
    isEditDrawerVisible.value = false
    loadWelcomePages(filterState)
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: err.message || 'Update failed.', life: 4000 })
  } finally {
    loading.value = false
  }
}
const handleFilterApply = (filters) => {
  Object.assign(filterState, filters)
  loadWelcomePages(filterState)
  isFilterDrawerVisible.value = false
}
const handleFilterReset = () => {
  Object.keys(filterState).forEach(k => delete filterState[k])
  loadWelcomePages()
  isFilterDrawerVisible.value = false
}

// SpeedDial actions
const speedDialActions = [
  {
    label: 'Create',
    icon: 'pi pi-plus',
    command: () => { isCreateDrawerVisible.value = true }
  },
  {
    label: 'Filter',
    icon: 'pi pi-filter',
    command: () => { isFilterDrawerVisible.value = true }
  }
]
</script>

<template>
  <div class="welcome-view-layout">
    <Toast />
    <div class="main">
      <WelcomeContent
        :filteredWelcomePages="welcomePages"
        :formatDate="formatDate"
        :loading="loading"
        @edit="handleEdit"
        @delete="deleteWelcomePage"
        @toggle="toggleWelcomePage"
      />
    </div>
    <WelcomeCreateDrawer
      v-model="isCreateDrawerVisible"
      @submit="handleCreate"
      @close="isCreateDrawerVisible = false"
    />
    <WelcomeEditDrawer
      v-model="isEditDrawerVisible"
      :record="selectedWelcome"
      @submit="handleUpdate"
      @close="isEditDrawerVisible = false"
    />
    <WelcomeFilterDrawer
      v-model="isFilterDrawerVisible"
      :initialFilters="filterState"
      @apply="handleFilterApply"
      @reset="handleFilterReset"
      @close="isFilterDrawerVisible = false"
    />
    <ConfirmDialog />
    <SpeedDial
      :model="speedDialActions"
      direction="up"
      :style="{ position: 'fixed', right: '1.5rem', bottom: '1.2rem', zIndex: 1000 }"
      :tooltipOptions="{ position: 'left' }"
      showIcon="pi pi-bars"
      hideIcon="pi pi-times"
    />
  </div>
</template>

<style scoped>
.welcome-view-layout {
  display: flex;
  flex-direction: row;
  height: 100%;
}
.main {
  flex: 1;
  padding: 2rem 2.5rem;
  overflow-x: auto;
  background: var(--surface-ground);
}
</style> 