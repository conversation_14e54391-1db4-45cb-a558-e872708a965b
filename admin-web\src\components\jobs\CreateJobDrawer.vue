<script setup>
import { computed, defineProps, defineEmits, ref, watch } from 'vue';
import Drawer from 'primevue/drawer';
import Fieldset from 'primevue/fieldset';
import Float<PERSON>abel from 'primevue/floatlabel';
import Select from 'primevue/select';
import InputGroup from 'primevue/inputgroup';
import InputGroupAddon from 'primevue/inputgroupaddon';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';

import Stepper from 'primevue/stepper';
import StepList from 'primevue/steplist';
import StepPanels from 'primevue/steppanels';
import Step from 'primevue/step';
import StepPanel from 'primevue/steppanel';
import Editor from 'primevue/editor';
import InputNumber from 'primevue/inputnumber';
import ToggleSwitch from 'primevue/toggleswitch';
import InputChips from 'primevue/inputchips';

import { api } from '@/api'
import { useAuthStore } from '@/stores/auth';
import { onMounted } from 'vue';


const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  job: {
    type: Object,
    default: null
  }
});

const authStore = useAuthStore();
const enums = ref({
  jobTypes: [],
  paymentTypes: [],
  experienceLevels: [],
  urgencyLevels: [],
  statuses: [],
  contactDisplayTypes: []
})
const emit = defineEmits(['update:visible']);

const internalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const submitted = ref(false);
const isEditMode = computed(() => !!props.job);

const jobData = ref({});

const initJobData = () => {
  if (isEditMode.value) {
    jobData.value = { ...props.job };
    // Ensure nested objects are handled correctly, e.g., for industry
    if (props.job.industry) {
      jobData.value.industryId = props.job.industry.id;
    }
    if (props.job.employer) {
      orgUser.value = props.job.employer;
    }
  } else {
    jobData.value = {
      title: '',
      description: '',
      industryId: null,
      subIndustryId: null,
      salary: null,
      jobType: null,
      paymentType: 'MONTHLY',
      location: '',
      status: 'PENDING',
      urgency: 'FLEXIBLE',
      experienceLevel: 'FRESHER',
      benefits: [],
      requirements: [],
      responsibilities: [],
      skills: [],
      thumbnail: '',
      images: [],
      showContact: true,
      contactDisplayType: 'NONE',
      contactPhone: '',
      contactEmail: '',
      contactPerson: '',
      vacancies: 1,
      workingHours: null,
      accommodation: false,
      transportation: false,
      foodProvided: false,
      safetyEquipment: false,
      trainingProvided: false
    };
    // orgUser.value = null;
  }
  submitted.value = false;
};



onMounted(async () => {
  if (!authStore.enums) {
    await authStore.getEnumData();
  }
  if (!authStore.industries) {
    await authStore.getIndustries();
  }

  enums.value = authStore.enums;
  initJobData();
});

watch(() => props.job, initJobData);

const industries = computed(() => authStore.industries || []);
const subIndustries = computed(() => {
  if (!jobData.value.industryId || !authStore.industries) {
    return [];
  }
  const selectedIndustry = authStore.industries.find((ind) => ind.id === jobData.value.industryId);
  return selectedIndustry ? selectedIndustry.subIndustries : [];
});

watch(
  () => jobData.value.industryId,
  () => {
    jobData.value.subIndustryId = null;
  }
);

const selectedSearchType = ref()
const searchValue = ref()
const orgUser = ref()
const orgSearchOption = ref([
  { name: 'By Email', code: 'email' },
  { name: 'By Phone', code: 'phone' },
  { name: 'By ID', code: 'id' },
])

const searchOrgUser = async () => {
  if (!selectedSearchType.value || !searchValue.value) {
    return;
  }

  const searchParam = {}

  searchParam[selectedSearchType.value.code] = searchValue.value

  const user = await api.jobs.findOrgUser(searchParam)

  if (user.success && user.data) {
    orgUser.value = user.data;
  }
}

watch(orgUser, (newUser) => {
  if (newUser) {
    initJobData();
    if (newUser.company) {
      jobData.value.contactPerson = newUser.name;
      jobData.value.contactEmail = newUser.email;
    }
  }
});

const validateStep1 = () => {
  const data = jobData.value;
  const isValid =
    data.title &&
    data.description &&
    data.industryId &&
    data.subIndustryId &&
    data.salary != null &&
    data.jobType;
  return !!isValid;
};

const handleNextFrom1 = (activateCallback) => {
  submitted.value = true;
  if (validateStep1()) {
    activateCallback('2');
  }
};

const handleCreateJob = async (activateCallback) => {
  submitted.value = true;
  if (!validateStep1()) {
    activateCallback('1');
    return;
  }

  let result;
  const payload = sanitizeJobData({ ...jobData.value });

  if (isEditMode.value) {
    result = await api.jobs.updateJob(jobData.value.id, payload);
  } else {
    const finalPayload = { ...payload, userId: orgUser.value.id };
    result = await api.jobs.createJob(finalPayload);
  }

  if (result.success) {
    emit('update:visible', false);
    emit('refresh:table', true)
  } else {
    // Handle error
    console.error(`Failed to ${isEditMode.value ? 'update' : 'create'} job:`, result.error);
  }
};

const sanitizeJobData = (data) => {
  const sanitized = { ...data };

  for (const key in sanitized) {
    if (sanitized[key] === '' || sanitized[key] === null) {
      delete sanitized[key];
    }
  }

  // Ensure numeric fields are numbers if they exist
  if (sanitized.salary) {
    sanitized.salary = Number(sanitized.salary);
  }
  if (sanitized.vacancies) {
    sanitized.vacancies = Number(sanitized.vacancies);
  }
  if (sanitized.workingHours) {
    sanitized.workingHours = Number(sanitized.workingHours);
  }

  return sanitized;
};

</script>

<template>
  <Drawer v-model:visible="internalVisible" :header="isEditMode ? 'Edit Job' : 'Create Job'" position="right"
    :dismissable="false" :pt="{
      root: { class: 'w-[785px]' }
    }">

    <div v-if="!isEditMode">
      <Fieldset legend="Create Job - On behalf of organization">
        <InputGroup>
          <InputGroupAddon>
            <i class="pi pi-search"></i>
          </InputGroupAddon>
          <Select v-model="selectedSearchType" :options="orgSearchOption" optionLabel="name" class="w-full md:w-32 mr-1" />
          <InputGroupAddon>
            <i class="pi pi-user"></i>
          </InputGroupAddon>
          <InputText type="text" v-model="searchValue" class="w-full md:w-56" />
          <Button icon="pi pi-search" @click="searchOrgUser" />
        </InputGroup>

        <div v-if="orgUser" class="mt-4 p-2 border-round-sm bg-green-50 text-green-700">
          Organization User Found: {{ orgUser.email }}
        </div>
      </Fieldset>
    </div>

    <Stepper v-if="isEditMode || orgUser" value="1" class="mt-4">
      <StepList>
        <Step value="1">Details</Step>
        <Step value="2">Requirements & Benefits</Step>
        <Step value="3">Contact</Step>
      </StepList>
      <StepPanels>
        <StepPanel v-slot="{ activateCallback }" value="1">
          <div class="flex flex-col gap-6 p-fluid">
            <div>
              <FloatLabel>
                <InputText id="job_title" v-model="jobData.title" class="w-full"
                  :class="{ 'p-invalid': submitted && !jobData.title }" />
                <label for="job_title">Job Title</label>
              </FloatLabel>
              <small v-if="submitted && !jobData.title" class="p-error">Job Title is required.</small>
            </div>

            <div class="flex gap-4">
              <div class="flex-1">
                <FloatLabel>
                  <Select id="industry" v-model="jobData.industryId" :options="industries" optionLabel="name"
                    optionValue="id" class="w-full" :class="{ 'p-invalid': submitted && !jobData.industryId }" />
                  <label for="industry">Industry</label>
                </FloatLabel>
                <small v-if="submitted && !jobData.industryId" class="p-error">Industry is required.</small>
              </div>
              <div class="flex-1">
                <FloatLabel>
                  <Select id="subIndustry" v-model="jobData.subIndustryId" :options="subIndustries" optionLabel="name"
                    optionValue="id" :disabled="!jobData.industryId" class="w-full"
                    :class="{ 'p-invalid': submitted && !jobData.subIndustryId }" />
                  <label for="subIndustry">Category / Sub-industry</label>
                </FloatLabel>
                <small v-if="submitted && !jobData.subIndustryId" class="p-error">Sub-industry is required.</small>
              </div>
            </div>

            <div>
              <label for="job_description" class="block mb-2">Job Description</label>
              <Editor id="job_description" editorStyle="height: 220px;" v-model="jobData.description"
                :class="{ 'p-invalid': submitted && !jobData.description }" />
              <small v-if="submitted && !jobData.description" class="p-error">Job Description is required.</small>
            </div>

            <div class="flex gap-4">
              <div class="flex-1">
                <FloatLabel>
                  <Select id="job_type" v-model="jobData.jobType" :options="enums.jobTypes" class="w-full"
                    :class="{ 'p-invalid': submitted && !jobData.jobType }" 
                    optionLabel="label" optionValue="id"/>
                  <label for="job_type">Job Type</label>
                </FloatLabel>
                <small v-if="submitted && !jobData.jobType" class="p-error">Job Type is required.</small>
              </div>
              <div class="flex-1">
                <FloatLabel>
                  <Select id="payment_type" v-model="jobData.paymentType" :options="enums.paymentTypes" class="w-full" 
                    optionLabel="label" optionValue="id"/>
                  <label for="payment_type">Payment Type</label>
                </FloatLabel>
              </div>
            </div>
            <div class="flex gap-4">
              <div class="flex-1">
                <FloatLabel>
                  <InputNumber id="salary" v-model="jobData.salary" mode="currency" currency="INR" locale="en-IN"
                    class="w-full" :class="{ 'p-invalid': submitted && jobData.salary == null }" />
                  <label for="salary">Salary</label>
                </FloatLabel>
                <small v-if="submitted && jobData.salary == null" class="p-error">Salary is required.</small>
              </div>
              <div class="flex-1">
                <FloatLabel>
                  <InputNumber id="vacancies" v-model="jobData.vacancies" class="w-full" />
                  <label for="vacancies">Number of Vacancies</label>
                </FloatLabel>
              </div>
              <div class="flex-1">
                <FloatLabel>
                  <InputNumber id="working_hours" v-model="jobData.workingHours" suffix=" hours" class="w-full" />
                  <label for="working_hours">Working Hours</label>
                </FloatLabel>
              </div>
            </div>
          </div>
          <div class="flex pt-6 justify-end">
            <Button label="Next" icon="pi pi-arrow-right" iconPos="right" @click="handleNextFrom1(activateCallback)" />
          </div>
        </StepPanel>
        <StepPanel v-slot="{ activateCallback }" value="2">
          <div class="flex flex-col gap-6 p-fluid">
            <FloatLabel>
              <Select id="experience_level" v-model="jobData.experienceLevel" :options="enums.experienceLevels"
                class="w-full" optionLabel="label" optionValue="id"/>
              <label for="experience_level">Experience Level</label>
            </FloatLabel>

            <div>
              <label for="skills" class="block mb-2">Skills</label>
              <InputChips id="skills" v-model="jobData.skills" />
            </div>
            <div>
              <label for="requirements" class="block mb-2">Requirements</label>
              <InputChips id="requirements" v-model="jobData.requirements" />
            </div>
            <div>
              <label for="responsibilities" class="block mb-2">Responsibilities</label>
              <InputChips id="responsibilities" v-model="jobData.responsibilities" />
            </div>
            <div>
              <label for="benefits" class="block mb-2">Benefits</label>
              <InputChips id="benefits" v-model="jobData.benefits" />
            </div>

            <Fieldset legend="Allowances" :toggleable="true">
              <div class="flex flex-wrap gap-4">
                <div class="flex items-center">
                  <ToggleSwitch v-model="jobData.accommodation" inputId="accommodation" />
                  <label for="accommodation" class="ml-2">Accommodation</label>
                </div>
                <div class="flex items-center">
                  <ToggleSwitch v-model="jobData.transportation" inputId="transportation" />
                  <label for="transportation" class="ml-2">Transportation</label>
                </div>
                <div class="flex items-center">
                  <ToggleSwitch v-model="jobData.foodProvided" inputId="food" />
                  <label for="food" class="ml-2">Food Provided</label>
                </div>
                <div class="flex items-center">
                  <ToggleSwitch v-model="jobData.safetyEquipment" inputId="safety" />
                  <label for="safety" class="ml-2">Safety Equipment</label>
                </div>
                <div class="flex items-center">
                  <ToggleSwitch v-model="jobData.trainingProvided" inputId="training" />
                  <label for="training" class="ml-2">Training</label>
                </div>
              </div>
            </Fieldset>
          </div>
          <div class="flex pt-6 justify-between">
            <Button label="Back" severity="secondary" icon="pi pi-arrow-left" @click="activateCallback('1')" />
            <Button label="Next" icon="pi pi-arrow-right" iconPos="right" @click="activateCallback('3')" />
          </div>
        </StepPanel>
        <StepPanel v-slot="{ activateCallback }" value="3">
          <div class="flex flex-col gap-6 p-fluid">
            <div class="flex gap-4">
              <FloatLabel>
                <InputText id="job_title" v-model="jobData.location" class="w-full"
                  :class="{ 'p-invalid': submitted && !jobData.location }" />
                <label for="job_title">Job Location</label>
              </FloatLabel>
            </div>
            <div class="flex gap-4">
              <FloatLabel class="flex-1">
                <Select id="job_urgency" v-model="jobData.urgency" :options="enums.urgencyLevels" class="w-full" 
                 optionLabel="label" optionValue="id"/>
                <label for="job_urgency">Job Urgency</label>
              </FloatLabel>
              <FloatLabel class="flex-1">
                <Select id="job_status" v-model="jobData.status" :options="enums.statuses" class="w-full" 
                optionLabel="label" optionValue="id" />
                <label for="job_status">Job Status</label>
              </FloatLabel>
            </div>
            <div class="flex items-center">
              <ToggleSwitch v-model="jobData.showContact" inputId="showContact" />
              <label for="showContact" class="ml-2">Show Contact Information on Job</label>
            </div>

            <template v-if="jobData.showContact">
              <FloatLabel>
                <Select id="contact_display_type" v-model="jobData.contactDisplayType"
                  :options="enums.contactDisplayTypes" class="w-full" optionLabel="label" optionValue="id" />
                <label for="contact_display_type">Contact Display Type</label>
              </FloatLabel>
              <FloatLabel v-if="jobData.contactDisplayType !== 'NONE'">
                <InputText id="contact_person" v-model="jobData.contactPerson" class="w-full" />
                <label for="contact_person">Contact Person</label>
              </FloatLabel>
              <div class="flex gap-4">
                <FloatLabel v-if="jobData.contactDisplayType === 'EMAIL' || jobData.contactDisplayType === 'BOTH'" class="flex-1">
                  <InputText id="contact_email" v-model="jobData.contactEmail" type="email" class="w-full" />
                  <label for="contact_email">Contact Email</label>
                </FloatLabel>
                <FloatLabel v-if="jobData.contactDisplayType === 'PHONE' || jobData.contactDisplayType === 'BOTH'" class="flex-1">
                  <InputText id="contact_phone" v-model="jobData.contactPhone" class="w-full" />
                  <label for="contact_phone">Contact Phone</label>
                </FloatLabel>
              </div>
            </template>

            
          </div>
          <div class="flex pt-6 justify-between">
            <Button label="Back" severity="secondary" icon="pi pi-arrow-left" @click="activateCallback('2')" />
            <Button label="Create" severity="primary" icon="pi pi-check" @click="handleCreateJob(activateCallback)" />
          </div>
        </StepPanel>
      </StepPanels>
    </Stepper>

  </Drawer>
</template>
