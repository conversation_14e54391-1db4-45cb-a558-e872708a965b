import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, Between, DataSource, In, ILike } from 'typeorm';
import { JobEntity, JobStatus } from '../entities/job.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { IndustryEntity } from '../../industries/entities/industry.entity';
import { SubIndustryEntity } from '../../industries/entities/sub-industry.entity';
import { CreateJobDto } from '../dto/create-job.dto';
import { UpdateJobDto } from '../dto/update-job.dto';
import { JobScheduleDto } from '../dto/job-schedule.dto';
import { UserRole } from '../../users/enums/user.enum';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';
import { ApplicationStatus, JobApplicationEntity } from '../entities/job-application.entity';
import { JobFavoriteEntity } from '../entities/job-favorite.entity';
import { ProfileEntity } from '../../users/entities/profile.entity';
import { CompanyEntity } from '../../companies/entities/company.entity';

@Injectable()
export class JobService {
  private readonly WAIT_TIME_MINUTES = 15;

  constructor(
    @InjectRepository(JobEntity)
    private readonly jobRepository: Repository<JobEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(IndustryEntity)
    private readonly industryRepository: Repository<IndustryEntity>,
    @InjectRepository(SubIndustryEntity)
    private readonly subIndustryRepository: Repository<SubIndustryEntity>,
    @InjectRepository(JobApplicationEntity)
    private readonly applicationRepository: Repository<JobApplicationEntity>,
    @InjectRepository(JobFavoriteEntity)
    private readonly favoriteRepository: Repository<JobFavoriteEntity>,
    @InjectRepository(ProfileEntity)
    private readonly profileRepository: Repository<ProfileEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepository: Repository<CompanyEntity>,
    private readonly dataSource: DataSource, // <-- Add this line
  ) {}

  async create(employerId: string, createJobDto: CreateJobDto): Promise<JobEntity> {
    const employer = await this.userRepository.findOne({
      where: { id: employerId },
      relations: ['verification'],
    });

    if (!employer) {
      throw new NotFoundException('Employer not found');
    }

    // if (employer.role !== UserRole.EMPLOYER) {
    //   throw new BadRequestException('Only employers can create jobs');
    // }

    if (employer.isBlocked) {
      throw new BadRequestException('Contact admin to create jobs');
    }

    // --- RATE LIMIT: Max 3 jobs per day ---
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    const jobsToday = await this.jobRepository.count({
      where: {
        employer: { id: employerId },
        createdAt: Between(todayStart, todayEnd),
        isDeleted: false,
      },
    });

    if (jobsToday >= 3) {
      throw new BadRequestException('You can only create up to 3 jobs per day.');
    }
    // --- END RATE LIMIT ---

    const industry = await this.industryRepository.findOne({
      where: { id: createJobDto.industryId, isActive: true },
    });

    if (!industry) {
      throw new NotFoundException('Industry not found or inactive');
    }

    let subIndustry: SubIndustryEntity | undefined;
    if (createJobDto.subIndustryId) {
      subIndustry = await this.subIndustryRepository.findOne({
        where: { id: createJobDto.subIndustryId, isActive: true },
      });
      if (!subIndustry) {
        throw new NotFoundException('Sub-industry not found or inactive');
      }
    }

    // Calculate scheduled post time (15 minutes from now)
    const scheduledPostTime = new Date();
    scheduledPostTime.setMinutes(scheduledPostTime.getMinutes() + this.WAIT_TIME_MINUTES);

    // Fetch the company for this employer
    const company = await this.companyRepository.findOne({ where: { userId: employerId } });

    const job = this.jobRepository.create({
      ...createJobDto,
      employer,
      company, // <-- assign company here if found
      industry,
      subIndustry,
      isUserVerified: employer.verification?.isAadharVerified || false,
      status: JobStatus.PENDING,
      scheduledPostTime,
    });

    return this.jobRepository.save(job);
  }

  async findAll(
    user: UserEntity,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobEntity>> {
    const {
      page = 1,
      limit = 10,
      sortBy,
      industry,
      jobType,
      search,
      location,
      experienceLevel,
      paymentType,
    } = paginationDto;
    const skip = (page - 1) * limit;

    // Filter: only show ACTIVE jobs to jobseekers
    const where: any = { isDeleted: false };
    if (user.role === UserRole.JOB_SEEKER) {
      where.status = JobStatus.ACTIVE;
    }

    let order: any = { createdAt: 'DESC' };

    if (sortBy) {
      switch (sortBy) {
        case 'DESC':
          order = { createdAt: 'DESC' };
          break;
        case 'ASC':
          order = { createdAt: 'ASC' };
          break;
        case 'URGENT':
          where.urgency = 'URGENT'; // Make sure you have this field in your JobEntity
          break;
        case 'accommodation':
          where.accommodation = true;
          break;
        case 'foodProvided':
          where.foodProvided = true;
          break;
        default:
          // fallback or ignore
          break;
      }
    }

    if (industry) {
      if (Array.isArray(industry)) {
        where.industry = { id: In(industry) };
      } else {
        where.industry = { id: industry };
      }
    }

    if (jobType) {
      if (Array.isArray(jobType)) {
        where.jobType = In(jobType);
      } else {
        where.jobType = jobType;
      }
    }

    if (search) {
      where.title = ILike(`%${search}%`);
    }

    if (location) {
      where.location = ILike(`%${location}%`);
    }

    if (experienceLevel) {
      where.experienceLevel = experienceLevel;
    }

    if (paymentType) {
      where.paymentType = paymentType;
    }

    const [jobs, total] = await this.jobRepository.findAndCount({
      where,
      relations: ['employer', 'industry', 'company'],
      order,
      skip,
      take: limit,
    });

    // --- Add isFavorite for jobseeker ---
    if (user.role === UserRole.JOB_SEEKER) {
      // Get all favorite job IDs for this user in one query
      const favorites = await this.favoriteRepository.find({
        where: { user: { id: user.id } },
        relations: ['job'],
      });
      const favoriteJobIds = new Set(favorites.map((fav) => fav.job.id));
      // Add isFavorite to each job
      jobs.forEach((job) => {
        (job as any).isFavorite = favoriteJobIds.has(job.id);
      });
    }

    const totalPages = Math.ceil(total / limit);

    return {
      items: jobs,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findMyJobs(
    userId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [jobs, total] = await this.jobRepository.findAndCount({
      where: {
        employer: { id: userId },
        isDeleted: false,
      },
      relations: ['employer', 'industry', 'company'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    // Get applicants count for each job
    const jobIds = jobs.map((job) => job.id);
    const applicantsCounts = await this.applicationRepository
      .createQueryBuilder('application')
      .select('application.job.id', 'jobId')
      .addSelect('COUNT(application.id)', 'applicantsCount')
      .where('application.job.id IN (:...jobIds)', { jobIds })
      .groupBy('application.job.id')
      .getRawMany();

    // Create a map for quick lookup
    const applicantsCountMap = new Map();
    applicantsCounts.forEach((item) => {
      applicantsCountMap.set(item.jobId, parseInt(item.applicantsCount));
    });

    // Add applicants count and views to each job
    jobs.forEach((job) => {
      (job as any).applicantsCount = applicantsCountMap.get(job.id) || 0;
      (job as any).views = job.views || 0;
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: jobs,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async incrementJobViews(jobId: string): Promise<void> {
    await this.dataSource.query(`UPDATE jobs SET views = views + 1 WHERE id = $1`, [jobId]);
  }

  async findOne(id: string, user: UserEntity): Promise<JobEntity> {
    const where: any = { id, isDeleted: false };
    if (user.role === UserRole.JOB_SEEKER) {
      where.status = JobStatus.ACTIVE;
    }
    // Optionally, employers/admins can see their own jobs regardless of status
    const job = await this.jobRepository.findOne({
      where,
      relations: ['industry', 'employer', 'company', 'profile', 'subIndustry'],
    });
    if (job) {
      // Only increment views for job seekers, not for employers viewing their own jobs
      if (user.role === UserRole.JOB_SEEKER) {
        await this.incrementJobViews(job.id);
      }

      // If user is a jobseeker, check if they have applied
      if (user.role === UserRole.JOB_SEEKER) {
        const application = await this.applicationRepository.findOne({
          where: {
            job: { id: job.id },
            applicant: { id: user.id },
          },
          // order: { createdAt: 'DESC' },
        });
        (job as any).hasApplied =
          (application && application.status !== ApplicationStatus.WITHDRAWN) || false;
        (job as any).applicationId = application?.id;
        (job as any).applicationStatus = application?.status || null;

        // Check if job is favorite
        const favorite = await this.favoriteRepository.findOne({
          where: {
            job: { id: job.id },
            user: { id: user.id },
          },
        });
        (job as any).isFavorite = !!favorite;
      }
    }
    return job;
  }

  async update(
    id: string,
    userId: string,
    userRole: UserRole,
    updateJobDto: UpdateJobDto,
  ): Promise<JobEntity> {
    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Check if user is admin or the job creator
    if (
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN &&
      job.employer.id !== userId
    ) {
      throw new ForbiddenException('Only admin or job creator can update this job');
    }

    if (updateJobDto.industryId) {
      const industry = await this.industryRepository.findOne({
        where: { id: updateJobDto.industryId, isActive: true },
      });

      if (!industry) {
        throw new NotFoundException('Industry not found or inactive');
      }

      job.industry = industry;
    }

    if (updateJobDto.subIndustryId) {
      const subIndustry = await this.subIndustryRepository.findOne({
        where: { id: updateJobDto.subIndustryId, isActive: true },
      });
      if (!subIndustry) {
        throw new NotFoundException('Sub-industry not found or inactive');
      }
      job.subIndustry = subIndustry;
    }

    // If job is being updated while pending, reset the scheduled post time
    if (job.status === JobStatus.PENDING) {
      const scheduledPostTime = new Date();
      scheduledPostTime.setMinutes(scheduledPostTime.getMinutes() + this.WAIT_TIME_MINUTES);
      job.scheduledPostTime = scheduledPostTime;
    }

    Object.assign(job, updateJobDto);
    return this.jobRepository.save(job);
  }

  async remove(id: string, userId: string, userRole: UserRole): Promise<void> {
    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Check if user is admin or the job creator
    if (
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN &&
      job.employer.id !== userId
    ) {
      throw new ForbiddenException('Only admin or job creator can delete this job');
    }

    job.isDeleted = true;
    await this.jobRepository.save(job);
  }

  // Method to activate pending jobs that have passed their wait time
  async activatePendingJobs(): Promise<void> {
    const now = new Date();
    await this.jobRepository.update(
      {
        status: JobStatus.PENDING,
        scheduledPostTime: LessThanOrEqual(now),
      },
      {
        status: JobStatus.ACTIVE,
      },
    );
  }

  async findAllPendingJobs(paginationDto: PaginationDto): Promise<PaginatedResponseDto<JobEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [jobs, total] = await this.jobRepository.findAndCount({
      where: {
        status: JobStatus.PENDING,
        isDeleted: false,
      },
      relations: ['employer', 'industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: jobs,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findPendingJobById(id: string): Promise<JobEntity> {
    const job = await this.jobRepository.findOne({
      where: {
        id,
        status: JobStatus.PENDING,
        isDeleted: false,
      },
      relations: ['employer', 'industry'],
    });

    if (!job) {
      throw new NotFoundException('Pending job not found');
    }

    return job;
  }

  async scheduleJob(
    id: string,
    userRole: UserRole,
    scheduleDto: JobScheduleDto,
  ): Promise<JobEntity> {
    if (userRole !== UserRole.ADMIN && userRole !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only admins can schedule jobs');
    }

    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    if (job.status !== JobStatus.PENDING) {
      throw new BadRequestException('Can only schedule pending jobs');
    }

    // Update job status and remove scheduled post time if job is being activated
    if (scheduleDto.status === JobStatus.ACTIVE) {
      job.scheduledPostTime = null;
    }

    job.status = scheduleDto.status;
    job.adminComment = scheduleDto.comment;

    return this.jobRepository.save(job);
  }

  async getJobScheduleHistory(id: string): Promise<JobEntity> {
    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer', 'industry'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    return job;
  }

  async getDashboardData(userId: string) {
    // Get total jobs count
    const totalJobs = await this.jobRepository.count({
      where: {
        employer: { id: userId },
        isDeleted: false,
      },
    });

    // Get jobs by status
    const jobsByStatus = await this.jobRepository
      .createQueryBuilder('job')
      .select('job.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('job.employer.id = :userId', { userId })
      .andWhere('job.isDeleted = :isDeleted', { isDeleted: false })
      .groupBy('job.status')
      .getRawMany();

    // Get all jobs for activity log
    const jobs = await this.jobRepository.find({
      where: { employer: { id: userId } },
      relations: ['industry'],
    });

    // Build activity log: added, updated, deleted
    let activities = [];
    for (const job of jobs) {
      // Added
      if (job.createdAt.getTime() === job.updatedAt.getTime() && job.isDeleted === false) {
        activities.push({
          id: job.id + '-added',
          jobId: job.id,
          title: job.title,
          action: 'added',
          time: job.createdAt,
          isDeleted: false,
        });
      }
      // Updated (not deleted, and updatedAt != createdAt)
      if (job.createdAt.getTime() !== job.updatedAt.getTime() && job.isDeleted === false) {
        activities.push({
          id: job.id + '-updated',
          jobId: job.id,
          title: job.title,
          action: 'updated',
          time: job.updatedAt,
          isDeleted: false,
        });
      }
      // Deleted (updatedAt != createdAt and isDeleted is true)
      if (job.createdAt.getTime() !== job.updatedAt.getTime() && job.isDeleted === true) {
        activities.push({
          id: job.id + '-deleted',
          jobId: job.id,
          title: job.title,
          action: 'deleted',
          time: job.updatedAt,
          isDeleted: true,
    });
      }
    }
    // Sort by time descending (most recent first)
    activities.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
    // Limit to most recent 10 actions
    activities = activities.slice(0, 10);

    // Get all applications for jobs posted by this employer
    const applications = await this.applicationRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.job', 'job')
      .leftJoinAndSelect('application.applicant', 'applicant')
      .leftJoinAndSelect('applicant.profile', 'profile')
      .where('job.employer.id = :userId', { userId })
      .orderBy('application.createdAt', 'DESC')
      .getMany();

    // Format applications for the dashboard
    const formattedApplications = applications.map((app) => {
      let applicantName = '';

      // Try to get name from profile first
      if (app.applicant?.profile?.firstName || app.applicant?.profile?.lastName) {
        const firstName = app.applicant.profile.firstName || '';
        const lastName = app.applicant.profile.lastName || '';
        applicantName = `${firstName} ${lastName}`.trim();
      }

      // Final fallback to email
      if (!applicantName) {
        applicantName = app.applicant?.email || 'Unknown Applicant';
      }

      return {
        id: app.id,
        jobTitle: app.job?.title || '',
        applicantName,
        applicationDate: app.createdAt,
        status: app.status,
      };
    });

    return {
      statistics: {
        totalJobs,
        jobsByStatus: jobsByStatus.reduce((acc, curr) => {
          acc[curr.status] = parseInt(String(curr.count));
          return acc;
        }, {}),
        totalApplications: applications.length, // <-- add this line
        applications: formattedApplications, // <-- add this line
      },
      recentActivity: activities,
    };
  }

  async getRecommendedJobsForUser(userId: string, limit = 5): Promise<JobEntity[]> {
    // Fetch user profile
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile'],
    });
    if (!user || !user.profile) return [];

    const skills = user.profile.skills || [];
    const location = user.profile.location || '';

    // Find jobs that match at least one skill or location
    const query = this.jobRepository
      .createQueryBuilder('job')
      .where('job.status = :status', { status: JobStatus.ACTIVE })
      .andWhere('job.isDeleted = false')
      .orderBy('job.createdAt', 'DESC')
      .take(limit);

    if (skills.length) {
      query.andWhere('job.skills && ARRAY[:...skills]', { skills });
    }
    if (location) {
      query.andWhere('job.location ILIKE :location', { location: `%${location}%` });
    }

    return await query.getMany();
  }

  async getJobSeekerDashboardData(userId: string) {
    // Get total active jobs count
    const totalActiveJobs = await this.jobRepository.count({
      where: {
        status: JobStatus.ACTIVE,
        isDeleted: false,
      },
    });

    // Get jobs by industry
    const jobsByIndustry = await this.jobRepository
      .createQueryBuilder('job')
      .select('industry.name', 'industry')
      .addSelect('COUNT(*)', 'count')
      .leftJoin('job.industry', 'industry')
      .where('job.status = :status', { status: JobStatus.ACTIVE })
      .andWhere('job.isDeleted = :isDeleted', { isDeleted: false })
      .groupBy('industry.name')
      .getRawMany();

    // Get recent active jobs (last 5)
    const recentJobs = await this.jobRepository.find({
      where: {
        status: JobStatus.ACTIVE,
        isDeleted: false,
      },
      relations: ['industry', 'employer'],
      order: { createdAt: 'DESC' },
      take: 5,
    });

    // Get jobs by experience level
    const jobsByExperience = await this.jobRepository
      .createQueryBuilder('job')
      .select('job.experienceLevel', 'experienceLevel')
      .addSelect('COUNT(*)', 'count')
      .where('job.status = :status', { status: JobStatus.ACTIVE })
      .andWhere('job.isDeleted = :isDeleted', { isDeleted: false })
      .groupBy('job.experienceLevel')
      .getRawMany();

    // Get total applications sent by the jobseeker
    const totalApplications = await this.applicationRepository.count({
      where: { applicant: { id: userId } },
    });

    // Get profile views for the jobseeker
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile'],
    });
    const profileViews = user?.profile?.profileViews || 0;

    // Get saved jobs count for the jobseeker
    const savedJobs = await this.favoriteRepository.count({
      where: { user: { id: userId } },
    });

    // Get recommended jobs
    const recommendedJobs = await this.getRecommendedJobsForUser(userId, 5);

    return {
      statistics: {
        totalActiveJobs,
        jobsByIndustry: jobsByIndustry.reduce((acc, curr) => {
          acc[curr.industry] = parseInt(String(curr.count));
          return acc;
        }, {}),
        jobsByExperience: jobsByExperience.reduce((acc, curr) => {
          acc[curr.experienceLevel] = parseInt(String(curr.count));
          return acc;
        }, {}),
        totalApplications,
        profileViews,
        savedJobs,
      },
      recentActivity: {
        jobs: recentJobs,
      },
      recommendedJobs,
    };
  }

  async getProfile(viewedUserId: string, viewerUserId: string) {
    // Only increment if the viewer is not the owner
    if (viewedUserId !== viewerUserId) {
      await this.profileRepository.increment({ userId: viewedUserId }, 'profileViews', 1);
    }
    // Fetch and return the profile
    return this.profileRepository.findOne({ where: { userId: viewedUserId } });
  }

  async getActiveJobCount(): Promise<number> {
    return this.jobRepository.count({
      where: {
        status: JobStatus.ACTIVE,
        isDeleted: false,
      },
    });
  }

  async getActiveJobCountByIndustry(): Promise<Array<{ industryName: string, count: number }>> {
    const jobsByIndustry = await this.jobRepository
      .createQueryBuilder('job')
      .leftJoin('job.industry', 'industry')
      // .select('job.industryId', 'industryId')
      .addSelect('industry.name', 'industryName')
      .addSelect('COUNT(*)', 'count')
      .where('job.status = :status', { status: JobStatus.ACTIVE })
      .andWhere('job.isDeleted = false')
      .groupBy('job.industryId')
      .addGroupBy('industry.name')
      .getRawMany();

    return jobsByIndustry.map(row => ({
      industryName: row.industryName,
      count: parseInt(row.count, 10)
    }));
  }
}
