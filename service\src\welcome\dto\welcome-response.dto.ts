import { ApiProperty } from '@nestjs/swagger';

export class DescriptionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  description: string;
}

export class AudienceMessageDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  message: string;
}

export class TestimonialDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  author: string;

  @ApiProperty()
  content: string;
}

export class WelcomeResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  cmp_name: string;

  @ApiProperty()
  logo_path: string;

  @ApiProperty()
  show_signup: boolean;

  @ApiProperty()
  show_login: boolean;

  @ApiProperty()
  welcome_pop_msg: string;

  @ApiProperty()
  base_url: string;

  @ApiProperty()
  notification_url: string;

  @ApiProperty()
  user_url: string;

  @ApiProperty({ type: [DescriptionDto] })
  descriptions: DescriptionDto[];

  @ApiProperty({ type: [AudienceMessageDto] })
  audience_messages: AudienceMessageDto[];

  @ApiProperty({ type: [TestimonialDto] })
  testimonials: TestimonialDto[];
}
