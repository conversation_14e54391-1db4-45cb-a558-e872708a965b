import { ref, computed, watch } from 'vue'
import { updatePreset } from '@primeuix/themes'
import Aura from '@primeuix/themes/aura'

const isDarkMode = ref(false)
const currentTheme = ref('blue')

// Available theme colors
const availableThemes = [
  { name: 'Blue', value: 'blue' },
  { name: 'Green', value: 'green' },
  { name: 'Purple', value: 'purple' },
  { name: 'Orange', value: 'orange' },
  { name: 'Red', value: 'red' },
  { name: 'Teal', value: 'teal' }
]

// Initialize theme from localStorage with jdadmin prefix or system preference
const initializeTheme = () => {
  const savedTheme = localStorage.getItem('jdadmin-theme')
  const savedDarkMode = localStorage.getItem('jdadmin-dark-mode')
  
  if (savedDarkMode !== null) {
    isDarkMode.value = JSON.parse(savedDarkMode)
  } else {
    // Check system preference
    isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }
  
  if (savedTheme) {
    currentTheme.value = savedTheme
  }
  
  applyTheme()
}

const applyTheme = () => {
  // Apply dark mode class
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
  
  // Update PrimeVue theme with proper dark mode support
  updatePreset({
    semantic: {
      primary: {
        50: getThemeShade(currentTheme.value, 50),
        100: getThemeShade(currentTheme.value, 100),
        200: getThemeShade(currentTheme.value, 200),
        300: getThemeShade(currentTheme.value, 300),
        400: getThemeShade(currentTheme.value, 400),
        500: getThemeShade(currentTheme.value, 500),
        600: getThemeShade(currentTheme.value, 600),
        700: getThemeShade(currentTheme.value, 700),
        800: getThemeShade(currentTheme.value, 800),
        900: getThemeShade(currentTheme.value, 900),
        950: getThemeShade(currentTheme.value, 950)
      },
      colorScheme: {
        light: {
          primary: {
            color: getThemeColor(currentTheme.value, false),
            contrastColor: '#ffffff',
            hoverColor: getThemeHoverColor(currentTheme.value, false),
            activeColor: getThemeActiveColor(currentTheme.value, false)
          },
          surface: {
            0: '#ffffff',
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#64748b',
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
            950: '#020617'
          }
        },
        dark: {
          primary: {
            color: getThemeColor(currentTheme.value, true),
            contrastColor: '#ffffff',
            hoverColor: getThemeHoverColor(currentTheme.value, true),
            activeColor: getThemeActiveColor(currentTheme.value, true)
          },
          surface: {
            0: '#ffffff',
            50: '#0f172a',
            100: '#1e293b',
            200: '#334155',
            300: '#475569',
            400: '#64748b',
            500: '#94a3b8',
            600: '#cbd5e1',
            700: '#e2e8f0',
            800: '#f1f5f9',
            900: '#f8fafc',
            950: '#ffffff'
          }
        }
      }
    }
  })
}

const getThemeShade = (theme, shade) => {
  const shades = {
    blue: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    },
    green: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },
    purple: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87',
      950: '#3b0764'
    },
    orange: {
      50: '#fff7ed',
      100: '#ffedd5',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#f97316',
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12',
      950: '#431407'
    },
    red: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },
    teal: {
      50: '#f0fdfa',
      100: '#ccfbf1',
      200: '#99f6e4',
      300: '#5eead4',
      400: '#2dd4bf',
      500: '#14b8a6',
      600: '#0d9488',
      700: '#0f766e',
      800: '#115e59',
      900: '#134e4a',
      950: '#042f2e'
    }
  }
  
  return shades[theme]?.[shade] || shades.blue[shade]
}

const getThemeColor = (theme, dark) => {
  const colors = {
    blue: dark ? '#60a5fa' : '#2563eb',
    green: dark ? '#4ade80' : '#16a34a',
    purple: dark ? '#c084fc' : '#7c3aed',
    orange: dark ? '#fb923c' : '#ea580c',
    red: dark ? '#f87171' : '#dc2626',
    teal: dark ? '#2dd4bf' : '#0d9488'
  }
  return colors[theme] || colors.blue
}

const getThemeHoverColor = (theme, dark) => {
  const colors = {
    blue: dark ? '#93c5fd' : '#1d4ed8',
    green: dark ? '#86efac' : '#15803d',
    purple: dark ? '#d8b4fe' : '#6b21a8',
    orange: dark ? '#fdba74' : '#c2410c',
    red: dark ? '#fca5a5' : '#b91c1c',
    teal: dark ? '#5eead4' : '#0f766e'
  }
  return colors[theme] || colors.blue
}

const getThemeActiveColor = (theme, dark) => {
  const colors = {
    blue: dark ? '#3b82f6' : '#1e40af',
    green: dark ? '#22c55e' : '#166534',
    purple: dark ? '#a855f7' : '#581c87',
    orange: dark ? '#f97316' : '#9a3412',
    red: dark ? '#ef4444' : '#991b1b',
    teal: dark ? '#14b8a6' : '#134e4a'
  }
  return colors[theme] || colors.blue
}

// Watch for theme changes and save with jdadmin prefix
watch([isDarkMode, currentTheme], () => {
  localStorage.setItem('jdadmin-dark-mode', JSON.stringify(isDarkMode.value))
  localStorage.setItem('jdadmin-theme', currentTheme.value)
  applyTheme()
}, { immediate: false })

export function useTheme() {
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
  }

  const setTheme = (newTheme) => {
    currentTheme.value = newTheme
  }

  return {
    isDarkMode: computed(() => isDarkMode.value),
    theme: computed(() => currentTheme.value),
    availableThemes,
    toggleDarkMode,
    setTheme,
    initializeTheme
  }
}