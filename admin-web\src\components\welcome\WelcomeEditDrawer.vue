<template>
  <Drawer 
    :visible="modelValue" 
    position="right" 
    :modal="true"
    :dismissable="true"
    :showCloseIcon="true"
    :style="{ width: '600px' }" 
    @update:visible="onDrawerVisible" 
    @hide="onClose"
  >
    <template #header>
      <span>Edit Welcome Page</span>
    </template>
    <form @submit.prevent="onSubmit">
      <div class="form-group">
        <label>Company Name *</label>
        <InputText v-model="form.cmp_name" class="w-full" required />
      </div>
      <div class="form-group">
        <label>Logo URL</label>
        <InputText v-model="form.logo_path" class="w-full" />
      </div>
      <div class="form-group">
        <label>Show Signup</label>
        <Checkbox v-model="form.show_signup" />
      </div>
      <div class="form-group">
        <label>Show Login</label>
        <Checkbox v-model="form.show_login" />
      </div>
      <div class="form-group">
        <label>Welcome Popup Message</label>
        <InputText v-model="form.welcome_pop_msg" class="w-full" />
      </div>
      <div class="form-group">
        <label>Base URL</label>
        <InputText v-model="form.base_url" class="w-full" />
      </div>
      <div class="form-group">
        <label>Notification URL</label>
        <InputText v-model="form.notification_url" class="w-full" />
      </div>
      <div class="form-group">
        <label>User URL</label>
        <InputText v-model="form.user_url" class="w-full" />
      </div>
      <div class="form-group">
        <label>Welcome Message *</label>
        <Textarea v-model="form.message" class="w-full" required />
      </div>
      <div class="form-group">
        <label>Image URL</label>
        <InputText v-model="form.imageUrl" class="w-full" />
      </div>
      <!-- Descriptions, Audience Messages, Testimonials can be added as needed -->
      <div class="form-actions">
        <Button type="submit" label="Save" icon="pi pi-check" class="mr-2" :disabled="!form.cmp_name || !form.message || loading" />
        <Button type="button" label="Cancel" icon="pi pi-times" severity="secondary" @click="onClose" />
      </div>
    </form>
  </Drawer>
</template>

<script setup>
import Drawer from 'primevue/drawer'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Checkbox from 'primevue/checkbox'
import Button from 'primevue/button'
import { reactive, ref, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  record: {
    type: Object,
    default: null
  }
})
const emit = defineEmits(['update:modelValue', 'success', 'close', 'submit'])

const loading = ref(false)

const form = reactive({
  cmp_name: '',
  logo_path: '',
  show_signup: false,
  show_login: false,
  welcome_pop_msg: '',
  base_url: '',
  notification_url: '',
  user_url: '',
  message: '',
  imageUrl: ''
})

watch(() => props.record, (val) => {
  if (val) {
    Object.assign(form, {
      cmp_name: val.cmp_name || '',
      logo_path: val.logo_path || '',
      show_signup: !!val.show_signup,
      show_login: !!val.show_login,
      welcome_pop_msg: val.welcome_pop_msg || '',
      base_url: val.base_url || '',
      notification_url: val.notification_url || '',
      user_url: val.user_url || '',
      message: val.message || '',
      imageUrl: val.imageUrl || ''
    })
  }
}, { immediate: true })

const resetForm = () => {
  Object.assign(form, {
    cmp_name: '',
    logo_path: '',
    show_signup: false,
    show_login: false,
    welcome_pop_msg: '',
    base_url: '',
    notification_url: '',
    user_url: '',
    message: '',
    imageUrl: ''
  })
}

const onSubmit = async () => {
  if (!form.cmp_name || !form.message) return
  loading.value = true
  try {
    emit('submit', { ...form, id: props.record?.id })
    emit('success')
    emit('update:modelValue', false)
    resetForm()
  } finally {
    loading.value = false
  }
}
const onClose = () => {
  emit('close')
  emit('update:modelValue', false)
  resetForm()
}
const onDrawerVisible = (val) => {
  emit('update:modelValue', val)
}
</script>

<style scoped>
.form-group {
  margin-bottom: 1rem;
}
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}
</style> 