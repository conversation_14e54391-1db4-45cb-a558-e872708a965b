<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import Card from 'primevue/card';
import Button from 'primevue/button';
import ConfirmDialog from 'primevue/confirmdialog';
import { useConfirm } from "primevue/useconfirm";
import SpeedDial from 'primevue/speeddial';

import ErrorTrackingTable from '@/components/error-tracking/ErrorTrackingTable.vue';
import ErrorFilterDrawer from '@/components/error-tracking/ErrorFilterDrawer.vue';
import ErrorDetailDrawer from '@/components/error-tracking/ErrorDetailDrawer.vue';
import ErrorCreateDrawer from '@/components/error-tracking/ErrorCreateDrawer.vue';
import api from '@/api';
import { errorLogService } from '@/api/services/errorLogService';

const toast = useToast();
const confirm = useConfirm();

const errors = ref([]);
const loading = ref(false);

const isDetailDrawerVisible = ref(false);
const isFilterDrawerVisible = ref(false);
const selectedError = ref(null);
const isCreateDrawerVisible = ref(false);
const editingErrorLog = ref(null);

const initialFilters = {
  searchTerm: '',
  severities: [],
  screens: [],
  resolvedStatus: null,
  dateRange: null,
};
const filters = ref(JSON.parse(JSON.stringify(initialFilters)));

const severityOptions = [
    { label: 'Low', value: 'LOW' },
    { label: 'Medium', value: 'MEDIUM' },
    { label: 'High', value: 'HIGH' },
    { label: 'Critical', value: 'CRITICAL' },
];

const screenOptions = computed(() => {
    const screens = new Set(errors.value.map(e => e.screen).filter(Boolean));
    return Array.from(screens).map(s => ({label: s, value: s}));
});

const page = ref(1);
const limit = ref(20);
const total = ref(0);
const sortField = ref('createdAt');
const sortOrder = ref(-1); // -1 for desc, 1 for asc

async function fetchErrors() {
  loading.value = true;
  try {
    const params = {
      page: page.value,
      limit: limit.value,
      ...filters.value,
      sortField: sortField.value,
      sortOrder: sortOrder.value,
    };
    const response = await errorLogService.getErrorLogs(params);
    errors.value = response.data.items || response.data || [];
    total.value = response.data.total || response.total || 0;
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to fetch errors.', life: 3000 });
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchErrors();
});

watch(filters, fetchErrors, { deep: true });

function handleViewError(error) {
  selectedError.value = error;
  isDetailDrawerVisible.value = true;
}

function handleToggleResolution(error) {
    const action = error.resolved ? 'unresolve' : 'resolve';
    confirm.require({
        message: `Are you sure you want to mark this error as ${error.resolved ? 'unresolved' : 'resolved'}?`,
        header: 'Confirm Resolution Change',
        icon: 'pi pi-info-circle',
        acceptClass: `p-button-${action === 'resolve' ? 'success' : 'warning'}`,
        accept: async () => {
            try {
                const updatedError = await api.error.updateErrorLog(error.id, { resolved: !error.resolved });
                const index = errors.value.findIndex(e => e.id === error.id);
                if (index !== -1) {
                    errors.value[index] = { ...errors.value[index], ...updatedError.data };
                }
                if(selectedError.value && selectedError.value.id === error.id) {
                    selectedError.value = { ...selectedError.value, ...updatedError.data };
                }
                toast.add({ severity: 'success', summary: 'Success', detail: `Error marked as ${updatedError.data.resolved ? 'resolved' : 'unresolved'}.`, life: 3000 });
                fetchErrors();
            } catch (err) {
                 toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to update error status.', life: 3000 });
            }
        },
    });
}

function handleTablePage(event) {
  page.value = event.page + 1;
  limit.value = event.rows;
  fetchErrors();
}

function handleTableSort(event) {
  sortField.value = event.sortField;
  sortOrder.value = event.sortOrder;
  fetchErrors();
}

function handleApplyFilters(newFilters) {
  filters.value = newFilters;
  page.value = 1;
  fetchErrors();
}

function handleResetFilters() {
  filters.value = JSON.parse(JSON.stringify(initialFilters));
  page.value = 1;
  fetchErrors();
}

function openCreateDrawer() {
  editingErrorLog.value = null;
  isCreateDrawerVisible.value = true;
}

function openEditDrawer(errorLog) {
  editingErrorLog.value = errorLog;
  isCreateDrawerVisible.value = true;
}

function closeCreateDrawer() {
  isCreateDrawerVisible.value = false;
  editingErrorLog.value = null;
}

async function handleCreateSuccess(payload) {
  try {
    loading.value = true;
    await errorLogService.createErrorLog(payload);
    toast.add({ severity: 'success', summary: 'Success', detail: 'Error log created.', life: 3000 });
    isCreateDrawerVisible.value = false;
    fetchErrors();
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to create error log.', life: 3000 });
  } finally {
    loading.value = false;
  }
}

async function handleUpdateErrorLog(payload) {
  try {
    loading.value = true;
    await errorLogService.updateErrorLog(payload.id, payload);
    toast.add({ severity: 'success', summary: 'Success', detail: 'Error log updated.', life: 3000 });
    isCreateDrawerVisible.value = false;
    editingErrorLog.value = null;
    fetchErrors();
  } catch (err) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to update error log.', life: 3000 });
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="flex flex-1">
    <ConfirmDialog></ConfirmDialog>
    <div class="p-4 flex flex-col flex-1 min-h-0">
      <Card class="flex flex-col flex-1 min-h-0">
        <template #content>
          <div class="flex flex-col flex-1 min-h-0">
            <ErrorTrackingTable
              :errors="errors"
              :loading="loading"
              :total="total"
              :page="page"
              :limit="limit"
              :sort-field="sortField"
              :sort-order="sortOrder"
              @page="handleTablePage"
              @sort="handleTableSort"
              @view-error="handleViewError"
              @toggle-resolution="handleToggleResolution"
              @edit-error="openEditDrawer"
            />
          </div>
        </template>
      </Card>
    </div>

    <ErrorFilterDrawer 
        :visible="isFilterDrawerVisible"
        :filters="filters"
        :severity-options="severityOptions"
        :screen-options="screenOptions"
        @close="isFilterDrawerVisible = false"
        @apply-filters="handleApplyFilters"
        @reset-filters="handleResetFilters"
    />

    <ErrorDetailDrawer
        :visible="isDetailDrawerVisible"
        :error="selectedError"
        @close="isDetailDrawerVisible = false"
        @toggle-resolution="handleToggleResolution"
    />

    <ErrorCreateDrawer
      :visible="isCreateDrawerVisible"
      :error-log="editingErrorLog"
      @close="closeCreateDrawer"
      @success="handleCreateSuccess"
      @update="handleUpdateErrorLog"
    />

    <SpeedDial
      :model="[
        { label: 'Create', icon: 'pi pi-plus', command: openCreateDrawer },
        { label: 'Filter', icon: 'pi pi-filter', command: () => isFilterDrawerVisible = true }
      ]"
      direction="up"
      :style="{ position: 'fixed', right: '1.5rem', bottom: '1.2rem', zIndex: 1000 }"
      :tooltipOptions="{ position: 'left' }"
      showIcon="pi pi-bars"
      hideIcon="pi pi-times"
    />
  </div>
</template> 