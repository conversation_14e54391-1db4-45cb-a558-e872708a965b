<script setup>
import Card from 'primevue/card'
</script>

<template>
  <div class="files-view">
    <div class="page-header">
      <h1>Files</h1>
      <p>Manage files and documents.</p>
    </div>
    
    <Card>
      <template #content>
        <div class="empty-state">
          <i class="pi pi-folder empty-icon"></i>
          <h3>File Manager</h3>
          <p>This feature is currently being developed and will be available soon.</p>
        </div>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.files-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.page-header p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-icon {
  font-size: 3rem;
  color: var(--p-text-muted-color);
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: var(--p-text-color);
}

.empty-state p {
  margin: 0;
  color: var(--p-text-muted-color);
}
</style>