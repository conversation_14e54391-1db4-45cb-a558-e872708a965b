DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=blu

# JWT Configuration
JWT_SECRET=blu_collar_app

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<Your Email>
SMTP_PASS=<Your APP ID>

# Twilio Configuration
TWILIO_ACCOUNT_SID=<TWILIO_ACCOUNT_SID>
TWILIO_AUTH_TOKEN=<TWILIO_AUTH_TOKEN>
# TWILIO_PHONE_NUMBER=+************
TWILIO_VERIFY_SERVICE_SID=<TWILIO_VERIFY_SERVICE_SID>

# Application Configuration
NODE_ENV=development