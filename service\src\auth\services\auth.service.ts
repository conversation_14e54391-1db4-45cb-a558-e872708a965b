import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../../users/entities/user.entity';
import { ProfileEntity } from '../../users/entities/profile.entity';
import { UserRole } from '../../users/enums/user.enum';
import { UsersService } from '../../users/services/users.service';
import { LoginResponseDto } from '../dto/login-response.dto';
import { CompanyEntity } from '../../companies/entities/company.entity';

interface AuthHeaders {
  jdu?: string;
  usertype?: string;
  deviceId?: string;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(ProfileEntity)
    private readonly profileRepository: Repository<ProfileEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepository: Repository<CompanyEntity>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
  ) {}

  async createUser(userData: Partial<UserEntity>): Promise<UserEntity> {
    const user = this.userRepository.create(userData);
    await this.userRepository.save(user);

    // Create default profile
    const profile = this.profileRepository.create({
      userId: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      phoneNumber: user.phone,
      isComplete: false,
    });
    await this.profileRepository.save(profile);

    // If user is an employer, create a default company record
    if (user.role === UserRole.EMPLOYER) {
      const company = this.companyRepository.create({
        userId: user.id,
        name: `${user.firstName}'s Company`, // Default company name
        isActive: true,
        // Other fields will be updated later when the employer completes their company profile
      });
      await this.companyRepository.save(company);
    }

    return user;
  }

  async login(user: UserEntity, usertype?: string): Promise<LoginResponseDto> {
    if (user.isBlocked) {
      throw new UnauthorizedException('User account is blocked');
    }

    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      usertype,
      iat: Math.floor(Date.now() / 1000),
    };

    const [access_token, refresh_token] = await Promise.all([
      this.generateAccessToken(payload),
      this.generateRefreshToken(payload),
    ]);

    // Fetch user with profile and company
    const userWithRelations = await this.userRepository.findOne({
      where: { id: user.id },
      relations: ['profile', 'company'],
    });

    const response: LoginResponseDto = {
      access_token,
      refresh_token,
      user: {
        id: userWithRelations.id,
        email: userWithRelations.email,
        phone: userWithRelations.phone,
        role: userWithRelations.role,
        isBlocked: userWithRelations.isBlocked,
        isProfileComplete: userWithRelations.isProfileComplete,
        profile: userWithRelations.profile,
        firstName: userWithRelations.firstName,
        lastName: userWithRelations.lastName,
        isEmailVerified: userWithRelations.isEmailVerified,
        isPhoneVerified: userWithRelations.isPhoneVerified,
        isAadharVerified: userWithRelations.isAadharVerified,
        company: userWithRelations.company,
      },
    };

    return response;
  }

  async refreshToken(refresh_token: string, headers?: AuthHeaders) {
    try {
      const payload = await this.jwtService.verifyAsync(refresh_token, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      });

      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
        select: ['id', 'email', 'role', 'isBlocked'],
        relations: ['profile'],
      });

      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      if (user.isBlocked) {
        throw new UnauthorizedException('User account is blocked');
      }

      const newPayload = {
        sub: user.id,
        email: user.email,
        role: user.role,
        usertype: headers?.usertype,
        iat: Math.floor(Date.now() / 1000),
      };

      const [newAccessToken, newRefreshToken] = await Promise.all([
        this.generateAccessToken(newPayload),
        this.generateRefreshToken(newPayload),
      ]);

      return {
        access_token: newAccessToken,
        refresh_token: newRefreshToken,
        user: {
          id: user.id,
          email: user.email,
          phone: user.phone,
          role: user.role,
          profile: user.profile,
        },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(headers?: AuthHeaders) {
    // Here you can implement any cleanup logic based on headers
    // For example, invalidating tokens for specific device
    return { message: 'Logout successful' };
  }

  private async generateAccessToken(payload: any): Promise<string> {
    return this.jwtService.signAsync(payload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: '15m',
    });
  }

  private async generateRefreshToken(payload: any): Promise<string> {
    return this.jwtService.signAsync(payload, {
      secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      expiresIn: '7d',
    });
  }

  private async generateTokens(user: any) {
    const payload = {
      sub: user.id,
      email: user.email,
      isEmployer: user.isEmployer,
    };

    const [access_token, refresh_token] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: '15m',
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        expiresIn: '7d',
      }),
    ]);

    return {
      access_token,
      refresh_token,
    };
  }

  async handleGoogleAuth(googleUser: any, options?: { isEmployer?: boolean }) {
    try {
      console.log('Auth Service - Google user:', googleUser);
      console.log('Auth Service - Options:', options);

      // Check if user exists
      let user = await this.userRepository.findOne({
        where: { email: googleUser.email },
      });

      if (!user) {
        // Determine role based on isEmployer flag
        const isEmployer = options?.isEmployer === true || googleUser.isEmployer === true;
        console.log('Auth Service - Is Employer:', isEmployer);

        // Set role based on isEmployer flag
        const role = isEmployer ? UserRole.EMPLOYER : UserRole.JOB_SEEKER;
        console.log('Auth Service - Setting role as:', role);

        // Create new user with determined role
        user = await this.usersService.create(
          {
            email: googleUser.email,
            firstName: googleUser.firstName || '',
            lastName: googleUser.lastName || '',
            isEmailVerified: true,
            role: role, // Set the role here
            isProfileComplete: false,
            isPhoneVerified: false,
            isAadharVerified: false,
            isBlocked: false,
          },
          null, // No admin ID for Google auth
        );

        console.log('Auth Service - Created new user with role:', user.role);
      } else {
        // For existing users, we could update the role if needed
        console.log('Auth Service - Found existing user with role:', user.role);

        // Uncomment if you want to update existing users' roles
        // if (options?.isEmployer === true && user.role !== UserRole.EMPLOYER) {
        //   user.role = UserRole.EMPLOYER;
        //   await this.userRepository.save(user);
        //   console.log('Auth Service - Updated existing user role to EMPLOYER');
        // }
      }

      // Generate tokens and return
      const tokens = await this.generateTokens(user);
      return {
        ...tokens,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName,
        },
      };
    } catch (error) {
      console.error('Auth Service - Google auth error:', error);
      throw new UnauthorizedException('Failed to authenticate with Google');
    }
  }
}
