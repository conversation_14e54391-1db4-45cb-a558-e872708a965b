<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useApplicationsStore } from '@/stores/applications'
import Button from 'primevue/button'
import Select from 'primevue/select'
import ApplicantsDataTable from './ApplicantsDataTable.vue'
import Dropdown from 'primevue/dropdown'
import Tag from 'primevue/tag'
import { applicationsService } from '@/api/services/applicationsService'

const router = useRouter()
const { t } = useI18n()
const applicationsStore = useApplicationsStore()

const activeFilter = ref('all')
const selectedJob = ref(null)
const sortField = ref('createdAt')
const sortOrder = ref(-1)

const statusOptions = [
  { label: 'Pending', value: 'PENDING' },
  { label: 'Reviewing', value: 'REVIEWING' },
  { label: 'Shortlisted', value: 'SHORTLISTED' },
  { label: 'Interview Scheduled', value: 'INTERVIEW_SCHEDULED' },
  { label: 'Offered', value: 'OFFERED' },
  { label: 'Rejected', value: 'REJECTED' },
  { label: 'Withdrawn', value: 'WITHDRAWN' }
]
const statusUpdating = ref(false)

// Filter options for the dropdown
const filterOptions = computed(() => [
  { label: t('applicants.allApplicants', { count: stats.value.total }), value: 'all' },
  { label: t('applicants.pending', { count: stats.value.pending }), value: 'pending' },
  { label: t('applicants.interview', { count: stats.value.interview }), value: 'interview' },
  { label: t('applicants.offers', { count: stats.value.offer }), value: 'offer' },
  { label: t('applicants.rejected', { count: stats.value.rejected }), value: 'rejected' },
  { label: t('applicants.withdrawn', { count: stats.value.withdrawn }), value: 'withdrawn' }
])

// Computed properties from store
const applications = computed(() => applicationsStore.filteredApplications || [])
const isLoading = computed(() => applicationsStore.isLoading)
const error = computed(() => applicationsStore.error)
const stats = computed(() => applicationsStore.stats || {
  total: 0,
  pending: 0,
  reviewing: 0,
  interview: 0,
  offer: 0,
  rejected: 0,
  withdrawn: 0
})
const pagination = computed(() => applicationsStore.pagination)

// Get unique jobs from applications
const jobs = computed(() => {
  const jobMap = new Map()
  applicationsStore.applications.forEach(app => {
    if (app.job && !jobMap.has(app.job.id)) {
      jobMap.set(app.job.id, app.job)
    }
  })
  return Array.from(jobMap.values())
})

// Filtered applications by status
const pendingApplications = computed(() => applicationsStore.pendingApplications || [])
const interviewApplications = computed(() => applicationsStore.interviewApplications || [])
const offerApplications = computed(() => applicationsStore.offerApplications || [])
const rejectedApplications = computed(() => applicationsStore.rejectedApplications || [])

// Watch for filter changes and update store
watch(activeFilter, (newFilter) => {
  applicationsStore.setFilters({ status: newFilter })
})

// Watch for job selection changes
watch(selectedJob, (newJob) => {
  if (newJob) {
    applicationsStore.setFilters({ jobId: newJob.id })
    // Set default filter to 'all' when job is selected
    activeFilter.value = 'all'
  } else {
    applicationsStore.setFilters({ jobId: null })
    // Reset filter when no job is selected
    activeFilter.value = 'all'
  }
  // Reload applications when job selection changes
  loadApplications()
})

const getEmptyStateTitle = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applicants.noPendingApplicants')
    case 'interview':
      return t('applicants.noInterviewApplicants')
    case 'offer':
      return t('applicants.noOfferApplicants')
    case 'rejected':
      return t('applicants.noRejectedApplicants')
    case 'withdrawn':
      return t('applicants.noWithdrawnApplicants')
    default:
      return t('applicants.noApplicantsYet')
  }
}

const getEmptyStateMessage = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applicants.allApplicantsReviewed')
    case 'interview':
      return t('applicants.noInterviewsScheduled')
    case 'offer':
      return t('applicants.noOffersMade')
    case 'rejected':
      return t('applicants.noRejections')
    case 'withdrawn':
      return t('applicants.noWithdrawals')
    default:
      return t('applicants.noApplicantsForJob')
  }
}

const loadApplications = async () => {
  try {
    await applicationsStore.fetchJobApplicants()
  } catch (error) {
    console.error('Failed to load applications:', error)
  }
}

// Data table event handlers
const onPageChange = async (event) => {
  applicationsStore.setPage(event.page)
  applicationsStore.setPageSize(event.limit)
  await loadApplications()
}

const onSortChange = async (event) => {
  sortField.value = event.sortField
  sortOrder.value = event.sortOrder
  applicationsStore.setFilters({ 
    sortBy: event.sortField, 
    sortOrder: event.sortOrder === 1 ? 'asc' : 'desc' 
  })
  await loadApplications()
}

const viewApplicantProfile = async (application) => {
  // Navigate to the applicant details page instead of opening modal
  router.push(`/employer/applicant-details/${application.applicant.id}`)
}



const updateApplicationStatus = async (event) => {
  try {
    await applicationsService.updateStatus(event.applicationId, event.status)
    await loadApplications() // refresh table
  } catch (error) {
    console.error('Failed to update application status:', error)
  }
}

const viewDetails = (application) => {
  console.log('View application details for:', application.id)
  // TODO: Implement application details modal or page
}

const downloadResume = (resumeUrl) => {
  console.log('Download resume:', resumeUrl)
  // TODO: Implement resume download
  window.open(resumeUrl, '_blank')
}

onMounted(async () => {
  await loadApplications()
})
</script>

<template>
  <div class="applicants-page flex-1 overflow-x-hidden overflow-y-auto">
    <div class="applicants-content">
              <!-- Job and Filter Selectors -->
        <div class="selectors-section">
          <div class="section-header">
            <h2>{{ t('applicants.selectJob') }}</h2>
            <p>{{ t('applicants.selectJobDesc') }}</p>
          </div>
          <div class="selectors-container">
            <div class="job-selector">
              <label class="selector-label">{{ t('applicants.jobLabel') }}</label>
              <Select
                v-model="selectedJob"
                :options="jobs"
                optionLabel="title"
                placeholder="Select a job to view applicants"
                class="job-dropdown"
                :showClear="true"
                :loading="isLoading"
              />
            </div>
            <div class="filter-selector">
              <label class="selector-label">{{ t('applicants.filterLabel') }}</label>
              <Select
                v-model="activeFilter"
                :options="filterOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="Select filter"
                class="filter-dropdown"
                :disabled="!selectedJob"
                :loading="isLoading"
              />
            </div>
          </div>
        </div>

      <!-- Applicants Data Table -->
      <div v-if="selectedJob && applications.length > 0" class="data-table-section">
        <ApplicantsDataTable
          :applications="applications"
          :isLoading="isLoading"
          :pagination="pagination"
          :sortField="sortField"
          :sortOrder="sortOrder"
          @page-change="onPageChange"
          @sort-change="onSortChange"
          @view-profile="viewApplicantProfile"
          @update-status="updateApplicationStatus"
          @view-details="viewDetails"
          @download-resume="downloadResume"
        />
      </div>

      <!-- Empty State - No Job Selected -->
      <div v-else-if="!selectedJob && jobs.length > 0" class="empty-state">
        <i class="pi pi-briefcase empty-icon"></i>
        <h3>{{ t('applicants.selectJobFirst') }}</h3>
        <p>{{ t('applicants.selectJobFirstDesc') }}</p>
      </div>

      <!-- Empty State - No Jobs Available -->
      <div v-else-if="!isLoading && jobs.length === 0" class="empty-state">
        <i class="pi pi-briefcase empty-icon"></i>
        <h3>No Available Candidates</h3>
        <p>No candidates have applied to your posted jobs yet.</p>
      </div>

      <!-- Empty State - No Applicants -->
      <div v-else-if="!isLoading && applications.length === 0" class="empty-state">
        <i class="pi pi-users empty-icon"></i>
        <h3>{{ getEmptyStateTitle() }}</h3>
        <p>{{ getEmptyStateMessage() }}</p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <i class="pi pi-spin pi-spinner loading-icon"></i>
        <p>{{ t('applicants.loadingApplicants') }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.applicants-page {
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.applicants-content {
  margin: 0 auto;
  padding: 1rem;
}

.selectors-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.section-header p {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 0.9rem;
}

.selectors-container {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
  align-items: flex-end;
}

.job-selector,
.filter-selector {
  flex: 1;
  min-width: 0;
}

.selector-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.job-dropdown,
.filter-dropdown {
  width: 100%;
}





.data-table-section {
  margin-top: 2rem;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon,
.loading-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .applicants-content {
    max-width: 100vw;
    padding: 1rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .selectors-container {
    flex-direction: column;
    gap: 1rem;
  }

  .job-selector,
  .filter-selector {
    flex: none;
    width: 100%;
  }
}


</style> 