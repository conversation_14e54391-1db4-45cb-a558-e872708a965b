import httpClient from '../httpClient'

const BASE_URL = '/welcome'

export const fetchWelcomeList = (filters = {}) => {
  // If your backend uses POST /welcome/search for filtering, use that. Otherwise, use GET /welcome with query params.
  if (Object.keys(filters).length > 0) {
    return httpClient.post(`${BASE_URL}/search`, filters)
  } else {
    return httpClient.get(BASE_URL)
  }
}

export const createWelcome = (data) => {
  return httpClient.post(BASE_URL, data)
}

export const updateWelcome = (id, data) => {
  return httpClient.patch(`${BASE_URL}/${id}`, data)
}

export const deleteWelcome = (id) => {
  return httpClient.delete(`${BASE_URL}/${id}`)
} 