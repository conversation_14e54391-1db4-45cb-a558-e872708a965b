import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'
import { shouldUseMock } from '../config'
import { jobsListMock } from '../mocks/jobsListMock'

// Mock data transformation helper
const transformMockJobData = (mockJob) => {
  return {
    id: mockJob.id,
    title: mockJob.title,
    company: mockJob.employer?.firstName && mockJob.employer?.lastName 
      ? `${mockJob.employer.firstName} ${mockJob.employer.lastName}`.trim()
      : mockJob.employer?.email?.split('@')[0] || 'Unknown Company',
    description: mockJob.description,
    location: mockJob.location,
    salary: parseFloat(mockJob.salary),
    salaryMin: parseFloat(mockJob.salary) * 0.9, // Estimate range
    salaryMax: parseFloat(mockJob.salary) * 1.1,
    currency: 'USD',
    jobType: mockJob.jobType,
    jobTypeName: formatJobType(mockJob.jobType),
    paymentType: mockJob.paymentType,
    experienceLevel: mockJob.experienceLevel,
    experienceLevelName: formatExperienceLevel(mockJob.experienceLevel),
    urgency: mockJob.urgency,
    status: mockJob.status?.toLowerCase() || 'active',
    statusName: formatStatus(mockJob.status),
    benefits: mockJob.benefits || [],
    requirements: mockJob.requirements || [],
    responsibilities: mockJob.responsibilities || [],
    skills: mockJob.skills || [],
    vacancies: mockJob.vacancies || 1,
    workingHours: mockJob.workingHours,
    accommodation: mockJob.accommodation,
    transportation: mockJob.transportation,
    foodProvided: mockJob.foodProvided,
    safetyEquipment: mockJob.safetyEquipment,
    trainingProvided: mockJob.trainingProvided,
    industry: mockJob.industry?.name || 'General',
    department: mockJob.industry?.name?.toLowerCase().replace(/\s+/g, '-') || 'general',
    departmentName: mockJob.industry?.name || 'General',
    postedDate: mockJob.createdAt,
    updatedDate: mockJob.updatedAt,
    closingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    applicationsCount: Math.floor(Math.random() * 50) + 1,
    viewsCount: Math.floor(Math.random() * 200) + 10,
    isRemote: mockJob.location?.toLowerCase().includes('remote') || false,
    isFeatured: Math.random() < 0.1,
    isUrgent: mockJob.urgency === 'URGENT',
    color: getRandomColor(),
    thumbnail: mockJob.thumbnail || null,
    images: mockJob.images || []
  }
}

// Helper functions for formatting
const formatJobType = (type) => {
  const typeMap = {
    'FULL_TIME': 'Full-time',
    'PART_TIME': 'Part-time',
    'CONTRACT': 'Contract',
    'TEMPORARY': 'Temporary',
    'FREELANCE': 'Freelance'
  }
  return typeMap[type] || type
}

const formatExperienceLevel = (level) => {
  const levelMap = {
    'FRESHER': 'Entry Level',
    'EXPERIENCED': 'Experienced',
    'EXPERT': 'Expert Level',
    'SENIOR': 'Senior Level'
  }
  return levelMap[level] || level
}

const formatStatus = (status) => {
  const statusMap = {
    'ACTIVE': 'Active',
    'DRAFT': 'Draft',
    'CLOSED': 'Closed',
    'PAUSED': 'On Hold'
  }
  return statusMap[status] || status
}

const getRandomColor = () => {
  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
    '#8b5cf6', '#06b6d4', '#ec4899', '#84cc16'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

export const jobsService = {
  // Get all jobs with filters and pagination
  async getAll(params = {}) {
    // Real API call
    try {
      return await httpClient.get(ENDPOINTS.JOBS.LIST, { params })
      
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch jobs')
    }
  },

  // Get jobs posted by the current employer
  async getMyJobs(params = {}) {
    try {
      return await httpClient.get(ENDPOINTS.JOBS.MY_JOBS, { params })
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch my jobs')
    }
  },

  // Get job by ID
  async getById(id) {
    if (shouldUseMock('jobs')) {
      try {
        const mockResponse = await jobsListMock.getJobs()
        
        if (mockResponse.success) {
          const job = mockResponse.data.items.find(item => item.id === id)
          
          if (!job) {
            throw new Error('Job not found')
          }
          
          return {
            success: true,
            data: transformMockJobData(job)
          }
        } else {
          throw new Error('Job not found')
        }
      } catch (error) {
        throw new Error(error.message || 'Job not found')
      }
    }

    // Real API call
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.DETAILS(id))
      return {
        success: true,
        data: response.job || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Job not found')
    }
  },

  // Create new job (for employers)
  async create(jobData) {
    if (shouldUseMock('jobs')) {
      // For mock, we'll simulate creation
      const newJob = {
        id: Date.now().toString(),
        ...jobData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'ACTIVE'
      }
      
      return {
        success: true,
        data: transformMockJobData(newJob),
        message: 'Job created successfully'
      }
    }

    // Real API call
    try {
      const response = await httpClient.post(ENDPOINTS.JOBS.CREATE, jobData)
      return {
        success: true,
        data: response.job || response.data,
        message: response.message || 'Job created successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to create job')
    }
  },

  // Update job
  async update(id, jobData) {
    if (shouldUseMock('jobs')) {
      // For mock, simulate update
      return {
        success: true,
        data: { id, ...jobData, updatedAt: new Date().toISOString() },
        message: 'Job updated successfully'
      }
    }

    // Real API call
    try {
      const response = await httpClient.patch(ENDPOINTS.JOBS.UPDATE(id), jobData)
      return {
        success: true,
        data: response.job || response.data,
        message: response.message || 'Job updated successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to update job')
    }
  },

  // Delete job
  async delete(id) {
    if (shouldUseMock('jobs')) {
      // For mock, simulate deletion
      return {
        success: true,
        message: 'Job deleted successfully'
      }
    }

    // Real API call
    try {
      const response = await httpClient.delete(ENDPOINTS.JOBS.DELETE(id))
      return {
        success: true,
        message: response.message || 'Job deleted successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to delete job')
    }
  },

  // Get job statistics
  async getStats() {
    if (shouldUseMock('jobs')) {
      try {
        const mockResponse = await jobsListMock.getJobs()
        
        if (mockResponse.success) {
          const jobs = mockResponse.data.items
          
          return {
            success: true,
            data: {
              total: jobs.length,
              active: jobs.filter(j => j.status === 'ACTIVE').length,
              draft: jobs.filter(j => j.status === 'DRAFT').length,
              closed: jobs.filter(j => j.status === 'CLOSED').length,
              totalApplications: jobs.length * 15, // Mock calculation
              avgApplicationsPerJob: 15,
              departmentBreakdown: [
                { department: 'Restaurant', count: jobs.filter(j => j.industry?.name === 'Restaurent').length }
              ]
            }
          }
        }
      } catch (error) {
        console.error('Mock stats error:', error)
      }
    }

    // Real API call
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.STATS)
      return {
        success: true,
        data: response.stats || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch job statistics')
    }
  },

  // Search jobs
  async search(query, filters = {}) {
    // Use the same getAll method with search parameters
    return this.getAll({ search: query, ...filters })
  },

  // Get featured jobs
  async getFeatured(limit = 10) {
    if (shouldUseMock('jobs')) {
      try {
        const allJobsResponse = await this.getAll({ limit: 100 })
        
        if (allJobsResponse.success) {
          const featuredJobs = allJobsResponse.data
            .filter(job => job.isFeatured)
            .slice(0, limit)
          
          return {
            success: true,
            data: featuredJobs,
            message: 'Featured jobs retrieved successfully'
          }
        }
      } catch (error) {
        console.error('Mock featured jobs error:', error)
      }
    }

    // Real API call
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.FEATURED, { 
        params: { limit } 
      })

      return {
        success: true,
        data: response.jobs || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch featured jobs')
    }
  },

  // Get recent jobs
  async getRecent(limit = 10) {
    if (shouldUseMock('jobs')) {
      try {
        const allJobsResponse = await this.getAll({ limit: 100 })
        
        if (allJobsResponse.success) {
          const recentJobs = allJobsResponse.data
            .sort((a, b) => new Date(b.postedDate) - new Date(a.postedDate))
            .slice(0, limit)
          
          return {
            success: true,
            data: recentJobs,
            message: 'Recent jobs retrieved successfully'
          }
        }
      } catch (error) {
        console.error('Mock recent jobs error:', error)
      }
    }

    // Real API call
    try {
      const response = await httpClient.get(ENDPOINTS.JOBS.RECENT, { 
        params: { limit } 
      })
      return {
        success: true,
        data: response.jobs || response.data || [],
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch recent jobs')
    }
  }
}